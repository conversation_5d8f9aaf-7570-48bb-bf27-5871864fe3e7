.footer {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  margin-top: 80px;
  padding: 50px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: white;
}

.footer-section h4 {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: #e0e0e0;
}

.footer-section p {
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 15px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 8px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.footer-section li:hover {
  opacity: 1;
}

.footer-section a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: #81c784;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-bottom p {
  margin: 0;
  opacity: 0.8;
}

.footer-links {
  display: flex;
  gap: 20px;
  align-items: center;
}

.footer-links span {
  opacity: 0.8;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .footer-section h3 {
    font-size: 1.3rem;
  }
  
  .footer-section h4 {
    font-size: 1rem;
  }
}
