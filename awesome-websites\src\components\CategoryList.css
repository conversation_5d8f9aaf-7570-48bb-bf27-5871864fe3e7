.category-list {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.category-list-title {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.category-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-button {
  background: none;
  border: none;
  padding: 12px 16px;
  text-align: left;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  color: #666;
  transition: all 0.2s ease;
  position: relative;
}

.category-button:hover {
  background-color: #f8f9fa;
  color: #333;
  transform: translateX(4px);
}

.category-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.category-button.active:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

@media (max-width: 768px) {
  .category-list {
    position: static;
    margin-bottom: 20px;
  }
  
  .category-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
  }
  
  .category-button {
    text-align: center;
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  .category-button:hover {
    transform: none;
  }
  
  .category-button.active:hover {
    transform: none;
  }
}
