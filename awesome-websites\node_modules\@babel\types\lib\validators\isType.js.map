{"version": 3, "names": ["_index", "require", "isType", "nodeType", "targetType", "ALIAS_KEYS", "aliases", "FLIPPED_ALIAS_KEYS", "includes"], "sources": ["../../src/validators/isType.ts"], "sourcesContent": ["import { FLIPPED_ALIAS_KEYS, ALIAS_KEYS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function isType<T extends t.Node[\"type\"]>(\n  nodeType: string,\n  targetType: T,\n): nodeType is T;\n\nexport default function isType(\n  nodeType: string | null | undefined,\n  targetType: string,\n): boolean;\n\n/**\n * Test if a `nodeType` is a `targetType` or if `targetType` is an alias of `nodeType`.\n */\nexport default function isType(nodeType: string, targetType: string): boolean {\n  if (nodeType === targetType) return true;\n\n  // If nodeType is nullish, it can't be an alias of targetType.\n  if (nodeType == null) return false;\n\n  // This is a fast-path. If the test above failed, but an alias key is found, then the\n  // targetType was a primary node type, so there's no need to check the aliases.\n  // @ts-expect-error targetType may not index ALIAS_KEYS\n  if (ALIAS_KEYS[targetType]) return false;\n\n  const aliases: Array<string> | undefined = FLIPPED_ALIAS_KEYS[targetType];\n  if (aliases?.includes(nodeType)) return true;\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAgBe,SAASC,MAAMA,CAACC,QAAgB,EAAEC,UAAkB,EAAW;EAC5E,IAAID,QAAQ,KAAKC,UAAU,EAAE,OAAO,IAAI;EAGxC,IAAID,QAAQ,IAAI,IAAI,EAAE,OAAO,KAAK;EAKlC,IAAIE,iBAAU,CAACD,UAAU,CAAC,EAAE,OAAO,KAAK;EAExC,MAAME,OAAkC,GAAGC,yBAAkB,CAACH,UAAU,CAAC;EACzE,IAAIE,OAAO,YAAPA,OAAO,CAAEE,QAAQ,CAACL,QAAQ,CAAC,EAAE,OAAO,IAAI;EAE5C,OAAO,KAAK;AACd", "ignoreList": []}