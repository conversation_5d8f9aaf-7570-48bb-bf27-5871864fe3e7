.website-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: fit-content;
  display: flex;
  flex-direction: column;
  opacity: 0;
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.website-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.website-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.website-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.website-favicon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  flex-shrink: 0;
}

.website-name {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.website-badges {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
}

.badge.paid {
  background-color: #ff6b6b;
}

.badge.student {
  background-color: #4ecdc4;
}

.website-description {
  color: #666;
  line-height: 1.6;
  margin: 0 0 20px 0;
  flex: 1;
}

.website-footer {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.website-domain {
  font-size: 0.8rem;
  color: #999;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  flex-shrink: 0;
}

.website-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.website-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.external-link-icon {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

@media (max-width: 768px) {
  .website-card {
    padding: 16px;
  }
  
  .website-name {
    font-size: 1.1rem;
  }
  
  .website-link {
    padding: 8px 14px;
    font-size: 0.85rem;
  }
}
