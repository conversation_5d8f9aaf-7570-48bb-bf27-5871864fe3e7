.website-grid {
  min-height: 500px;
}

.category-section {
  margin-bottom: 50px;
}

.category-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0 0 25px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  border-bottom: 3px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 10px;
}

.subcategory-section {
  margin-bottom: 35px;
}

.subcategory-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: white;
  margin: 0 0 20px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

.websites-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .category-title {
    font-size: 1.6rem;
  }
  
  .subcategory-title {
    font-size: 1.2rem;
  }
  
  .websites-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .websites-container {
    grid-template-columns: 1fr;
  }
}
