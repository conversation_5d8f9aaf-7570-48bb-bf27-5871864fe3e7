.website-grid {
  min-height: 500px;
}

.grid-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 30px;
}

.toggle-all-button {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 10px 20px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-all-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.category-section {
  margin-bottom: 50px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  margin-bottom: 25px;
  user-select: none;
}

.category-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  border-bottom: 3px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.website-count {
  font-size: 1.2rem;
  opacity: 0.8;
  font-weight: 500;
}

.expand-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.expand-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.expand-button svg {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.expand-button.expanded svg {
  transform: rotate(180deg);
}

.category-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subcategory-section {
  margin-bottom: 35px;
}

.subcategory-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: white;
  margin: 0 0 20px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

.websites-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .category-header {
    margin-bottom: 20px;
  }

  .category-title {
    font-size: 1.6rem;
  }

  .website-count {
    font-size: 1rem;
  }

  .expand-button {
    width: 35px;
    height: 35px;
  }

  .expand-button svg {
    width: 18px;
    height: 18px;
  }

  .subcategory-title {
    font-size: 1.2rem;
  }

  .websites-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .websites-container {
    grid-template-columns: 1fr;
  }
}
