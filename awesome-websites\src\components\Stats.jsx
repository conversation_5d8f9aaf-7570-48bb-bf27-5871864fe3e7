import './Stats.css'

function Stats({ data, searchTerm, selectedCategory }) {
  // 计算总网站数量
  const getTotalWebsites = () => {
    return data.categories.reduce((total, category) => {
      const categoryWebsites = category.websites.length
      const subcategoryWebsites = category.subcategories.reduce((subTotal, sub) => {
        return subTotal + sub.websites.length
      }, 0)
      return total + categoryWebsites + subcategoryWebsites
    }, 0)
  }

  // 计算分类数量
  const getTotalCategories = () => {
    return data.categories.length
  }

  // 计算子分类数量
  const getTotalSubcategories = () => {
    return data.categories.reduce((total, category) => {
      return total + category.subcategories.length
    }, 0)
  }

  const totalWebsites = getTotalWebsites()
  const totalCategories = getTotalCategories()
  const totalSubcategories = getTotalSubcategories()

  return (
    <div className="stats">
      <div className="stats-container">
        <div className="stat-item">
          <div className="stat-number">{totalWebsites}</div>
          <div className="stat-label">精选网站</div>
        </div>
        <div className="stat-item">
          <div className="stat-number">{totalCategories}</div>
          <div className="stat-label">主要分类</div>
        </div>
        <div className="stat-item">
          <div className="stat-number">{totalSubcategories}</div>
          <div className="stat-label">子分类</div>
        </div>
        {searchTerm && (
          <div className="stat-item search-result">
            <div className="stat-number">{totalWebsites}</div>
            <div className="stat-label">搜索结果</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Stats
