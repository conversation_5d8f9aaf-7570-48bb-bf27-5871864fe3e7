import { useState, useMemo } from 'react'
import { websitesData } from './data/websitesData'
import Header from './components/Header'
import SearchBar from './components/SearchBar'
import CategoryList from './components/CategoryList'
import WebsiteGrid from './components/WebsiteGrid'
import './App.css'

function App() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState(null)

  // 过滤网站数据
  const filteredData = useMemo(() => {
    if (!searchTerm && !selectedCategory) {
      return websitesData
    }

    const filtered = {
      ...websitesData,
      categories: websitesData.categories.map(category => {
        // 如果选择了特定分类，只显示该分类
        if (selectedCategory && category.id !== selectedCategory) {
          return null
        }

        // 过滤网站
        const filterWebsites = (websites) => {
          return websites.filter(website =>
            website.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            website.description.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }

        const filteredCategory = {
          ...category,
          websites: filterWebsites(category.websites),
          subcategories: category.subcategories.map(sub => ({
            ...sub,
            websites: filterWebsites(sub.websites)
          })).filter(sub => sub.websites.length > 0)
        }

        // 只返回有网站的分类
        if (filteredCategory.websites.length > 0 || filteredCategory.subcategories.length > 0) {
          return filteredCategory
        }
        return null
      }).filter(Boolean)
    }

    return filtered
  }, [searchTerm, selectedCategory])

  return (
    <div className="app">
      <Header
        title={websitesData.title}
        description={websitesData.description}
      />

      <div className="container">
        <SearchBar
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />

        <div className="main-content">
          <CategoryList
            categories={websitesData.categories}
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
          />

          <WebsiteGrid
            data={filteredData}
            searchTerm={searchTerm}
          />
        </div>
      </div>
    </div>
  )
}

export default App
