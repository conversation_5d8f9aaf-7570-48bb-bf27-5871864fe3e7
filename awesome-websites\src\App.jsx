import { useState, useMemo, useEffect } from 'react'
import { websitesData } from './data/fullWebsitesData'
import Header from './components/Header'
import SearchBar from './components/SearchBar'
import Stats from './components/Stats'
import CategoryList from './components/CategoryList'
import WebsiteGrid from './components/WebsiteGrid'
import Footer from './components/Footer'
import BackToTop from './components/BackToTop'
import Loading from './components/Loading'
import './App.css'

function App() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [isLoading, setIsLoading] = useState(true)

  // 模拟加载过程
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000) // 1秒加载时间

    return () => clearTimeout(timer)
  }, [])

  // 过滤网站数据
  const filteredData = useMemo(() => {
    if (!searchTerm && !selectedCategory) {
      return websitesData
    }

    const filtered = {
      ...websitesData,
      categories: websitesData.categories.map(category => {
        // 如果选择了特定分类，只显示该分类
        if (selectedCategory && category.id !== selectedCategory) {
          return null
        }

        // 过滤网站
        const filterWebsites = (websites) => {
          return websites.filter(website =>
            website.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            website.description.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }

        const filteredCategory = {
          ...category,
          websites: filterWebsites(category.websites),
          subcategories: category.subcategories.map(sub => ({
            ...sub,
            websites: filterWebsites(sub.websites)
          })).filter(sub => sub.websites.length > 0)
        }

        // 只返回有网站的分类
        if (filteredCategory.websites.length > 0 || filteredCategory.subcategories.length > 0) {
          return filteredCategory
        }
        return null
      }).filter(Boolean)
    }

    return filtered
  }, [searchTerm, selectedCategory])

  if (isLoading) {
    return (
      <div className="app">
        <Header
          title={websitesData.title}
          description={websitesData.description}
        />
        <div className="container">
          <Loading />
        </div>
      </div>
    )
  }

  return (
    <div className="app">
      <Header
        title={websitesData.title}
        description={websitesData.description}
      />

      <div className="container">
        <SearchBar
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />

        <Stats
          data={filteredData}
          searchTerm={searchTerm}
          selectedCategory={selectedCategory}
        />

        <div className="main-content">
          <CategoryList
            categories={websitesData.categories}
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
          />

          <WebsiteGrid
            data={filteredData}
            searchTerm={searchTerm}
          />
        </div>
      </div>

      <Footer />
      <BackToTop />
    </div>
  )
}

export default App
