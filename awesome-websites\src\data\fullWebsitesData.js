// 从AD.md自动生成的完整网站数据
// 生成时间: 2025-06-05T13:40:14.203Z
// 总计: 1420个网站，30个主要分类，134个子分类

export const websitesData = {
  "title": "Awesome Useful Websites",
  "description": "探索互联网上的隐藏宝石，发现各种有用的在线工具和资源！",
  "categories": [
    {
      "name": "工具",
      "originalName": "Tools",
      "id": "tools",
      "subcategories": [
        {
          "name": "白板工具",
          "originalName": "White Board",
          "id": "white-board",
          "websites": [
            {
              "name": "TypeHere",
              "url": "https://typehere.co/",
              "description": "只能输入文字的空白网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PixelPaper",
              "url": "https://pixelpaper.io/",
              "description": "永久免费的数字白板，无需注册，可嵌入SaaS产品。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Excalidraw",
              "url": "https://excalidraw.com/",
              "description": "可以绘制手绘风格图表的白板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Excalideck",
              "url": "https://excalideck.com/",
              "description": "基于Excalidraw创建手绘风格幻灯片的应用程序。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Blank Page",
              "url": "https://blank.page/",
              "description": "显示空白白页的简单网页。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Kid Pix",
              "url": "https://kidpix.app/",
              "description": "为儿童设计的位图绘图程序，提供有趣、用户友好的界面用于创意和交互式数字艺术作品。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Krita",
              "url": "https://krita.org/en/",
              "description": "为艺术家设计的免费开源数字绘画软件，提供插图、概念艺术和纹理绘画的高级工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "思维导图笔记",
          "originalName": "Mind Map / Note Taking",
          "id": "mind-map---note-taking",
          "websites": [
            {
              "name": "Relanote",
              "url": "https://relanote.com/",
              "description": "将您的笔记相互链接，形成思维网络。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bubbl.us",
              "url": "https://bubbl.us/",
              "description": "在线思维导图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MindMup",
              "url": "https://www.mindmup.com/",
              "description": "免费在线思维导图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Anotepad",
              "url": "https://anotepad.com/",
              "description": "在线记事本。无需登录。可将笔记下载为PDF或Word文档。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Notes.io",
              "url": "https://notes.io/",
              "description": "基于Web的笔记应用程序。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "图表制作",
          "originalName": "Diagrams",
          "id": "diagrams",
          "websites": [
            {
              "name": "Creately",
              "url": "https://creately.com/",
              "description": "用于头脑风暴、规划、执行和获取知识的数据连接可视化工作空间。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "draw.io",
              "url": "https://www.drawio.com/",
              "description": "用于构建图表应用程序的开源、安全优先技术栈。可用于[网页使用](https://app.diagrams.net/?src=about)。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OrgPad",
              "url": "https://orgpad.com/?ref=producthunt",
              "description": "交互式在线思维导图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Lucidchart",
              "url": "https://www.lucidchart.com/pages/",
              "description": "一个图表和流程图应用程序，将团队聚集在一起做出更好的决策并构建未来。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learn Anything",
              "url": "https://learn-anything.xyz/",
              "description": "组织世界知识、探索连接并策划学习路径的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "文本处理",
          "originalName": "Texts",
          "id": "texts",
          "websites": [
            {
              "name": "Word Counter",
              "url": "https://freecodetools.org/word-counter/",
              "description": "字数统计工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Text Faces",
              "url": "https://textfac.es/",
              "description": "编写Unicode表情符号。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Title Case",
              "url": "https://titlecase.com/",
              "description": "将文本转换为各种大小写格式。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fancy Text Generator",
              "url": "https://lingojam.com/FancyTextGenerator",
              "description": "将文本转换为各种字体样式。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Calligraphr",
              "url": "https://www.calligraphr.com/en/",
              "description": "将您的手写字体或书法转换为字体文件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Dongerlist",
              "url": "https://dongerlist.com/",
              "description": "由Unicode字符组成的文本表情符号集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ASCII-art Tutorial",
              "url": "https://stonestoryrpg.com/ascii_tutorial.html",
              "description": "ASCII艺术教程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Emoji Combos",
              "url": "https://emojicombos.com/",
              "description": "表情符号组合和序列集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ASCII World",
              "url": "https://www.asciiworld.com/",
              "description": "以ASCII艺术和教程为特色的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Rentry",
              "url": "https://rentry.co/FMHY",
              "description": "在线协作markdown编辑器。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "浏览器自动化",
          "originalName": "Automating browser",
          "id": "automating-browser",
          "websites": [
            {
              "name": "Automa",
              "url": "https://chrome.google.com/webstore/detail/automa/infppggnoaenmfagbfknfkancpbljcca?ref=producthunt",
              "description": "用于自动化浏览器操作的Chrome扩展。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Browse.AI",
              "url": "https://www.browse.ai/",
              "description": "从任何网站提取、设置点击和监控数据的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Tango",
              "url": "https://www.tango.us/",
              "description": "用截图创建分步文档、操作手册和产品指南的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "BookmarkOS",
              "url": "https://bookmarkos.com/bookmark-manager-finder",
              "description": "允许您筛选各种书签管理器。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "比较工具",
          "originalName": "Comparison",
          "id": "comparison",
          "websites": [
            {
              "name": "Social Media Messaging Apps Comparison",
              "url": "https://docs.google.com/spreadsheets/d/1-UlA4-tslROBDS9IqHalWVztqZo7uxlCeKPQ-8uoFOU/edit#gid=0",
              "description": "社交媒体消息应用的详细比较和分析。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DxOMark",
              "url": "https://www.dxomark.com/category/smartphone-reviews/",
              "description": "对智能手机、传感器、镜头和扬声器进行科学测试和数据分析。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TechSpecs Compare Phones",
              "url": "https://techspecs.io/vs/",
              "description": "比较手机规格参数。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TechSpecs",
              "url": "https://techspecs.io/",
              "description": "消费电子产品的搜索引擎。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Kimovil",
              "url": "https://www.kimovil.com/en/",
              "description": "比较智能手机和平板电脑的规格和价格。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DiffChecker",
              "url": "https://www.diffchecker.com/",
              "description": "比较文本、图像、PDF等文件以找出差异。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodingFont",
              "url": "https://www.codingfont.com/",
              "description": "通过游戏化体验比较和寻找编程字体。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "This vs That",
              "url": "https://thisvsthat.io/",
              "description": "输入两个事物来比较它们。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Secure Messaging Apps Comparison",
              "url": "https://www.securemessagingapps.com/",
              "description": "安全消息应用的比较平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RTINGS",
              "url": "https://www.rtings.com/",
              "description": "提供音视频设备的深度评测和比较，包括电视、显示器、耳机和音响，含详细测试和评级。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "文件管理",
          "originalName": "File",
          "id": "file",
          "websites": [
            {
              "name": "Wormhole",
              "url": "https://wormhole.app/",
              "description": "通过端到端加密分享文件的平台，链接会自动过期。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Keybase",
              "url": "https://keybase.io/",
              "description": "端到端加密的安全消息和文件共享平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MediaFire",
              "url": "https://www.mediafire.com/",
              "description": "文件存储和共享平台（提供订阅选项）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Zippyshare",
              "url": "https://www.zippyshare.com/",
              "description": "无需注册、无下载限制、免费且无限磁盘空间的文件分享服务。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "格式转换",
          "originalName": "Converter / Conversion",
          "id": "converter---conversion",
          "websites": [
            {
              "name": "PDF2DOC",
              "url": "https://pdf2doc.com/",
              "description": "免费在线PDF转DOC转换器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Online-Convert",
              "url": "https://www.online-convert.com/",
              "description": "在线转换不同格式的媒体文件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JPG to PNG",
              "url": "https://jpg.to-png.com/",
              "description": "各种格式的免费文件转换工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Conversion-Tool",
              "url": "https://www.conversion-tool.com/",
              "description": "提供广泛的免费在线转换工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Zamzar",
              "url": "https://www.zamzar.com/",
              "description": "转换1100多种格式的文档、图像、视频和音频。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Web2PDFConvert",
              "url": "https://www.web2pdfconvert.com/",
              "description": "将网页或HTML转换为PDF或图像格式。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SmallPDF",
              "url": "https://smallpdf.com/",
              "description": "21种免费PDF转换、压缩和编辑工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Corrupt-a-File",
              "url": "https://corrupt-a-file.net/",
              "description": "在线损坏任何文件（使用风险自负）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CloudConvert",
              "url": "https://cloudconvert.com/",
              "description": "支持200多种格式的在线文件转换器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OnlineOCR",
              "url": "https://www.onlineocr.net/",
              "description": "带OCR支持的图片转文字转换器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PDF Candy",
              "url": "https://pdfcandy.com/",
              "description": "处理PDF文件的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PDFescape",
              "url": "https://www.pdfescape.com/",
              "description": "免费在线PDF编辑器和表单填写工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PrintIt",
              "url": "https://printit.work/about",
              "description": "将网页打印为PDF的服务，提供各种自定义选项。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "单位转换",
          "originalName": "Unit Conversion",
          "id": "unit-conversion",
          "websites": [
            {
              "name": "UnitConverters",
              "url": "https://www.unitconverters.net/",
              "description": "转换各种单位的在线平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OnlineConversion",
              "url": "https://onlineconversion.com/",
              "description": "几乎可以将任何东西转换为任何其他东西的工具，包含数千个单位和数百万种转换。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Text to Binary Conversion",
              "url": "https://www.online-toolz.com/tools/text-binary-convertor.php",
              "description": "文本转二进制的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CSSUnitConverter",
              "url": "https://cssunitconverter.com/",
              "description": "在PX、EM、REM、PT、英寸、厘米等单位之间转换，适用于网页和印刷设计。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "视觉工具",
          "originalName": "Visual",
          "id": "visual",
          "websites": [
            {
              "name": "Unscreen",
              "url": "https://www.unscreen.com/",
              "description": "自动免费移除视频背景。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Remove.bg",
              "url": "https://www.remove.bg/",
              "description": "自动免费移除图像背景。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Foco Clipping",
              "url": "https://www.fococlipping.com/",
              "description": "移除图像背景。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Designify",
              "url": "https://www.designify.com/",
              "description": "通过自动移除背景、增强颜色、调整智能阴影等功能创建AI驱动的设计。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "PfpMaker",
              "url": "https://pfpmaker.com/",
              "description": "从任何照片制作头像。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JPEG-Optimizer",
              "url": "https://jpeg-optimizer.com/",
              "description": "免费的在线数字照片和图像调整和压缩工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Extract.pics",
              "url": "https://extract.pics/",
              "description": "使用虚拟浏览器从任何公共网站提取图像。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Generated.Photos",
              "url": "https://generated.photos/",
              "description": "独特、无忧（AI生成）、免费下载的模特照片。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Zoom.it",
              "url": "https://zoom.it/",
              "description": "创建高分辨率、可缩放的图像。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "VectorMagic",
              "url": "https://vectormagic.com/",
              "description": "将位图（JPG、PNG、GIF）转换为矢量图（PDF、SVG、EPS）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Screenshot.Guru",
              "url": "https://screenshot.guru/",
              "description": "对网站和推文进行高分辨率屏幕截图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Stolen Camera Finder",
              "url": "https://www.stolencamerafinder.com/",
              "description": "使用照片中存储的序列号在网上搜索用同一台相机拍摄的其他照片。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ribbet",
              "url": "https://www.ribbet.com/",
              "description": "照片编辑工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Crossfade.io",
              "url": "https://crossfade.io/",
              "description": "从您喜爱的网站制作基于网络的视频混剪。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GoProHeroes",
              "url": "https://goproheroes.com/",
              "description": "网络上的GoPro视频。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Synthesia",
              "url": "https://www.synthesia.io/",
              "description": "在几分钟内从文本创建视频的AI视频创作平台。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "ClipDrop",
              "url": "https://clipdrop.co/",
              "description": "在AI驱动下几秒钟内创建令人惊叹的视觉效果。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Reface",
              "url": "https://hey.reface.ai/",
              "description": "创建换脸视频，由AI驱动的移动应用。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PhotoSonic",
              "url": "https://photosonic.writesonic.com/",
              "description": "用像素描绘您梦想的AI，DALL-E的另一个版本。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Shottr",
              "url": "https://shottr.cc/",
              "description": "小巧快速的macOS截图工具，具有注释、滚动截图和云上传功能。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "3D GIF Maker",
              "url": "https://www.3dgifmaker.com/",
              "description": "轻松从您的图像创建3D GIF。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "EZGIF",
              "url": "https://ezgif.com/",
              "description": "用于创建和编辑GIF的在线GIF制作器和图像编辑器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PimEyes",
              "url": "https://pimeyes.com/en",
              "description": "面部识别搜索引擎和反向图像搜索，用于查找包含特定人员的图像。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visual Illusions",
              "url": "https://sites.socsci.uci.edu/~ddhoff/illusions.html",
              "description": "视觉错觉和演示的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ByClickDownloader",
              "url": "https://www.byclickdownloader.com/",
              "description": "使用他们的软件以HD、MP3、MP4、AVI和其他格式备份各种网站的视频。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Reanimate",
              "url": "https://reanimate.github.io/",
              "description": "使用SVG和Haskell构建声明式动画。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "5000Best Tools",
          "url": "https://5000best.com/tools/",
          "description": "5000个工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "10015.io",
          "url": "https://10015.io/",
          "description": "免费的多合一工具箱，用于各种任务。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "UnTools",
          "url": "https://untools.co/",
          "description": "思维工具和框架集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "TimeTravel Memento",
          "url": "https://timetravel.mementoweb.org/",
          "description": "在Internet Archive、Archive-It、大英图书馆、archive.today、GitHub等地方查找备忘录。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "discu.eu",
          "url": "https://discu.eu/",
          "description": "通过每周新闻通讯、社交和机器人、浏览器扩展、书签工具跟上您关心的话题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "PromoWizard",
          "url": "https://promowizard.softr.app/",
          "description": "无需在YouTube上观看数小时内容即可获取促销代码。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Everybodywiki",
          "url": "https://en.everybodywiki.com/Everybodywiki:Welcome",
          "description": "从维基百科中拯救已删除的文章和被拒绝的草稿，支持多种语言，并欢迎新文章。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Lunar",
          "url": "https://lunar.fyi/",
          "description": "控制显示器的多功能应用程序。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "GetHuman",
          "url": "https://gethuman.com/",
          "description": "更快地与知名公司的代表通话并获得更好的帮助。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "S-ings Scratchpad",
          "url": "https://www.s-ings.com/scratchpad/",
          "description": "为快速笔记、计算和非正式写作设计的在线草稿本工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "UFreeTools",
          "url": "https://www.ufreetools.com/",
          "description": "您的在线免费工具包。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "动手制作",
      "originalName": "DIY",
      "id": "diy",
      "subcategories": [],
      "websites": [
        {
          "name": "WikiHow",
          "url": "https://www.wikihow.com/Main-Page",
          "description": "创建和分享操作指南的协作平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ManualsLib",
          "url": "https://www.manualslib.com/",
          "description": "用户手册和指南的在线存储库。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "This to That",
          "url": "https://thistothat.com/",
          "description": "学习如何将不同材料粘合在一起。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "HowStuffWorks",
          "url": "https://www.howstuffworks.com/",
          "description": "通过深入的解释和文章探索事物的工作原理。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "WonderHowTo",
          "url": "https://www.wonderhowto.com/",
          "description": "通过教学视频和分步指南学习几乎任何事情。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Dummies",
          "url": "https://www.dummies.com/",
          "description": "一系列教学/参考书籍。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "DoItYourself",
          "url": "https://www.doityourself.com/",
          "description": "DIY项目和家庭装修的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "JScreenFix",
          "url": "https://www.jscreenfix.com/",
          "description": "用于修复缺陷像素的像素修复算法，对卡住的像素特别有效。无需安装，且免费。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Donkey Car",
          "url": "https://www.donkeycar.com/",
          "description": "小型汽车的开源DIY自动驾驶平台。它将遥控车与树莓派结合，由Python（tornado、keras、tensorflow、opencv等）驱动。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Instructables",
          "url": "https://www.instructables.com/",
          "description": "发现和分享DIY项目的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "iFixit",
          "url": "https://www.ifixit.com/",
          "description": "为各种电子产品、家电和其他产品提供免费维修指南和手册，由社区贡献，赋予用户自己修理物品的能力。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Fix It Club",
          "url": "https://fixitclub.com/",
          "description": "通过有用的指南节省家庭维修费用。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BookCrossing",
          "url": "https://bookcrossing.com/",
          "description": "将您的书籍\"放归野外\"供陌生人发现，或对另一个BookCrossing成员进行\"受控释放\"，并通过来自世界各地的日记条目跟踪它们的旅程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Dimensions",
          "url": "https://www.dimensions.com/",
          "description": "为各个类别提供尺寸和测量视觉参考设计的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Repair Clinic",
          "url": "https://www.repairclinic.com/",
          "description": "北美历史最悠久的正品家电、暖通空调和户外动力设备零件来源，提供专家建议、操作指导资源和DIY维修支持。受到专业人士和房主信赖，该网站提供高质量的OEM零件和指导，帮助用户成功完成维修。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "文化",
      "originalName": "Culture",
      "id": "culture",
      "subcategories": [],
      "websites": [
        {
          "name": "Cultural Atlas",
          "url": "https://culturalatlas.sbs.com.au/",
          "description": "提供文化背景综合信息的教育资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "QuoteMaster",
          "url": "https://www.quotemaster.org/",
          "description": "拥有98,683个类别和1,488,431条引语的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "FactSlides",
          "url": "https://www.factslides.com/",
          "description": "提供1001个关于各种主题的事实，并附有来源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Starkey Comics",
          "url": "https://starkeycomics.com/",
          "description": "展示关于文化和语言的彩色图像和帖子的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Unusual Wikipedia Articles",
          "url": "https://en.wikipedia.org/wiki/Wikipedia:Unusual_articles",
          "description": "不寻常维基百科文章的汇编。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "List of Common Misconceptions",
          "url": "https://en.wikipedia.org/wiki/List_of_common_misconceptions",
          "description": "列出常见误解的维基百科页面。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Behind the Name",
          "url": "https://www.behindthename.com/",
          "description": "提供名字的词源和历史。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Behind the Surname",
          "url": "https://surnames.behindthename.com/",
          "description": "提供姓氏的词源和历史。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Nameberry",
          "url": "https://nameberry.com/",
          "description": "专家婴儿取名平台，包括流行名字、独特名字、女孩名字、男孩名字和性别中性名字。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Library of Juggling",
          "url": "https://libraryofjuggling.com/",
          "description": "将所有流行（以及可能不那么流行）的杂技技巧整理在一个有组织的地方的在线资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Toaster Central",
          "url": "https://toastercentral.com/",
          "description": "工作中的古董烤面包机收藏。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "All About Berlin",
          "url": "https://allaboutberlin.com/",
          "description": "为计划在柏林定居的个人提供指南和信息的平台。包括获得签证、找工作、租房等详细信息。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Unita",
          "url": "https://unita.co/",
          "description": "发现、比较和评论30多个类别中最佳社区、智囊团和在线群组的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Escape Room Tips",
          "url": "https://escaperoomtips.com/",
          "description": "密室逃脱的技巧、诀窍和谜题",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "语言学习",
      "originalName": "Language",
      "id": "language",
      "subcategories": [
        {
          "name": "语法",
          "originalName": "Grammar",
          "id": "grammar",
          "websites": [
            {
              "name": "GrammarBook",
              "url": "https://www.grammarbook.com/english_rules.asp",
              "description": "英语语法规则的综合指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "The Punctuation Guide",
              "url": "https://www.thepunctuationguide.com/index.html",
              "description": "提供美式标点符号规则指南的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Progressive Punctuation",
              "url": "https://progressivepunctuation.com/",
              "description": "非标准标点符号的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Purdue OWL",
              "url": "https://owl.purdue.edu/site_map.html",
              "description": "普渡大学在线写作实验室，提供写作、语法和引用格式的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Towson University Online Writing Support",
              "url": "https://webapps.towson.edu/ows/index.asp",
              "description": "在线写作支持和语法资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Grammar Monster",
              "url": "https://www.grammar-monster.com/index.html",
              "description": "提供免费英语语法课程和测试的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fraze It",
              "url": "https://fraze.it/",
              "description": "拥有超过1亿个句子的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "词汇释义",
          "originalName": "Words & Meanings",
          "id": "words---meanings",
          "websites": [
            {
              "name": "Educalingo",
              "url": "https://educalingo.com/en/dic-en",
              "description": "查找单词的同义词、用法、趋势、统计等信息的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fine Dictionary",
              "url": "https://www.finedictionary.com/",
              "description": "关于单词的综合信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Crown Academy English",
              "url": "https://www.crownacademyenglish.com/articles/",
              "description": "提供各种英语语言概念的简单、简洁和清晰解释。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ask Difference",
              "url": "https://www.askdifference.com/",
              "description": "提供两个相似主题之间简短而简洁的差异。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Key Differences",
              "url": "https://keydifferences.com/",
              "description": "专注于呈现差异和比较的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DifferenceBetween.info",
              "url": "https://www.differencebetween.info/",
              "description": "提供不同概念之间的描述性分析和比较。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wayne State University's List of Words",
              "url": "https://wordwarriors.wayne.edu/list",
              "description": "韦恩州立大学策划的值得更广泛使用的单词汇编。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "All Acronyms",
              "url": "https://www.allacronyms.com/",
              "description": "社区驱动的缩略词和简称词典。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "How to Professionally Say",
              "url": "https://howtoprofessionallysay.akashrajpurohit.com/",
              "description": "日常专业交流指南，帮助您以专业语调应对各种情况。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Business English Resources",
              "url": "https://www.businessenglishresources.com/",
              "description": "用于提高商务英语技能的免费资源集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Digital Glossary",
              "url": "https://www.digital-glossary.com/",
              "description": "为数字环境提供土耳其语、英语和德语术语词汇。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bilim Terimleri",
              "url": "https://terimler.org/",
              "description": "为各种术语提供解释和定义的土耳其语平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "YouGlish",
          "url": "https://youglish.com/",
          "description": "使用YouTube提高您的英语发音。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Voscreen",
          "url": "https://www.voscreen.com/",
          "description": "提供英语句子视频片段的平台；通过选择释义句子来测试您的理解能力。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "News in Levels",
          "url": "https://www.newsinlevels.com/",
          "description": "为英语学习者量身定制的世界新闻。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Pink Trombone",
          "url": "https://dood.al/pinktrombone/",
          "description": "使用动画语音盒对人类口腔及其声音的交互式模拟。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Japanese Wiki Corpus",
          "url": "https://www.japanese-wiki-corpus.org/",
          "description": "从维基百科京都文章的日英双语语料库生成的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Latin Phrases",
          "url": "https://latin-phrases.co.uk/",
          "description": "查找拉丁短语翻译的参考资料。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Prismatext",
          "url": "https://prismatext.com/",
          "description": "将最有用的外语单词和短语融入您最喜爱的小说和故事中。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ponly",
          "url": "https://ponly.com/about/",
          "description": "包含有趣幽默内容和笑话的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tongue-Twister",
          "url": "https://tongue-twister.net/",
          "description": "世界最大的绕口令集合，包含118种语言的3660个条目",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "OLAC (Open Language Archives Community)",
          "url": "http://olac.ldc.upenn.edu/",
          "description": "致力于创建语言资源全球虚拟图书馆的国际机构和个人网络，专注于数字档案实践并提供可互操作的语言数据访问存储库。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "旅行",
      "originalName": "Travel",
      "id": "travel",
      "subcategories": [
        {
          "name": "环球旅行",
          "originalName": "Globetrotting",
          "id": "globetrotting",
          "websites": [
            {
              "name": "Random Street View",
              "url": "https://randomstreetview.com/",
              "description": "足不出户探索世界各地的街道。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Virtual Vacation",
              "url": "https://virtualvacation.us/",
              "description": "在家中舒适地进行环球虚拟旅行。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MapCrunch",
              "url": "https://www.mapcrunch.com/",
              "description": "通过传送到随机位置，通过Google街景体验世界。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "时间工具",
          "originalName": "Time",
          "id": "time",
          "websites": [
            {
              "name": "Every Time Zone",
              "url": "https://everytimezone.com/",
              "description": "不同国家的可视化时区比较。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Time and Date",
              "url": "https://www.timeanddate.com/",
              "description": "提供日历、时钟和各种时间相关信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Time.is",
              "url": "https://time.is/",
              "description": "以51种语言显示任何时区的精确官方原子钟时间，覆盖超过700万个地点。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "航班",
          "originalName": "Flight",
          "id": "flight",
          "websites": [
            {
              "name": "SeatGuru",
              "url": "https://seatguru.com/",
              "description": "探索1,278架飞机的座位图找到您的座位。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Flightradar24",
              "url": "https://www.flightradar24.com/43,24.35/7",
              "description": "全球航班跟踪服务，提供世界各地数千架飞机的实时信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Skyscanner",
              "url": "https://www.skyscanner.co.in/",
              "description": "航班搜索引擎，允许用户按日期、价格和预算搜索航班。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "天气",
          "originalName": "Weather",
          "id": "weather",
          "websites": [
            {
              "name": "Hint.fm Wind Map",
              "url": "https://hint.fm/wind/",
              "description": "美国上空风流的精美轨迹。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Windy",
              "url": "https://www.windy.com/",
              "description": "任何地点的综合天气信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Zoom Earth",
              "url": "https://zoom.earth/",
              "description": "世界的实时可视化，跟踪热带风暴、飓风、恶劣天气、野火等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Earth Nullschool",
              "url": "https://earth.nullschool.net/",
              "description": "超级计算机预测的全球天气状况可视化，每三小时更新一次。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OpenWeatherMap",
              "url": "https://openweathermap.org/",
              "description": "以快速优雅的方式提供天气预报、新闻播报和历史天气数据的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Radiosondy",
              "url": "https://radiosondy.info/",
              "description": "跟踪气象无线电探空仪，提供当前和过去探空仪飞行的数据库，包括发射点、类型、最后帧、航向、速度、高度和频率等信息。（注：无线电探空仪是通常由气象气球携带进入大气层的电池供电遥测仪器。）",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Airheart - Travel Restrictions",
          "url": "https://airheart.com/travel-restrictions/united-states-vaccinated",
          "description": "了解您的旅行限制和要求，包括COVID-19限制。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Country Code",
          "url": "https://countrycode.org/",
          "description": "打电话到世界任何地方的指南，提供国际拨号的国家代码。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Passport Index",
          "url": "https://www.passportindex.org/",
          "description": "探索来自世界各地的护照信息，包括排名和详细信息。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Countries Been",
          "url": "https://www.countriesbeen.com/",
          "description": "跟踪和列出您访问过的国家的移动应用，提供各种功能。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Couchsurfing",
          "url": "https://www.couchsurfing.com/",
          "description": "与免费分享家园和体验的全球社区联系。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Puffin Maps",
          "url": "https://www.puffinmaps.com/",
          "description": "无广告的一体化旅行规划器，帮助您组织旅行计划。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "AllTrails",
          "url": "https://www.alltrails.com/",
          "description": "探索包含30万条步道的数据库，附有户外爱好者的评论和照片。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Wanderprep",
          "url": "https://www.wanderprep.com/",
          "description": "之前提供装备、应用和旅行技巧建议，让旅程更智能。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Looria",
          "url": "https://looria.com/",
          "description": "查找诚实产品信息的可信平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Trip Destination App (iOS)",
          "url": "https://apps.apple.com/us/app/id1580599572",
          "description": "用于搜索和规划旅行目的地的免费iPhone应用。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Freecycle",
          "url": "https://www.freecycle.org/",
          "description": "人们在当地社区免费赠送和获取物品的网络。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Roadtrippers",
          "url": "https://roadtrippers.com/",
          "description": "规划您的路线并使用逐步导航探索公路旅行中的各种景点。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mountain Project",
          "url": "https://www.mountainproject.com/",
          "description": "免费的、众包的世界攀岩目的地指南。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Welcome to My Garden",
          "url": "https://welcometomygarden.org/",
          "description": "为慢旅行者在私人花园中提供免费露营点的非营利网络。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Warmshowers",
          "url": "https://www.warmshowers.org/",
          "description": "自行车旅行者和在旅途中支持他们的主人的社区。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Slowby",
          "url": "https://www.slowby.travel/",
          "description": "提供精心策划的慢旅行行程的平台，获得独特的旅行体验。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "健康",
      "originalName": "Health",
      "id": "health",
      "subcategories": [
        {
          "name": "空气质量",
          "originalName": "Air Quality",
          "id": "air-quality",
          "websites": [
            {
              "name": "Air Quality Index (European Environment Agency)",
              "url": "http://airindex.eea.europa.eu/",
              "description": "提供欧洲实时空气质量数据，具有可视化空气污染水平及其对公共健康影响的交互式地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Berkeley Earth",
              "url": "http://berkeleyearth.org/",
              "description": "专注于提供准确和全面的空气质量和气候数据的非营利组织，提供全球环境数据可视化工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "World Air Quality Index",
              "url": "https://waqi.info/",
              "description": "提供来自世界各地的实时空气质量信息，提供交互式地图和污染水平及其对健康影响的详细数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IQAir",
              "url": "https://www.iqair.com/",
              "description": "运营世界上最大的免费实时空气质量监测平台，为个人、研究人员和政府提供关键数据，以监测和解决空气污染问题，最终帮助保护全球公共健康。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "美食",
          "originalName": "Food",
          "id": "food",
          "websites": [
            {
              "name": "GoBento",
              "url": "https://www.gobento.com/",
              "description": "专注于改善高风险成员健康和福祉的参与平台，特别是那些面临食品不安全问题的成员。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MyFridgeFood",
              "url": "https://myfridgefood.com/",
              "description": "允许用户勾选他们拥有的食材来寻找可以用这些食材制作的食谱的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Just the Recipe",
              "url": "https://www.justtherecipe.com/",
              "description": "提供来自任何食谱网站的直接说明，无广告和弹窗的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Two Peas and Their Pod",
              "url": "https://www.twopeasandtheirpod.com/",
              "description": "提供各种食谱的食谱网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HelloFresh",
              "url": "https://www.hellofresh.com/",
              "description": "餐食套装配送服务，用户可以选择食谱，HelloFresh将食材直接送到他们家门口。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "庭院护理",
          "originalName": "Lawn/Yard care",
          "id": "lawn-yard-care",
          "websites": [
            {
              "name": "Healthy Yards",
              "url": "https://healthyyards.org",
              "description": "提供环保和可持续草坪护理实践的资源和技巧，重点关注对当地野生动物和环境的益处。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Homegrown National Park",
              "url": "https://homegrownnationalpark.org",
              "description": "提供关于通过种植本土物种创建野生动物走廊和促进生物多样性的信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Butterfly Conservation",
              "url": "https://butterfly-conservation.org",
              "description": "分享蝴蝶和飞蛾保护的详细信息，包括物种保护和参与机会。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Xerces Society",
              "url": "https://xerces.org",
              "description": "提供传粉媒介和无脊椎动物保护指导，包括栖息地恢复和可持续实践。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Beyond Pesticides",
              "url": "https://beyondpesticides.org",
              "description": "提供有机和可持续实践的资源，以减少农药使用并保护公众健康和环境。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "National Pesticide Information Center",
              "url": "https://npic.orst.edu",
              "description": "为消费者和健康专业人士提供基于科学的农药使用信息、安全指南和事实表。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "MuscleWiki",
          "url": "https://musclewiki.com/",
          "description": "了解您的身体和肌肉的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Just a Minute",
          "url": "https://jinay.dev/just-a-minute/",
          "description": "在一分钟内测试您的时间感知能力（对时间流逝的感觉）。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "FutureMe",
          "url": "https://www.futureme.org/",
          "description": "给未来的自己写信的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Strobe.Cool",
          "url": "https://strobe.cool/",
          "description": "使用频闪效果创建视觉刺激。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "UnTools",
          "url": "https://untools.co/",
          "description": "思维工具和框架的集合，帮助解决问题、决策制定和理解系统。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Puzzle Loop",
          "url": "https://www.puzzle-loop.com/",
          "description": "提供规则简单但解决方案具有挑战性的逻辑谜题的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "What Should You Do with Your Life?",
          "url": "https://guzey.com/personal/what-should-you-do-with-your-life/",
          "description": "提供人生决策方向和建议的文章。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "InnerBody",
          "url": "https://www.innerbody.com/",
          "description": "研究健康产品、服务等的评论和研究。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Sleep Calculator",
          "url": "https://sleepcalculator.com/",
          "description": "帮助用户根据期望的起床时间确定最佳就寝时间的工具，优化睡眠周期以获得更好的休息和警觉性。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "SimpleLab",
          "url": "https://gosimplelab.com/",
          "description": "使用基于云的平台在美国提供快速、可靠的环境测试，用于高效的采样、测试和数据管理。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "音乐音频",
      "originalName": "Music / Audio",
      "id": "music---audio",
      "subcategories": [
        {
          "name": "音乐发现",
          "originalName": "Find Music",
          "id": "find-music",
          "websites": [
            {
              "name": "Lalal.ai",
              "url": "https://www.lalal.ai/",
              "description": "从任何音频中提取人声、伴奏和各种乐器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Audd.io",
              "url": "https://audd.io/",
              "description": "从声音或流媒体中识别音乐或正在播放的内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Music-Map",
              "url": "https://www.music-map.com/",
              "description": "根据您的偏好发现相似的音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Commercial Tunage",
              "url": "https://www.commercialtunage.com/",
              "description": "识别广告中播放的歌曲。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "免费音乐",
          "originalName": "Free Music",
          "id": "free-music",
          "websites": [
            {
              "name": "Pretzel Rocks",
              "url": "https://www.pretzel.rocks/",
              "description": "为Twitch和YouTube提供流媒体安全音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Incompetech",
              "url": "https://incompetech.com/",
              "description": "免版税音乐集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Chosic",
              "url": "https://www.chosic.com/",
              "description": "免费背景音乐，可用于商业和非商业用途。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "混音",
          "originalName": "Mix Sounds",
          "id": "mix-sounds",
          "websites": [
            {
              "name": "Hidden Life Radio",
              "url": "https://hiddenliferadio.com/",
              "description": "由马萨诸塞州剑桥市树木生物数据生成的音乐直播流。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Noisli",
              "url": "https://www.noisli.com/",
              "description": "创建和收听背景声音，以提高专注力和生产力，或放松身心。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Soundrop",
              "url": "https://naim30.github.io/soundrop/",
              "description": "交互式音乐播放器，您可以创建生成音乐的美丽图案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SoundLove",
              "url": "https://www.producthunt.com/posts/soundlove",
              "description": "帮助您根据心情发现和创建播放列表的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Rainy Mood",
              "url": "https://rainymood.com/",
              "description": "享受舒缓的雨声，用于放松、睡眠和学习。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "I Miss the Office",
              "url": "https://imisstheoffice.eu/",
              "description": "办公室噪音生成器，提供现代办公室生活的环境声音，帮助在家工作时重现办公室氛围。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "音乐理论",
          "originalName": "Music Theory",
          "id": "music-theory",
          "websites": [
            {
              "name": "Teoria",
              "url": "https://www.teoria.com/",
              "description": "",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MusicTheory.net",
              "url": "https://www.musictheory.net/",
              "description": "",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "All About Music Theory",
              "url": "https://www.allaboutmusictheory.com/",
              "description": "钢琴键盘、音乐记谱法、大调音阶",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Studio Guru - Note Frequency Chart",
              "url": "https://studioguru.co/producer-tools/note-frequency-chart/",
              "description": "",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "押韵",
          "originalName": "Rhyme",
          "id": "rhyme",
          "websites": [
            {
              "name": "RhymeZone",
              "url": "https://www.rhymezone.com/",
              "description": "查找押韵词、同义词、形容词等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Rhymer",
              "url": "https://rhymer.com/",
              "description": "免费在线押韵词典。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Spotify工具",
          "originalName": "Spotify",
          "id": "spotify",
          "websites": [
            {
              "name": "Chosic",
              "url": "https://www.chosic.com/spotify-playlist-analyzer/",
              "description": "通过分析您的播放列表发现新音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Pudding",
              "url": "https://pudding.cool/2020/12/judge-my-spotify/",
              "description": "训练来评估音乐品味的A.I.。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Discoverify Music",
              "url": "https://www.discoverifymusic.com/login",
              "description": "根据您的品味发现新音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Spottr",
              "url": "https://spottr.vercel.app/login",
              "description": "查看您的Spotify统计数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Playlist Mutator",
              "url": "https://playlistmutator.com/",
              "description": "在React中变异现有播放列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TuneMyMusic",
              "url": "https://www.tunemymusic.com/",
              "description": "在音乐服务之间传输播放列表。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "KHInsider",
          "url": "https://downloads.khinsider.com/",
          "description": "提供MP3和无损格式的视频和PC游戏原声音乐下载。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Online Tone Generator",
          "url": "https://onlinetonegenerator.com/",
          "description": "生成音调，您也可以以WAV格式下载它们。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tello Music Charts",
          "url": "https://music.tello.app/",
          "description": "查找世界各国的最新音乐排行榜。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Music Lab",
          "url": "https://musiclab.chromeexperiments.com/Experiments",
          "description": "通过有趣的动手实验使音乐学习更加容易。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Rap4Ever",
          "url": "https://www.rap4ever.org/",
          "description": "探索说唱歌曲、歌词、混音带、专辑、艺术家等。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Every Noise",
          "url": "https://everynoise.com/",
          "description": "算法生成的音乐流派空间散点图的持续尝试。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "MP3Cut",
          "url": "https://mp3cut.net/",
          "description": "在线修剪或切割任何音频文件。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "MP3Gain",
          "url": "https://mp3gain.flowsoft7.com/",
          "description": "增加、减少和标准化MP3音频文件的音量水平（每个文件60M大小限制）。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Magic Playlist",
          "url": "https://create.magicplaylist.co/#/?_k=m5jobg",
          "description": "输入您最喜欢的歌曲并创建完美的播放列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Moises AI",
          "url": "https://moises.ai/",
          "description": "以任何调性、任何速度与您最喜欢的艺术家一起演奏。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Drumeo",
          "url": "https://www.drumeo.com/",
          "description": "与世界最好的老师学习打鼓。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "SoundLove",
          "url": "https://soundlove.se/",
          "description": "不寻常的合成算法，为音乐增添随机性和不可预测性。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Audionautix",
          "url": "https://audionautix.com/",
          "description": "由Jason Shaw作曲和制作的音乐，免费下载和使用（甚至可用于商业目的）。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Typatone",
          "url": "https://typatone.com/",
          "description": "通过在键盘上打字生成您自己的音乐。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Incredibox",
          "url": "https://www.incredibox.com/",
          "description": "音乐应用程序，让您在快乐的打击乐手团队帮助下创建自己的音乐。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Sampurr",
          "url": "https://www.sampurr.com/",
          "description": "从网络上采样音频。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Audiocheck",
          "url": "https://www.audiocheck.net/",
          "description": "在线测试您的音频设备。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mixlr",
          "url": "https://mixlr.com/",
          "description": "在线分享高质量实时音频。使用任何音源广播，邀请人们收听，并实时聊天（仅收听免费）。",
          "isPaid": true,
          "isStudentFriendly": false
        },
        {
          "name": "Learn Choral Music",
          "url": "https://www.learnchoralmusic.co.uk/",
          "description": "John的MIDI文件集合，包含以声音为重点的知名合唱作品，可免费下载。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Bandura Festival",
          "url": "https://bandura.ukrzen.in.ua/en#lvivbandurfest",
          "description": "在线班杜拉琴，一种传统的乌克兰乐器。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "AllMusic",
          "url": "https://www.allmusic.com/",
          "description": "提供关于专辑、艺术家、歌曲和乐队的全面深入信息，为音乐爱好者提供宝贵资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ASMR Microphones",
          "url": "https://asmrmicrophones.com",
          "description": "提供各种ASMR麦克风的评测、比较和专家意见，帮助用户选择最合适的设备。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "影视娱乐",
      "originalName": "Movies and Series",
      "id": "movies-and-series",
      "subcategories": [
        {
          "name": "动漫",
          "originalName": "Anime",
          "id": "anime",
          "websites": [
            {
              "name": "Reddit Top Anime Streaming Sites",
              "url": "https://reddit.com/r/streamingAnime/wiki/topsites/",
              "description": "Reddit上列出顶级动漫流媒体网站的Wiki页面。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Reddit Legal Anime Streams",
              "url": "https://reddit.com/r/anime/wiki/legal_streams/",
              "description": "提供Reddit上合法动漫流媒体网站列表的Wiki页面。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Kanopy",
          "url": "https://www.kanopy.com/en/",
          "description": "在图书馆或大学支持下免费提供数千部电影的流媒体平台。",
          "isPaid": false,
          "isStudentFriendly": true
        },
        {
          "name": "Movie Map",
          "url": "https://www.movie-map.com/",
          "description": "根据您的偏好找到相似的电影。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Reddit Movie Suggestions",
          "url": "https://www.reddit.com/r/MovieSuggestions/wiki/faq",
          "description": "各种类型的电影推荐列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "A Good Movie to Watch",
          "url": "https://agoodmovietowatch.com/",
          "description": "精心挑选的高评分电影和电视剧。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tubi TV",
          "url": "https://tubitv.com/home",
          "description": "提供各种电影和电视剧的免费流媒体服务。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tiii.me",
          "url": "https://tiii.me/",
          "description": "计算您观看电视剧的总时间。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "JustWatch",
          "url": "https://www.justwatch.com/",
          "description": "查找和流播电影与电视剧的指导平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Movie Settings Database",
          "url": "https://www.moviesettingsdatabase.com/",
          "description": "按场景整理的超过30,000部电影和电视剧的有序集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Reelgood Netflix Roulette",
          "url": "https://reelgood.com/roulette/netflix",
          "description": "随机推荐Netflix上可观看的电影或电视剧。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Reelgood",
          "url": "https://reelgood.com/",
          "description": "浏览、搜索并观看来自150多个服务的电视剧和电影，包括Netflix、Hulu、HBO、Disney+、Prime Video等。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Movie Sounds",
          "url": "https://movie-sounds.org/",
          "description": "提供短音频片段和特效的免费电影引言档案。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tunefind",
          "url": "https://www.tunefind.com/",
          "description": "发现您喜爱的电视剧和电影中的音乐。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "IMSDB",
          "url": "https://imsdb.com/",
          "description": "网络上最大的电影剧本集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Physics in Film and TV",
          "url": "https://physicsinfilmandtv.wordpress.com/",
          "description": "探索电影和电视中物理概念呈现的博客。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "RareFilm",
          "url": "https://rarefilm.net/",
          "description": "珍稀和老电影平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "I Have No TV",
          "url": "https://ihavenotv.com/",
          "description": "观看免费在线纪录片。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "WCostream",
          "url": "https://m.wcostream.com/",
          "description": "免费卡通和动漫系列流播。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Watch Documentaries",
          "url": "https://watchdocumentaries.com/",
          "description": "提供各种主题纪录片集合的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Senses of Cinema",
          "url": "https://www.sensesofcinema.com/",
          "description": "最早的在线电影期刊之一，以其与电影研究和行业趋势相关的专业高质量内容而闻名。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "媒体",
      "originalName": "Media",
      "id": "media",
      "subcategories": [
        {
          "name": "X/Twitter工具",
          "originalName": "X / Twitter",
          "id": "x---twitter",
          "websites": [
            {
              "name": "TweetDeck",
              "url": "https://tweetdeck.twitter.com/",
              "description": "Twitter仪表板应用程序，用于Twitter账户的管理、跟踪和组织。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Twitter Video Downloader",
              "url": "https://twittervideodownloader.com",
              "description": "在线工具，直接将任何Twitter视频下载到您的手机或电脑。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ThreadReaderApp",
              "url": "https://threadreaderapp.com/",
              "description": "帮助您将Twitter线程保存为PDF的平台，让阅读和分享变得更加容易。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Tweepsmap",
              "url": "https://tweepsmap.com/",
              "description": "AI驱动的Twitter分析和管理工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Shadowban Checker",
              "url": "https://shadowban.yuzurisa.com/",
              "description": "检查用户名是否在Twitter上被影子封禁的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Twitter Name Generator",
              "url": "https://twitternamegenerator.com/",
              "description": "免费在线工具，为Twitter生成漂亮的昵称。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Thread Hunt",
              "url": "https://threadhunt.xyz/",
              "description": "发现优质Twitter线程的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Small World",
              "url": "https://smallworld.kiwi/signin",
              "description": "利用您Twitter个人资料中的位置信息查找附近朋友。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Murmel",
              "url": "https://murmel.social/top",
              "description": "策划来自Twitter宇宙的最新发人深省的故事。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Chirpty",
              "url": "https://chirpty.com/",
              "description": "创建您自己的Twitter互动圈。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Capture My Tweet",
              "url": "https://capturemytweet.in/",
              "description": "免费将您的推文转换为精美图片。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Twitter Card Generator",
              "url": "https://freecodetools.org/twitter-card-generator/",
              "description": "生成Twitter卡片的工具，免费为推文附加丰富内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Rattibha",
              "url": "https://rattibha.com/",
              "description": "按类别和作者策划Twitter线程，提供时间、语言和排序选项。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Musk Messages",
              "url": "https://muskmessages.com/",
              "description": "编译和分类Elon Musk在Twitter上直接消息的平台，方便访问他的想法和声明。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Reddit工具",
          "originalName": "Reddit",
          "id": "reddit",
          "websites": [
            {
              "name": "RedditList",
              "url": "https://redditlist.com/",
              "description": "按各种主题分类的子版块综合列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Redsim",
              "url": "https://anvaka.github.io/redsim/",
              "description": "根据您的兴趣查找相似的子版块。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Reveddit",
              "url": "https://www.reveddit.com/about/",
              "description": "揭示Reddit被删除的内容。您可以按用户名、子版块(r/)、链接或域名搜索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Spacebar Counter - Reddit List",
              "url": "https://www.spacebarcounter.net/reddit-list",
              "description": "100多个子版块的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Unreadit",
              "url": "https://unreadit.com/",
              "description": "Reddit驱动的每周新闻通讯，从各种子版块策划内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "What Is This Thing",
              "url": "https://whatisthisthing.vercel.app/",
              "description": "汇总来自r/whatisthisthing子版块的帖子和答案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Gummy Search",
              "url": "https://gummysearch.com/",
              "description": "在Reddit上探索痛点、内容想法，发现人们急于付费的内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RedditSearch.io",
              "url": "https://redditsearch.io/",
              "description": "高级Reddit搜索引擎，允许您过滤和自定义搜索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Better Reddit Search",
              "url": "https://betterredditsearch.web.app/",
              "description": "通过改进的功能和特性增强您的Reddit搜索体验。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Radio Garden",
          "url": "https://radio.garden/",
          "description": "探索以绿点形式在Google Earth地图上显示的实时广播电台，只需一键即可收听任何一个电台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Radiooooo",
          "url": "https://radiooooo.com/",
          "description": "\"音乐时光机\"，您可以探索不同时代和地点的音乐。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Lightyear.fm",
          "url": "https://www.lightyear.fm/",
          "description": "展示广播信号以光速从地球传播的距离。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "TasteDive",
          "url": "https://tastedive.com/",
          "description": "基于您的偏好发现音乐、书籍、电影等的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "PIDGI Wiki",
          "url": "https://www.pidgi.net/wiki/Main_Page",
          "description": "社区驱动的视频游戏媒体数据库，包括艺术作品、宣传材料、标志等。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Kassellabs",
          "url": "https://kassellabs.io/",
          "description": "在著名电影和电视剧片头中创建您自己的文本。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "USTVGO",
          "url": "https://ustvgo.tv/",
          "description": "免费在线直播电视频道。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "All You Can Read",
          "url": "https://www.allyoucanread.com/",
          "description": "互联网上最大的杂志和报纸数据库，收录了来自世界各地约25,000份出版物。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "LexiCap",
          "url": "https://karpathy.ai/lexicap/",
          "description": "Lex Fridman播客节目的文字记录。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Brett Hall's TokCast Transcripts",
          "url": "https://www.aniketvartak.com/html/hall-index.html",
          "description": "Brett Hall's TokCast播客的文字记录。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Thumbly",
          "url": "https://thumbly.ai/",
          "description": "将您的脚本转换为引人注目的缩略图，可以增加您的YouTube观看量。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "经济",
      "originalName": "Economy",
      "id": "economy",
      "subcategories": [],
      "websites": [
        {
          "name": "Investopedia",
          "url": "https://www.investopedia.com/",
          "description": "为投资者提供金融教育、新闻和研究的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Money",
          "url": "https://money.com/",
          "description": "个人理财和金融新闻的综合资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "The Balance Money",
          "url": "https://www.thebalancemoney.com/",
          "description": "金融教育和资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CNN Fear and Greed Index",
          "url": "https://edition.cnn.com/markets/fear-and-greed",
          "description": "了解当前推动市场的情绪。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Where's Willy",
          "url": "https://www.whereswilly.com/",
          "description": "致力于在全球范围内追踪加拿大纸币的国际非营利志愿项目。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Where's George",
          "url": "https://www.wheresgeorge.com/",
          "description": "类似于\"Where's Willy\"的项目，用于在全球追踪美国纸币。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "EuroBillTracker",
          "url": "https://en.eurobilltracker.com/",
          "description": "致力于在全球范围内追踪欧元纸币的国际非营利志愿项目。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "TradingView",
          "url": "https://www.tradingview.com/",
          "description": "全球超过3000万交易员和投资者使用的平台和社交网络，用于发现全球市场机会。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "LendingTree",
          "url": "https://www.lendingtree.com/",
          "description": "通过寻找贷款而非制造贷款来为您省钱的市场平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Masterworks",
          "url": "https://www.masterworks.com/",
          "description": "投资蓝筹艺术品的专属社区。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "EquityBee",
          "url": "https://equitybee.com/",
          "description": "通过连接全球投资者网络，为初创公司员工提供行使股票期权所需资金。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "EquityZen",
          "url": "https://equityzen.com/",
          "description": "允许您通过EquityZen基金在二级市场投资或出售股份。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "WTF Happened in 1971",
          "url": "https://wtfhappenedin1971.com/",
          "description": "探索和突出1971年发生的各种经济、社会和金融事件的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ergodicity Economics",
          "url": "https://ergodicityeconomics.com/",
          "description": "提供遍历性经济学和相关概念见解的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "商业",
      "originalName": "Business",
      "id": "business",
      "subcategories": [
        {
          "name": "金融",
          "originalName": "Finance",
          "id": "finance",
          "websites": [
            {
              "name": "FIGR",
              "url": "https://www.figr.app/",
              "description": "Google Docs + 计算器 = 个人理财",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LiveFortunately",
              "url": "https://app.livefortunately.com/",
              "description": "为更好的财务未来制定计划。创建完整的财务计划，充分利用您的资金。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "KeeperTax",
              "url": "https://www.keepertax.com/ask-an-ai-accountant-2-0",
              "description": "AI会计师，帮助您获得帮助和计算税务相关问题。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "专利",
          "originalName": "Patents",
          "id": "patents",
          "websites": [
            {
              "name": "Espacenet",
              "url": "https://worldwide.espacenet.com/",
              "description": "免费访问超过1.3亿份专利文件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Patents",
              "url": "https://patents.google.com/",
              "description": "搜索并阅读来自世界各地的专利全文。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WIPO Patentscope",
              "url": "https://patentscope.wipo.int/search/en/search.jsf",
              "description": "搜索1.05亿份专利文件，包括430万份已发布的国际专利申请(PCT)。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Patently Apple",
              "url": "https://www.patentlyapple.com/patents-applications/",
              "description": "探索苹果公司的专利申请。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "USPTO Report",
              "url": "https://uspto.report/",
              "description": "提供美国专利商标局专利相关信息和报告的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "营销",
          "originalName": "Marketing",
          "id": "marketing",
          "websites": [
            {
              "name": "Telega",
              "url": "https://telega.io/",
              "description": "在Telegram中寻找目标受众并启动有效的广告活动。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IGMassDMS",
              "url": "https://igmassdms.com/",
              "description": "在不使用您账户的情况下向目标受众发送Instagram直接消息。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Product Management Tools",
              "url": "https://rohitverma.gumroad.com/l/PM-tools",
              "description": "产品管理工具集合，售价5美元。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Yes Promo",
              "url": "https://yespromo.me/",
              "description": "100多个成功的Reddit自我推广帖子的免费数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Marketing for Startups E-Book",
              "url": "https://www.welovenocode.com/marketingforstartups",
              "description": "免费电子书，包含47种获得客户和实现指数级增长的实用策略。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ArrayList",
              "url": "https://arraylist.org/",
              "description": "云端列表数据库，用于存储表单提交、电子邮件订阅表单或进行AJAX/REST调用来存储数值。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SuperMeme AI",
              "url": "https://www.supermeme.ai/",
              "description": "使用AI生成110多种语言的原创表情包。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Apollo",
              "url": "https://www.apollo.io/",
              "description": "销售智能和参与平台，搜索、接触并转化超过6000万家公司的2.5亿多个联系人。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Vocus.io",
              "url": "https://vocus.io/",
              "description": "个性化电子邮件活动，跟踪和自动化电子邮件跟进，安排约会，与您的CRM同步，并与您的团队协作。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "社交媒体",
          "originalName": "Social Media",
          "id": "social-media",
          "websites": [
            {
              "name": "Instagram Caption Maker",
              "url": "https://apps4lifehost.com/Instagram/CaptionMaker.html",
              "description": "IG标题的简洁美观换行符。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Social Sizes",
              "url": "https://socialsizes.io/",
              "description": "社交媒体的图片和视频尺寸。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ocoya",
              "url": "https://www.ocoya.net/",
              "description": "创建、自动生成和安排内容的平台。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Imgupscaler",
              "url": "https://imgupscaler.com/?ref=producthunt",
              "description": "基于AI的PNG/JPG图像放大器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Namechk",
              "url": "https://namechk.com/",
              "description": "在几秒钟内检查用户名或域名的可用性。30个域名和90多个社交媒体账户。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Inflact",
              "url": "https://inflact.com/downloader/instagram/video/",
              "description": "将Instagram上的原始高质量视频保存到您的设备。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TikTok Video Downloader",
              "url": "https://ttvdl.com/",
              "description": "下载TikTok视频。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SWAPD",
              "url": "https://swapd.co/",
              "description": "提供虚拟物品和服务买卖交易的中介服务，提供安全平台，连接用户与庞大的买家、卖家网络和数字市场中的机会。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "趋势分析",
          "originalName": "Trends",
          "id": "trends",
          "websites": [
            {
              "name": "Google Trends",
              "url": "https://trends.google.com/trends/?geo=US",
              "description": "基于Google搜索探索热门话题和见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Trends - Visualize",
              "url": "https://trends.google.com/trends/hottrends/visualize",
              "description": "可视化和探索Google上的最热门趋势。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Statista",
              "url": "https://www.statista.com/",
              "description": "统计数据和可视化平台，提供各行业和主题的见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Books Ngram Viewer",
              "url": "https://books.google.com/ngrams",
              "description": "图形化显示所选年份书籍语料库中短语出现频率。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "会议工具",
          "originalName": "Meetings",
          "id": "meetings",
          "websites": [
            {
              "name": "When2meet",
              "url": "https://www.when2meet.com/",
              "description": "安排团体会议的免费服务。允许用户创建和参与可用性调查，找到团体聚会的最佳时间。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Sessions",
              "url": "https://sessions.us/",
              "description": "旨在增强会议体验的基于Web的会议工具。旨在改善整体会议体验，免费提供。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Form to Chatbot",
              "url": "https://formtochatbot.com/",
              "description": "将Google表单转换为聊天机器人形式的互动对话。此工具有助于基于表单回复创建引人入胜的动态交互。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Crunchbase",
          "url": "https://www.crunchbase.com/",
          "description": "发现创新公司、初创企业和商业世界关键人物的平台。提供关于公司、投资和行业趋势的综合数据和见解。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Business Model Toolbox",
          "url": "https://bmtoolbox.net/",
          "description": "学习各种商业概念和模式的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Gumroad",
          "url": "https://gumroad.com/",
          "description": "创作者直接向客户销售数字产品的电子商务平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Humble Bundle",
          "url": "https://www.humblebundle.com/",
          "description": "销售游戏、电子书、软件和数字内容的平台，致力于支持慈善事业，同时以实惠价格提供优质内容。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Google Takeout",
          "url": "https://takeout.google.com/",
          "description": "导出您所有Google数据的副本。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Honey",
          "url": "https://www.joinhoney.com/",
          "description": "在全球超过30,000个网站上自动搜索优惠券的浏览器扩展。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BotHelp",
          "url": "https://bothelp.io/widget",
          "description": "为网站提供免费聊天按钮小部件。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CertificateClaim",
          "url": "https://www.certificateclaim.com/",
          "description": "创建和发送各种类型证书的数字服务。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Respresso",
          "url": "https://respresso.io/",
          "description": "管理应用本地化文本、图像、颜色、字体等的工具，只需一键即可自动传送到您的项目中。",
          "isPaid": true,
          "isStudentFriendly": false
        },
        {
          "name": "Bonanza",
          "url": "https://www.bonanza.com/",
          "description": "让企业家基于回头客建立可持续业务的在线市场。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Pipl",
          "url": "https://pipl.com/",
          "description": "通过搜索Pipl的全球身份信息索引，使用电子邮件地址、社交用户名或电话号码识别、搜索和验证员工，减少客户摩擦，打击欺诈，节省审查和研究时间。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "工作求职",
      "originalName": "Jobs",
      "id": "jobs",
      "subcategories": [
        {
          "name": "远程工作",
          "originalName": "Remote Jobs",
          "id": "remote-jobs",
          "websites": [
            {
              "name": "Remote OK",
              "url": "https://remoteok.com/",
              "description": "远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Remotive",
              "url": "https://remotive.com/",
              "description": "连接远程公司与优秀专业人士的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Remote.co",
              "url": "https://remote.co/",
              "description": "寻找远程工作机会的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Remote Leaf",
              "url": "https://remoteleaf.com/",
              "description": "专注于科技行业远程机会的招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Remote Leads",
              "url": "https://remoteleads.io/",
              "description": "在软件开发领域连接公司与远程人才的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RemoteBear",
              "url": "https://remotebear.io/",
              "description": "科技和设计远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RemoteBase",
              "url": "https://remotebase.com/",
              "description": "连接远程工作者与提供远程职位公司的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JustRemote",
              "url": "https://justremote.co/",
              "description": "远程工作机会招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JS Remotely",
              "url": "https://jsremotely.com/",
              "description": "专门针对远程JavaScript职位的招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Jobspresso",
              "url": "https://jobspresso.co/",
              "description": "科技、营销等领域远程工作的策划招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Just Join",
              "url": "https://justjoin.it/",
              "description": "IT远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FlexJobs",
              "url": "https://flexjobs.com/",
              "description": "灵活和远程工作机会招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "We Work Remotely",
              "url": "https://weworkremotely.com/",
              "description": "各行业远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Daily Remote",
              "url": "https://dailyremote.com/",
              "description": "每日更新的远程工作机会招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AngelList Candidates",
              "url": "https://angel.co/candidates/overview",
              "description": "连接初创公司与潜在候选人的平台，包括远程职位。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hired",
              "url": "https://hired.com/",
              "description": "连接技术人才与创新公司的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PowerToFly",
              "url": "https://powertofly.com/",
              "description": "专注于科技行业女性远程机会的招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SkipTheDrive",
              "url": "https://skipthedrive.com/",
              "description": "各行业远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Authentic Jobs",
              "url": "https://authenticjobs.com/",
              "description": "创意和技术专业人士的招聘板，包括远程职位。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Working Nomads",
              "url": "https://workingnomads.co/",
              "description": "各行业远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Europe Remotely",
              "url": "https://europeremotely.com/",
              "description": "专门针对欧洲远程职位的招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Virtual Vocations",
              "url": "https://virtualvocations.com/",
              "description": "远程办公和远程职位招聘板。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "自由职业",
          "originalName": "Freelancing",
          "id": "freelancing",
          "websites": [
            {
              "name": "Remote Starter Kit",
              "url": "https://www.remotestarterkit.com/",
              "description": "远程团队工具和流程的终极清单。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fiverr",
              "url": "https://www.fiverr.com/",
              "description": "为您的业务寻找完美的自由职业服务。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Upwork",
              "url": "https://www.upwork.com/",
              "description": "雇佣自由职业者并在线获得自由职业工作。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "简历作品集",
          "originalName": "Portfolio / CV / Resume",
          "id": "portfolio---cv---resume",
          "websites": [
            {
              "name": "University of Nebraska Omaha - Job & Internship Resources",
              "url": "https://www.unomaha.edu/student-life/achievement/academic-and-career-development-center/career-development/jobs-and-internships/job-internship-resources.php",
              "description": "内布拉斯加大学奥马哈分校学术和职业发展中心提供的简历和求职信资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "VisualCV",
              "url": "https://www.visualcv.com/resume-samples/",
              "description": "500多个专业简历样本集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SuperPortfolio",
              "url": "https://superportfolio.co/",
              "description": "在线作品集制作器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Referd.ai",
              "url": "https://www.referd.ai/resume-scanner",
              "description": "免费简历扫描器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RxResu.me",
              "url": "https://rxresu.me/",
              "description": "免费开源简历构建器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GoodCV",
              "url": "https://www.goodcv.com/",
              "description": "无需Photoshop或AI技术，几分钟内创建专业简历/CV。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JSON Resume",
              "url": "https://jsonresume.io/",
              "description": "根据规范上传您的JSON简历并进行精美渲染。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CVmkr",
              "url": "https://cvmkr.com/",
              "description": "免费创建、维护、发布和分享您的简历。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Novoresume",
              "url": "https://novoresume.com/",
              "description": "在线简历构建器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HelloTechRecruiters",
              "url": "https://hellotechrecruiters.com/",
              "description": "为技术招聘人员量身定制。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FlowCV",
              "url": "https://flowcv.com/",
              "description": "AI增强的简历构建器、求职信、工作跟踪器、电子邮件签名、个人网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Signature Maker",
              "url": "https://signature-maker.net/",
              "description": "创建手写数字签名。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "职业发展",
          "originalName": "Careers",
          "id": "careers",
          "websites": [
            {
              "name": "Roadmap.sh",
              "url": "https://roadmap.sh/",
              "description": "提供各种开发领域的路线图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WTF Should I Do With My Life",
              "url": "https://www.wtfshouldidowithmylife.com/",
              "description": "探索和了解不同职业的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "path-to-polymath.notion",
              "url": "https://path-to-polymathy.notion.site/path-to-polymathy/Path-To-Polymathy-d7586429b7ce4db1889fc539822b9670",
              "description": "整理了所有学科和领域的安排，以及构成它们的具体主题。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Y Combinator Jobs",
          "url": "https://www.ycombinator.com/jobs",
          "description": "发现Y Combinator策划的最佳初创公司工作机会。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Coroflot",
          "url": "https://www.coroflot.com/discover",
          "description": "专门为设计师量身定制的求职平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Cool Startup Jobs",
          "url": "https://www.coolstartupjobs.com/",
          "description": "探索成长型初创公司的工作机会，给您的股票期权一个机会。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Anon Friendly",
          "url": "https://anonfriendly.com/",
          "description": "寻找尊重您匿名愿望的工作。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Prompt Engineering Jobs",
          "url": "https://prompt-engineering-jobs.com",
          "description": "探索Prompt的工程工作机会。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "KeyValues",
          "url": "https://www.keyvalues.com/",
          "description": "寻找与您价值观一致的工程团队。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "About.me",
          "url": "https://about.me/",
          "description": "自由职业者和企业家扩大受众和吸引客户的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Rejected.us",
          "url": "https://rejected.us/",
          "description": "阅读和分享工作被拒的故事。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tech Interview Handbook",
          "url": "https://www.techinterviewhandbook.org/",
          "description": "免费策划的面试准备材料。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "创业",
      "originalName": "Startups",
      "id": "startups",
      "subcategories": [
        {
          "name": "失败案例",
          "originalName": "Failures",
          "id": "failures",
          "websites": [
            {
              "name": "Failory - Google Failures",
              "url": "https://www.failory.com/google/",
              "description": "从Google的100多个失败案例中学习，以构建盈利业务和扩展被收购的公司。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Killed by Google",
              "url": "https://killedbygoogle.com/",
              "description": "Google推出并终止的项目。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "创意发现",
          "originalName": "Finding Ideas",
          "id": "finding-ideas",
          "websites": [
            {
              "name": "Random Startup Website Generator",
              "url": "https://tiffzhang.com/startup/",
              "description": "随机初创公司网站生成器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AnswerSocrates",
              "url": "https://answersocrates.com/",
              "description": "免费发现人们在Google上询问的几乎任何主题的问题。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IdeasAI",
              "url": "https://ideasai.com/",
              "description": "由OpenAI的GPT-3生成的想法。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AnswerThePublic",
              "url": "https://answerthepublic.com/",
              "description": "发现人们在热门搜索引擎中询问的问题。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "List of Emerging Technologies",
              "url": "https://en.wikipedia.org/wiki/List_of_emerging_technologies",
              "description": "维基百科新兴技术列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "UniCorner",
              "url": "https://unicorner.news/",
              "description": "每周一早晨为您的收件箱提供新兴初创公司2分钟概览的通讯。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DemandHunt",
              "url": "https://demandhunt.com/",
              "description": "发现和投票支持新初创公司的平台。",
              "isPaid": true,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "连接工具",
          "originalName": "Connectivity",
          "id": "connectivity",
          "websites": [
            {
              "name": "Integromat",
              "url": "https://www.integromat.com/en?pc=referralbonus",
              "description": "几次点击即可连接应用程序并自动化工作流程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IFTTT",
              "url": "https://ifttt.com/",
              "description": "快速轻松地自动化您最喜欢的应用程序和设备，使它们以新的强大方式协同工作。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Franz",
              "url": "https://meetfranz.com/",
              "description": "在一个平台中管理您的所有消息应用程序，如WhatsApp、Facebook Messenger、Slack、Telegram等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Remote Desktop",
              "url": "https://remotedesktop.google.com/",
              "description": "远程连接您的家庭或工作电脑，或与他人共享您的屏幕。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RecWide",
              "url": "https://www.recwide.com/",
              "description": "屏幕和网络摄像头录制器。免费，在线（无需下载）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "1,000 True Fans",
              "url": "https://kk.org/thetechnium/1000-true-fans/",
              "description": "培养1,000个忠实粉丝以实现可持续创意成功的概念。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Alias",
              "url": "https://alias.co/",
              "description": "与最好的保持同步。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LittleSis",
              "url": "https://littlesis.org/",
              "description": "商业和政府高层人物关系的免费数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "设计",
          "originalName": "Design",
          "id": "design",
          "websites": [
            {
              "name": "Social Image Maker",
              "url": "https://socialimagemaker.io/",
              "description": "轻松创建社交媒体图片的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Source Design Resources",
              "url": "https://opensourcedesign.net/resources/",
              "description": "展示提供开放许可图标、字体、图片、工具和其他设计资源的网站和平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Transparent Textures",
              "url": "https://transparenttextures.com/",
              "description": "为设计项目提供透明纹理的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Collection of $0 Design Tools",
              "url": "https://www.producthunt.com/e/0-design-tools",
              "description": "帮助项目创建的免费设计工具集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Easel.ly",
              "url": "https://www.easel.ly/",
              "description": "可视化各种类型信息的设计工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Material Design",
              "url": "https://m3.material.io/",
              "description": "Google为构建Android、iOS、Flutter和网页高质量数字体验而设计的设计系统。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Rasterizer.io",
              "url": "https://rasterizer.io/",
              "description": "创建动态图像的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Jitter.Video",
              "url": "https://jitter.video/",
              "description": "网页上的简单动画工具。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Polotno Studio",
              "url": "https://studio.polotno.com/",
              "description": "无需注册和广告即可创建图形设计的网页应用程序，Canva的免费替代品。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "BitBof",
              "url": "https://bitbof.com/",
              "description": "免费绘画和素描应用程序。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Sumo",
              "url": "https://sumo.app/",
              "description": "绘图工具和图像编辑器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Random Design Stuff",
              "url": "https://randomdesignstuff.com/",
              "description": "浏览为设计师精心挑选的网站，既有用的也有无用的。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Discover NFT Club",
              "url": "https://www.discovernft.club/",
              "description": "发现最新的NFT项目。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Haikei",
              "url": "https://app.haikei.app/",
              "description": "基于网络的设计工具，为各种目的生成独特的SVG设计资产。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Spline",
              "url": "https://spline.design/",
              "description": "创建3D场景、编辑材质和建模3D对象，控制设计工作的结果。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visiwig",
              "url": "https://www.visiwig.com/",
              "description": "仅通过点击和粘贴创建图形。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FreeType",
              "url": "https://freetype.org/",
              "description": "用C语言编写的字体渲染软件库，能够为大多数矢量和位图字体格式产生高质量输出，设计为小巧、高效、高度可定制和便携。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "颜色工具",
          "originalName": "Colors",
          "id": "colors",
          "websites": [
            {
              "name": "HexColor",
              "url": "https://hexcolor.co/",
              "description": "提供各种免费颜色工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Adobe Color Wheel",
              "url": "https://color.adobe.com/create/color-wheel",
              "description": "可用于生成调色板的色轮。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SchemeColor",
              "url": "https://www.schemecolor.com/",
              "description": "允许您下载配色方案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mobile Palette Generator",
              "url": "https://mobilepalette.colorion.co/",
              "description": "生成移动端调色板的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DeGraeve Color Palette Generator",
              "url": "https://www.degraeve.com/color-palette/",
              "description": "生成调色板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "0to255",
              "url": "https://0to255.com/",
              "description": "基于任何颜色帮助查找更亮和更暗颜色的颜色工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ColorHexa",
              "url": "https://www.colorhexa.com/",
              "description": "提供任何颜色的信息并生成匹配的调色板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Color Hunt",
              "url": "https://colorhunt.co/",
              "description": "发现精心挑选的调色板。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Coolors",
              "url": "https://coolors.co/",
              "description": "配色方案生成器和调色板创建工具，允许用户为设计项目探索、创建和分享颜色组合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Colors.lol",
              "url": "https://colors.lol/",
              "description": "提供简单界面生成调色板的网站，具有保存和导出选项，用于数字设计。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "字体",
          "originalName": "Fonts",
          "id": "fonts",
          "websites": [
            {
              "name": "Font Squirrel",
              "url": "https://www.fontsquirrel.com/",
              "description": "免费字体乌托邦。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Font Discovery",
              "url": "https://fontdiscovery.typogram.co/",
              "description": "为创作者、创始人、制作者提供的每周设计、字体和颜色创意新闻通讯。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MyFonts",
              "url": "https://www.myfonts.com/",
              "description": "超过130,000种可用字体，且数量还在增长。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Fonts",
              "url": "https://fonts.google.com/",
              "description": "Google的免费开源字体集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DaFont",
              "url": "https://www.dafont.com/",
              "description": "提供免费字体下载的热门网站，具有各种风格和用途的分类，包括装饰性、手写和无衬线字体。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "UCL Fonts Project",
              "url": "http://vecg.cs.ucl.ac.uk/Projects/projects_fonts/projects_fonts.html",
              "description": "专注于字体流形的研究项目，提供其工作成果和交互式2D字体流形演示。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "图标",
          "originalName": "Icons / Icon Packs",
          "id": "icons---icon-packs",
          "websites": [
            {
              "name": "The Noun Project",
              "url": "https://thenounproject.com/",
              "description": "提供大量免费图标和库存照片的平台，可用于各种项目和设计。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IconPacks",
              "url": "https://www.iconpacks.net/",
              "description": "提供各种图标包供个人和商业使用的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Doodlicons on Notion",
              "url": "https://www.notion.so/Doodlicons-519314a92ed3474093a10e44946bbb72",
              "description": "Notion上项目线框图的涂鸦图标。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Illustration Kit",
              "url": "https://illustrationkit.com/",
              "description": "个人和商业项目免费矢量插图集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FontAwesome",
              "url": "https://fontawesome.com/",
              "description": "互联网图标库和工具包，被设计师、开发者和内容创作者广泛使用。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Iconshock",
              "url": "https://www.iconshock.com/freeicons/",
              "description": "提供来自各种开源集合图标的平台，有10万个图标可免费下载。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "3DIcons",
              "url": "https://3dicons.co/",
              "description": "在CC0许可下可免费商业和个人使用的3D图标集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fontello",
              "url": "https://fontello.com/",
              "description": "创建自定义图标字体的图标字体生成器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HealthIcons",
              "url": "https://healthicons.org/",
              "description": "各种用例的免费开源健康图标。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TablerIcons",
              "url": "https://tablericons.com/",
              "description": "开源免费SVG图标，高度可定制，商业使用无需署名。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "David Li",
              "url": "https://david.li/",
              "description": "基于粒子的3D模拟和渲染平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IcoMoon",
              "url": "https://icomoon.io/",
              "description": "创建和管理图标字体的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "UTF8Icons",
              "url": "https://www.utf8icons.com/",
              "description": "UTF-8标准中Unicode符号的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Free Isometric Illustrations",
              "url": "https://passionhacks.com/free-isometric-illustrations/",
              "description": "各种项目的免费等距插图集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Illlustrations",
              "url": "https://illlustrations.co/",
              "description": "创意项目的开源插图套件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PixelBazaar",
              "url": "https://www.pixelbazaar.com",
              "description": "为有特色的品牌提供有态度的图标。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Iconfinder",
              "url": "https://www.iconfinder.com/",
              "description": "提供图标、插图、3D插图、设计师和免费图标的平台。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Iconz Design",
              "url": "https://iconz.design/",
              "description": "223个图标的高级3D库。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "HugeIcons.pro",
              "url": "https://hugeicons.pro/",
              "description": "提供超过25,000个图标的平台，有5种独特风格，分布在57个热门类别中。",
              "isPaid": true,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "素材图片",
          "originalName": "Stock Images",
          "id": "stock-images",
          "websites": [
            {
              "name": "The Stocks",
              "url": "https://thestocks.im/",
              "description": "聚合器，提供来自各种来源的免费库存照片、视频和音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Pexels",
              "url": "https://www.pexels.com/",
              "description": "提供高质量库存照片和视频免费下载和使用的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Unsplash",
              "url": "https://unsplash.com/",
              "description": "为创意项目提供大量高分辨率免版税图片的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FreeImages",
              "url": "https://www.freeimages.com/",
              "description": "提供多样化免费库存照片供个人或商业使用的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Pixabay",
              "url": "https://pixabay.com/",
              "description": "社区驱动的平台，贡献者分享高质量库存图片、视频和音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PNG Guru",
              "url": "https://www.pngguru.in/",
              "description": "免费PNG图片、背景和模板的来源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Pond5",
              "url": "https://www.pond5.com/free",
              "description": "提供免费库存视频、照片和音乐资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Critter Pics",
              "url": "https://www.critter.pics/",
              "description": "为创意项目提供小动物图片的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Stock Up",
              "url": "https://stockup.sitebuilderreport.com/",
              "description": "索引来自31个不同免费库存照片网站的35,356张照片，便于访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Shutterstock",
              "url": "https://www.shutterstock.com/",
              "description": "提供大量免版税图片、视频、矢量、插图和音乐库的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Zoom.nl",
              "url": "https://zoom.nl/",
              "description": "荷兰最大的摄影社区，为摄影爱好者提供资源和平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Depositphotos",
              "url": "https://depositphotos.com/",
              "description": "拥有2.32亿文件的平台，包括免版税图片、视频、矢量、插图和音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Skuawk",
              "url": "https://skuawk.com/",
              "description": "各种创意项目的公共领域照片来源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "All-Free-Download",
              "url": "https://all-free-download.com/",
              "description": "提供可在个人或商业项目中免费使用的图形艺术的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "壁纸",
          "originalName": "Wallpapers",
          "id": "wallpapers",
          "websites": [
            {
              "name": "Wallpapers.com",
              "url": "https://wallpapers.com/",
              "description": "提供不同主题和风格壁纸的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WallpaperCave",
              "url": "https://wallpapercave.com/",
              "description": "提供各种类别高质量壁纸集合的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wallhaven",
              "url": "https://wallhaven.cc/",
              "description": "拥有大量高分辨率壁纸集合的壁纸社区。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MovieMania",
              "url": "https://www.moviemania.io/phone",
              "description": "手机无文字高分辨率电影壁纸数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WallpaperTip",
              "url": "https://www.wallpapertip.com/",
              "description": "上传和发现免费高清壁纸的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SimpleDesktops",
              "url": "https://simpledesktops.com/browse/",
              "description": "桌面背景的极简壁纸集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WallpaperFlare",
              "url": "https://www.wallpaperflare.com/search?wallpaper=vertical",
              "description": "提供高分辨率垂直壁纸的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Positron Dream",
              "url": "https://www.positrondream.com/wallpapers-all",
              "description": "可下载的抽象壁纸集合。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Startup Growth Calculator",
          "url": "https://growth.tlb.org/",
          "description": "计算您的初创公司增长所需的资金。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Startup Equity Calculator",
          "url": "https://capbase.com/startup-equity-calculator/",
          "description": "基于不同变量为您的初创公司分配股权。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Fifty Years Progress Map",
          "url": "https://progress.fiftyyears.com/",
          "description": "突出显示低初创投资的大型市场。提供按竞争力排名的大规模市场列表，包括市场规模和过去10年A轮初创公司的总投资。为每个市场计算竞争比率。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Museum of Websites",
          "url": "https://www.kapwing.com/museum-of-websites",
          "description": "展示著名互联网公司如何随时间变化的画廊。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Goal Examples",
          "url": "https://hypercontext.com/goal-examples",
          "description": "为技术领域每个角色精心策划的目标示例列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Startup Resources",
          "url": "https://www.feedough.com/startup-resources/",
          "description": "为初创公司分类的资源集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "500+ Free Tools For Startups",
          "url": "https://docs.google.com/spreadsheets/d/1s6-hGBh0_tqa-jd23fsdYuwbmS8UPmElPqaH-Rnoa_A/htmlview",
          "description": "初创公司免费工具的综合列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Founder Resources",
          "url": "https://www.founderresources.io/",
          "description": "为初创公司提供的免费策划资源、模板和工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "100+ Resources on GPT-3",
          "url": "https://harishgarg.gumroad.com/l/wiSvc?ref=producthunt",
          "description": "100多个GPT-3资源的策划列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "100+ Resources for Building a Successful Startup",
          "url": "https://cerdeira.notion.site/b3b5f44d37cf4843b3fcd2f300354467?v=8f3458522f4542d8896ebb3720c14b2d",
          "description": "构建成功初创公司的资源汇编。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "2,500 Accelerators Incubators",
          "url": "https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fattachments.convertkitcdnn2.com%2F587796%2F16ab0442-1232-4a49-956c-acafd6df4189%2F2%2C500%2520Accelerators%2520Incubators.xlsx&wdOrigin=BROWSELINK",
          "description": "2,500个加速器和孵化器的列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "The Complete Unicorn List",
          "url": "https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fattachments.convertkitcdnn2.com%2F587796%2F476863f4-bda0-4132-8bd0-d25728513cfd%2FThe%2520Complete%2520Unicorn%2520List.xlsx&wdOrigin=BROWSELINK",
          "description": "1,016个独角兽公司（估值超过10亿美元的私人公司）的综合列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "GitHub Email Hunter",
          "url": "https://chrome.google.com/webstore/detail/github-email-hunter/ppcegaekdbgcgbapfdcjbhednhmgcjnk",
          "description": "一键查找GitHub用户和仓库的电子邮件地址。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "The Angel Philosopher",
          "url": "https://theangelphilosopher.com/",
          "description": "Naval智慧、知识和思想的汇编。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Under Glass",
          "url": "https://underglass.io/",
          "description": "对世界最佳数字产品的分析。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "The Perfect Pitch Deck",
          "url": "https://attachments.convertkitcdnn2.com/587796/1e723803-ab50-4a61-9b3a-f347aa436408/The%20Perfect%20Pitch%20Deck.pdf",
          "description": "从分析350多个初创公司路演稿中得出的经验教训。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Old Computers Museum",
          "url": "https://oldcomputers.net/",
          "description": "探索拥有150件展品的老式和复古计算机博物馆。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Startups List",
          "url": "https://www.startups-list.com/",
          "description": "不同地方最佳初创公司的集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Pessimists Archive",
          "url": "https://pessimistsarchive.org/",
          "description": "唤醒我们对新技术、想法和趋势常常引起的歇斯底里、技术恐惧症和道德恐慌的集体记忆的项目。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "艺术",
      "originalName": "Art",
      "id": "art",
      "subcategories": [
        {
          "name": "摄影",
          "originalName": "Photography",
          "id": "photography",
          "websites": [
            {
              "name": "Cambridge in Colour",
              "url": "https://www.cambridgeincolour.com/",
              "description": "摄影师学习社区",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Exposure Guide",
              "url": "https://www.exposureguide.com/",
              "description": "摄影技巧、技术和教程",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "艺术社区",
          "originalName": "Art Communities",
          "id": "art-communities",
          "websites": [
            {
              "name": "Ello",
              "url": "https://ello.co/discover",
              "description": "",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Behance",
              "url": "https://www.behance.net/",
              "description": "",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ArtStation",
              "url": "https://www.artstation.com/",
              "description": "",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "WeavesSilk",
          "url": "https://weavesilk.com/",
          "description": "使用Silk创建美丽流动的艺术作品。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Google Arts & Culture",
          "url": "https://artsandculture.google.com/",
          "description": "将世界艺术和文化在线呈现给每个人的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ArtGraphica",
          "url": "https://www.artgraphica.net/",
          "description": "免费绘画、素描和绘画技巧的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tattoos Wizard",
          "url": "https://tattooswizard.com/",
          "description": "寻找您附近的纹身艺术家和工作室。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "The Art Institute of Chicago Collection",
          "url": "https://www.artic.edu/collection",
          "description": "探索博物馆收藏的数千件艺术品。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ZoomQuilt",
          "url": "https://zoomquilt.org/",
          "description": "协作无限缩放绘画。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Invaluable",
          "url": "https://www.invaluable.com/",
          "description": "世界顶级在线拍卖平台，每日添加数千件拍品。Invaluable为随时随地发现和获得卓越艺术品和物品提供便利。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "50 Watts",
          "url": "https://50watts.com/",
          "description": "来自世界各地奇异精彩视觉短篇作品的档案",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "学术",
      "originalName": "Academia",
      "id": "academia",
      "subcategories": [
        {
          "name": "学习",
          "originalName": "Studying",
          "id": "studying",
          "websites": [
            {
              "name": "Bartleby",
              "url": "https://www.bartleby.com/",
              "description": "搜索教科书、作业问题的逐步解释等内容的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Chegg",
              "url": "https://www.chegg.com/",
              "description": "提供24/7课程帮助的服务，包括教科书解答和专家问答。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Chegg Flashcards",
              "url": "https://www.chegg.com/flashcards",
              "description": "使用学生和专家为各种课程创建的闪卡学习。",
              "isPaid": false,
              "isStudentFriendly": true
            }
          ]
        },
        {
          "name": "计算器",
          "originalName": "Calculators",
          "id": "calculators",
          "websites": [
            {
              "name": "Calc Resource",
              "url": "https://calcresource.com/index.html",
              "description": "提供各种计算器和数学计算资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "eFunda",
              "url": "https://www.efunda.com/home.cfm",
              "description": "提供计算器、公式以及材料和工艺信息的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LCM Calculator",
              "url": "https://www.calculator.net/lcm-calculator.html",
              "description": "查找最小公倍数的计算器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GCF Calculator",
              "url": "https://www.calculator.net/gcf-calculator.html?numberinputs=9%2C+57%2C+72&x=75&y=22",
              "description": "查找最大公因数的计算器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CalculatorSoup",
              "url": "https://www.calculatorsoup.com/",
              "description": "为不同数学目的提供各种计算器的在线平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RapidTables",
              "url": "https://www.rapidtables.com/",
              "description": "提供计算器和表格集合供快速参考的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Linear Algebra Calculator",
              "url": "https://www.emathhelp.net/en/linear-algebra-calculator/?u=3%2C1%2C4&v=-2%2C0%2C5&action=cross+product",
              "description": "线性代数计算器，如叉积。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wikipedia: List of Physical Quantities",
              "url": "https://en.wikipedia.org/wiki/List_of_physical_quantities",
              "description": "列出各种物理量的Wikipedia页面。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "eMathHelp Linear Algebra Calculator",
              "url": "https://www.emathhelp.net/en/calculators/linear-algebra/",
              "description": "线性代数计算的在线计算器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Online Math School",
              "url": "https://onlinemschool.com/math/assistance/",
              "description": "提供数学帮助以及各种计算器和资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "在线课程",
          "originalName": "MOOC (Massive Open Online Courses)",
          "id": "mooc--massive-open-online-courses-",
          "websites": [
            {
              "name": "InfoCobuild - Audio Video Courses",
              "url": "http://www.infocobuild.com/education/audio-video-courses/",
              "description": "来自世界各地学院和大学免费音频/视频学术课程讲座的集合。按学术科目进行良好分类，包括生物学、化学、计算机科学、经济学、电子和电气工程、历史、文学、材料科学、数学、物理学和心理学。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MIT OpenCourseWare",
              "url": "https://ocw.mit.edu/",
              "description": "MIT的倡议，提供对广泛课程材料的免费开放访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Coursera",
              "url": "https://www.coursera.org/",
              "description": "在线学习平台，提供来自世界各地大学和组织的课程、证书和学位项目。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wikiversity",
              "url": "https://en.wikiversity.org/wiki/Wikiversity:Main_Page",
              "description": "维基媒体基金会项目，提供免费的教育内容和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Udemy",
              "url": "https://www.udemy.com/",
              "description": "平台提供由专家教授的各种主题的大量在线课程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Culture",
              "url": "https://www.openculture.com/",
              "description": "提供免费文化和教育媒体的网站，包括课程、教科书和有声读物。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "edX",
              "url": "https://www.edx.org/",
              "description": "在线学习平台，提供来自世界各地大学和机构的课程、证书和学位。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Udacity",
              "url": "https://www.udacity.com/",
              "description": "专注于技术相关课程和与行业领导者合作设计的纳米学位项目的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Stanford Online",
              "url": "https://online.stanford.edu/",
              "description": "斯坦福大学的在线学习平台，提供各种课程和项目。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OpenLearn",
              "url": "https://www.open.edu/openlearn/",
              "description": "开放大学的倡议，提供对课程材料和教育资源的免费访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Learning Initiative (OLI)",
              "url": "https://oli.cmu.edu/",
              "description": "卡内基梅隆大学的平台，提供公开可用的课程和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MITx",
              "url": "https://www.mitx.org/",
              "description": "MIT的平台，提供专注于前沿研究的在线课程和项目。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Yale Courses",
              "url": "https://oyc.yale.edu/",
              "description": "耶鲁大学的倡议，提供由杰出教师教授的入门课程的免费访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Alison",
              "url": "https://alison.com/",
              "description": "平台提供各种主题的免费在线课程和文凭。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Academic Earth",
              "url": "https://academicearth.org/",
              "description": "聚合来自世界顶级大学在线课程的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "NPTEL",
              "url": "https://nptel.ac.in/",
              "description": "国家技术增强学习计划，提供工程和科学在线课程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "University of the People",
              "url": "https://www.uopeople.edu/",
              "description": "在线大学，提供免学费的认证学位项目。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Canvas Network",
              "url": "https://www.canvas.net/",
              "description": "使用Canvas学习管理系统提供来自各种机构的在线课程和项目的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Isaac Newton Institute for Mathematical Sciences",
              "url": "https://www.newton.ac.uk/",
              "description": "研究所网站，提供数学科学资源和项目的访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Saylor Academy",
              "url": "https://www.saylor.org/",
              "description": "非营利倡议，提供免费的自定进度在线课程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Connexions",
              "url": "https://cnx.org/",
              "description": "提供各种学科领域开放教育资源和教科书的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Directory of Open Access Journals (DOAJ)",
              "url": "https://doaj.org/",
              "description": "在线目录，提供对高质量开放获取科学和学术期刊的访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learning on the Internet",
              "url": "https://www.learningontheinternet.com/?ref=producthunt",
              "description": "展示来自各种来源的精选教育内容和资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Class Central",
              "url": "https://www.classcentral.com/",
              "description": "在线课程的搜索引擎和评论平台，聚合来自各种提供商的课程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OCW SNU",
              "url": "https://ocw.snu.ac.kr/",
              "description": "首尔国立大学的开放课件。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "TLDR This",
          "url": "https://tldrthis.com/",
          "description": "将任何文本总结为简洁易懂内容的平台，帮助用户克服信息过载。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Archive.org General Index",
          "url": "https://archive.org/details/GeneralIndex",
          "description": "提供访问超过1.07亿篇期刊文章的综合索引。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Homework Help Global",
          "url": "https://www.homeworkhelpglobal.com/",
          "description": "提供专业和定制论文写作服务的在线平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Dartmouth Academic Careers",
          "url": "https://sites.dartmouth.edu/nyhan/academic-careers/",
          "description": "提供学术职业见解的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CNX",
          "url": "https://cnx.org/",
          "description": "查看和分享免费教育材料的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Reach Out Michigan Tutorials",
          "url": "https://www.reachoutmichigan.org/learn/tutorials.html#math",
          "description": "涵盖各种主题的在线教程和参考资料集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Open Text BC",
          "url": "https://opentextbc.ca/",
          "description": "简单的图书制作软件，允许用户发布教科书、学术专著、教学大纲、小说和非小说书籍、白皮书等多种格式。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Modern for Wikipedia",
          "url": "https://chrome.google.com/webstore/detail/modern-for-wikipedia/emdkdnnopdnajipoapepbeeiemahbjcn",
          "description": "通过现代化可定制设计增强Wikipedia体验的Chrome扩展。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Wikiwand - Wikipedia Modern",
          "url": "https://chrome.google.com/webstore/detail/wikiwand-wikipedia-modern/emffkefkbkpkgpdeeooapgaicgmcbolj",
          "description": "优化Wikipedia内容以改善阅读体验的Chrome扩展。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "List of Academic Databases and Search Engines",
          "url": "https://en.wikipedia.org/wiki/List_of_academic_databases_and_search_engines",
          "description": "列出学术数据库和搜索引擎的Wikipedia页面。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Scribbr APA Citation Generator",
          "url": "https://www.scribbr.com/citation/generator/apa/",
          "description": "提供准确APA引文的平台，经专家验证，受数百万人信任。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Bridges: About Institutions, Histories, and Artifacts",
          "url": "https://temple.manifoldapp.org/projects/bridges",
          "description": "关于美国学院和大学生活的机构、历史和文物的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tropy",
          "url": "https://tropy.org/",
          "description": "通过将照片转化为物品来组织研究的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Linda Hall Library Catalog",
          "url": "https://catalog.lindahall.org/discovery/search?vid=01LINDAHALL_INST:LHL",
          "description": "Linda Hall图书馆目录，允许您搜索书籍、期刊、会议论文集、技术报告和标准以及其他材料，专注于科学、工程和技术。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Project Abstracts",
          "url": "https://projectabstracts.com/",
          "description": "各个领域学术小项目和毕业项目的项目摘要和下载集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "SCIRP Open Access Journal",
          "url": "https://www.scirp.org/journal/OpenAccess",
          "description": "提供各种科学学科开放获取学术期刊的平台，促进免费获取研究。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "DOI.org",
          "url": "https://www.doi.org/",
          "description": "数字对象标识符(DOI)的官方网站，提供数字资源（包括学术论文和数据集）的持久标识和访问系统。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "科学",
      "originalName": "Science",
      "id": "science",
      "subcategories": [
        {
          "name": "传记",
          "originalName": "Biographies",
          "id": "biographies",
          "websites": [
            {
              "name": "Mathematics History",
              "url": "https://mathshistory.st-andrews.ac.uk/",
              "description": "包含3000多位数学家传记和2000多页论文及支持材料的免费在线资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Web of Stories",
              "url": "https://www.webofstories.com/",
              "description": "聆听我们时代一些伟大人物的生活故事。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Letters of Note",
              "url": "https://lettersofnote.com/",
              "description": "历史上最迷人的信件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Organism Earth Library",
              "url": "https://www.organism.earth/library/",
              "description": "生物地球图书馆。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Darwin Project",
              "url": "https://www.darwinproject.ac.uk/",
              "description": "进化科学家查尔斯·达尔文（1809-1882）写的信件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Darwin Online",
              "url": "https://darwin-online.org.uk/",
              "description": "世界上最大最广泛使用的查尔斯·达尔文资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Newton Project",
              "url": "https://www.newtonproject.ox.ac.uk/",
              "description": "艾萨克·牛顿爵士（1642-1727）所有著作的在线版本。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bethe",
              "url": "https://bethe.cornell.edu/index.html",
              "description": "汉斯·贝特的个人和历史观点。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Source Shakespeare",
              "url": "https://www.opensourceshakespeare.org/",
              "description": "开源莎士比亚。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Leonardo da Vinci",
              "url": "https://www.leonardodavinci.net/",
              "description": "列奥纳多·达·芬奇，他的生活和艺术作品。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Our Karl Popper",
              "url": "https://ourkarlpopper.net/",
              "description": "卡尔·波普尔如何改变了我们的生活（来自五大洲的证词）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Varlam Shalamov",
              "url": "https://shalamov.ru/en/",
              "description": "瓦尔拉姆·沙拉莫夫的著作和历史背景，这位俄国作家以其关于苏联劳改营监禁的短篇小说系列而闻名。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Samuel Beckett On-Line Resources",
              "url": "https://www.samuel-beckett.net/",
              "description": "塞缪尔·贝克特在线资源和链接页面。（塞缪尔·贝克特的《[等待戈多](https://www.samuel-beckett.net/Waiting_for_Godot_Part1.html)》）",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Philip K. Dick",
              "url": "https://philipdick.com/",
              "description": "致力于科幻作家菲利普·K·迪克（1928-82）的生活和作品。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Alan Turing Digital Archive",
              "url": "https://turingarchive.kings.cam.ac.uk/",
              "description": "这个数字档案包含图灵的许多信件、谈话记录、照片和未发表的论文。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Turing.org.uk",
              "url": "https://www.turing.org.uk/",
              "description": "致力于数学家和计算机科学家艾伦·图灵的生活和工作的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Sherlock Holmes Series",
              "url": "https://sherlock-holm.es/",
              "description": "阿瑟·柯南·道尔爵士的夏洛克·福尔摩斯系列（公有领域，免版权）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Feynman Lectures Online",
              "url": "https://www.feynmanlectures.caltech.edu/",
              "description": "理查德·费曼的在线讲座。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Leibniz Translations",
              "url": "https://www.leibniz-translations.com/index2.php",
              "description": "提供哲学家和数学家戈特弗里德·威廉·莱布尼茨作品翻译的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "David Hume",
              "url": "https://davidhume.org/",
              "description": "致力于苏格兰哲学家大卫·休谟的平台。准确、有用地呈现休谟几乎所有著作。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fooled by Randomness",
              "url": "https://fooledbyrandomness.com/",
              "description": "纳西姆·尼古拉斯·塔勒布的主页。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Prabook",
              "url": "https://prabook.com/web/home.html/",
              "description": "提供著名人物传记信息的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Famous Mathematicians",
              "url": "https://famous-mathematicians.org/",
              "description": "展示著名数学家信息和传记的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "书籍文章",
          "originalName": "Books, Articles, Texts",
          "id": "books--articles--texts",
          "websites": [
            {
              "name": "Archive.org/texts",
              "url": "https://archive.org/details/texts",
              "description": "提供对大量数字内容集合免费通用访问的数字图书馆。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Library",
              "url": "https://openlibrary.org/",
              "description": "包含图书元数据的通用目录，可访问广泛的数字图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OpenStax",
              "url": "https://openstax.org/",
              "description": "为教育目的提供免费灵活的教科书和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Project Gutenberg",
              "url": "https://www.gutenberg.org/",
              "description": "提供超过60,000本免费电子书（包括许多经典作品）的数字图书馆。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wikibooks",
              "url": "https://en.wikibooks.org/wiki/Main_Page",
              "description": "任何人都可以编辑的开放内容教科书集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wikisource",
              "url": "https://wikisource.org/wiki/Main_Page",
              "description": "允许协作改进其内容的免费图书馆。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MIT Classics",
              "url": "https://classics.mit.edu/",
              "description": "从59位不同作者的441部经典文学作品列表中选择。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Goodreads Free eBooks",
              "url": "https://www.goodreads.com/ebooks?sort=readable",
              "description": "Goodreads上提供的免费图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Lit2Go",
              "url": "https://etc.usf.edu/lit2go/",
              "description": "免费在线故事和诗歌有声读物集合（Mp3格式）。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Booksc",
              "url": "https://booksc.org/",
              "description": "世界最大的科学文章存储库，包含7000万+免费文章。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IntechOpen",
              "url": "https://www.intechopen.com/books",
              "description": "阅读、分享和下载超过5,800本同行评审的开放获取图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FreeTechBooks",
              "url": "https://www.freetechbooks.com/",
              "description": "免费/开放获取在线计算机科学图书、教科书和讲义的数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FreeComputerBooks",
              "url": "https://freecomputerbooks.com/",
              "description": "提供免费计算机科学和编程图书集合的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Manning",
              "url": "https://www.manning.com/",
              "description": "编程图书出版商。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Holy Books",
              "url": "https://holybooks.com/",
              "description": "下载免费PDF格式的精神文本电子书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Librivox",
              "url": "https://librivox.org/",
              "description": "提供来自世界各地志愿者朗读的免费公有领域有声读物。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Online Books Page",
              "url": "https://onlinebooks.library.upenn.edu/",
              "description": "网络上超过300万本免费图书的列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Audible",
              "url": "https://www.audible.com/",
              "description": "提供优质音频故事讲述的平台，拥有广泛的有声读物选择。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "VCU Transcendentalism",
              "url": "https://archive.vcu.edu/english/engweb/transcendentalism/",
              "description": "关于先验主义文本的教育超文本空间，链接到其他互联网空间。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Library of Short Stories",
              "url": "https://www.libraryofshortstories.com/",
              "description": "在线图书馆，包含超过1000个经典短篇小说，可供阅读和下载。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SlideShare",
              "url": "https://www.slideshare.net/",
              "description": "分享演示文稿和文档的在线平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Free-eBooks.net",
              "url": "https://www.free-ebooks.net/",
              "description": "发现数百个虚构和非虚构类别中的数千位新作者。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "University of Pennsylvania Digital Library",
              "url": "https://digital.library.upenn.edu/books/",
              "description": "提供图书集合访问的数字图书馆。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Feedbooks Public Domain",
              "url": "https://www.feedbooks.com/catalog/public_domain",
              "description": "免费提供公有领域图书目录的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Authorama",
              "url": "https://www.authorama.com/",
              "description": "提供不同作者公有领域图书集合的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Play - Top Selling Free Books",
              "url": "https://play.google.com/store/books/collection/topselling_free?clp=ChcKFQoPdG9wc2VsbGluZ19mcmVlEAcYAQ%3D%3D:S:ANO1ljKuey8&gsr=ChkKFwoVCg90b3BzZWxsaW5nX2ZyZWUQBxgB:S:ANO1ljIbX7M",
              "description": "Google Play上最畅销免费图书集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Taoism.net",
              "url": "https://taoism.net/",
              "description": "探索道教的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Early Modern Texts",
              "url": "https://earlymoderntexts.com/",
              "description": "提供早期现代哲学文本现代英语译本的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Online Library of Liberty",
              "url": "https://oll.libertyfund.org/",
              "description": "涉及自由核心问题的学术作品精选集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Online Books Library",
              "url": "https://onlinebooks.library.upenn.edu/",
              "description": "宾夕法尼亚大学的在线图书馆，提供对网络上300万本免费图书的访问。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Elegant eBooks",
              "url": "https://www.ibiblio.org/ebooks/",
              "description": "以时尚版本查找优秀的虚构和非虚构经典作品。本网站上几乎所有电子书都来自公有领域图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "22 Free Data Science Books",
              "url": "https://www.wzchen.com/data-science-books",
              "description": "精选的免费优质数据科学图书汇编，帮助探索数据科学职业道路的人士。每本书的最后更新日期包含在括号中。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Chest of Books",
              "url": "https://chestofbooks.com/",
              "description": "免费在线图书馆，提供各种主题的大量图书集合，包括科学、技术、艺术和文学。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Stephen Wolfram: A New Kind of Science",
              "url": "https://www.wolframscience.com/nks/",
              "description": "史蒂芬·沃尔夫拉姆《一种新的科学》图书的在线版本，提供目录和细胞自动机与复杂性相关材料的访问。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "书籍推荐",
          "originalName": "Book Recommendations and Summaries",
          "id": "book-recommendations-and-summaries",
          "websites": [
            {
              "name": "Read Next",
              "url": "https://read-next.com/",
              "description": "来自科学家、投资者、企业家、名人和作者等各个领域名人推荐的3,000+本图书集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Goodbooks",
              "url": "https://www.goodbooks.io/",
              "description": "提供来自世界各地成功有趣人士的8,500+本图书推荐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Most Recommended Books",
              "url": "https://mostrecommendedbooks.com/",
              "description": "策展500+专家、600+列表、500+图书系列，提供100%验证的图书推荐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Books Chatter",
              "url": "https://bookschatter.com/",
              "description": "从人们的推文中寻找图书推荐，显示相关推文。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Leafmarks",
              "url": "https://www.leafmarks.com/",
              "description": "探索来自著名作者、顶级CEO、传奇投资者和喜爱名人的图书推荐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bookstash",
              "url": "https://bookstash.io/",
              "description": "展示名人推荐的顶级图书，3分钟或更短时间内总结。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Abakcus",
              "url": "https://abakcus.com/books/",
              "description": "关于数学和一些科学的图书集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "BookBub",
              "url": "https://www.bookbub.com/welcome",
              "description": "根据您的偏好获得个性化图书推荐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hacker News Books",
              "url": "https://hackernewsbooks.com/",
              "description": "每周策展Hacker News上提到的最佳图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Goodreads",
              "url": "https://www.goodreads.com/",
              "description": "世界最大的读者和图书推荐网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "What Should I Read Next?",
              "url": "https://www.whatshouldireadnext.com/",
              "description": "输入您喜欢的图书，网站将分析我们庞大的真实读者最爱图书数据库，为您提供图书推荐和下一步阅读建议。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Blas",
              "url": "https://blas.com/",
              "description": "Blas Moros总结的超过400本图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google Books - Talk to Books",
              "url": "https://books.google.com/talktobooks/",
              "description": "允许用户使用自然语言与图书对话的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "地图数据",
          "originalName": "Maps and Data",
          "id": "maps-and-data",
          "websites": [
            {
              "name": "Our World in Data",
              "url": "https://ourworldindata.org/covid-vaccinations",
              "description": "按国家分类的COVID-19疫苗接种数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WebSDR",
              "url": "https://websdr.org/",
              "description": "连接到互联网的软件定义无线电接收器，允许众多听众同时收听和调谐，捕获来自地球的当前信号。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GSM Security Map",
              "url": "https://gsmmap.org/",
              "description": "比较移动网络在GSM安全方面保护能力的地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "International Campuses",
              "url": "https://cbert.org/resources-data/intl-campus/",
              "description": "国际校园列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "United States International College Campuses on Google Maps",
              "url": "https://www.google.com/maps/d/u/0/viewer?mid=1ckm_TSM8mbCrnhA8COpBNG-qJqTR2IW3&ll=43.664774893391936%2C24.56718262638249&z=5",
              "description": "展示美国国际大学校园的地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Submarine Cable Map",
              "url": "https://www.submarinecablemap.com/",
              "description": "展示世界海底电缆系统的交互式地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Observable",
              "url": "https://observablehq.com/",
              "description": "协作探索、分析和解释数据的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FixPhrase",
              "url": "https://fixphrase.com/",
              "description": "仅用四个词定位地球上任何地方，适用于处理几个词比一长串数字更方便的情况。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Common Crawl",
              "url": "https://commoncrawl.org/",
              "description": "任何人都可以访问和分析的开放网络爬取数据存储库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mount Everest 3D Map",
              "url": "https://mount-everest3d.com/3d-map/",
              "description": "珠穆朗玛峰的交互式3D地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visualize Value Archive",
              "url": "https://archivve.visualizevalue.com/",
              "description": "Visualize Value的视觉内容档案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Fliist",
              "url": "https://fliist.com/en",
              "description": "创建和分享您最喜爱列表的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Search Engine Map",
              "url": "https://www.searchenginemap.com/",
              "description": "流行搜索引擎的视觉表示。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Friendly Dubinsky",
              "url": "https://friendly-dubinsky-cb22fe.netlify.app/",
              "description": "不同地方的地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Real-Time SpaceX Starlink Satellite Tracker",
              "url": "https://www.starlinkmap.org/",
              "description": "提供SpaceX星链卫星实时跟踪器的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Album of Computational Fluid Motion",
              "url": "https://album-of-cfm.com/",
              "description": "展示计算流体运动图像的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visualization of Every Job Title in the World",
              "url": "https://duarteocarmo.com/blog/every-job-world",
              "description": "将世界上每个职位名称分类为10个类别的交互式可视化。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ObservableHQ",
              "url": "https://observablehq.com/@tophtucker/examples-of-bitemporal-charts",
              "description": "双时态图表示例",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "The Uncensored Library",
              "url": "https://uncensoredlibrary.com/en",
              "description": "由无国界记者发布、BlockWorks、DDB Berlin和MediaMonks创建的Minecraft服务器和地图，旨在规避新闻自由受限国家的审查制度",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Lighthouse Map",
              "url": "https://geodienst.github.io/lighthousemap/",
              "description": "显示世界各地灯塔位置的交互式地图，为每个地点提供地理空间信息和历史。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Arxiv",
          "originalName": "Arxiv",
          "id": "arxiv",
          "websites": [
            {
              "name": "ArxivXplorer",
              "url": "https://arxivxplorer.com/",
              "description": "专为探索arXiv科学论文而设计的搜索工具，允许用户查找和筛选各学科的研究文章。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Interactive Data Map of ArXiv Machine Learning Papers",
              "url": "https://datamapplot.readthedocs.io/en/latest/auto_examples/plot_interactive_arxiv_ml.html",
              "description": "显示ArXiv机器学习部分论文的交互式数据地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ArXiv Machine Learning Landscape",
              "url": "https://lmcinnes.github.io/datamapplot_examples/ArXiv_data_map_example.html",
              "description": "展示ArXiv机器学习研究景观的可视化。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DataMapPlot Examples",
              "url": "https://lmcinnes.github.io/datamapplot_examples/arXiv/",
              "description": "使用DataMapPlot库在流形上可视化数据的示例和演示集合，专注于arXiv数据集。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "信息图表",
          "originalName": "Infographic",
          "id": "infographic",
          "websites": [
            {
              "name": "Information is Beautiful",
              "url": "https://informationisbeautiful.net/",
              "description": "专注于基于事实和数据决策的平台，通过视觉吸引的图形呈现信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visual Capitalist",
              "url": "https://www.visualcapitalist.com/",
              "description": "数据驱动的视觉内容来源，涵盖市场、技术、能源和全球经济等各种主题。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Infographic Journal",
              "url": "https://infographicjournal.com/",
              "description": "涵盖广泛主题的信息图表档案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "D3.js",
              "url": "https://d3js.org/",
              "description": "基于数据操作文档的JavaScript库，能够创建动态和交互式数据可视化。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Deniz Cem On Duygu Portfolio",
              "url": "https://www.denizcemonduygu.com/",
              "description": "Deniz Cem On Duygu的信息图表个人作品集。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Data Visualization Catalogue",
              "url": "https://datavizcatalogue.com/index.html",
              "description": "提供各种类型数据可视化信息的目录。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Tableau Public",
              "url": "https://public.tableau.com/app/discover",
              "description": "免费平台，可在线探索、创建和公开分享数据可视化。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Will Robots Take My Job?",
              "url": "https://willrobotstakemyjob.com/",
              "description": "估算各种职业的自动化风险、增长、工资等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Preceden",
              "url": "https://www.preceden.com/",
              "description": "在线时间线和路线图制作工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Jason Davies",
              "url": "https://www.jasondavies.com/",
              "description": "Jason Davies的个人网站，展示他的项目和可视化作品。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "哲学",
          "originalName": "Philosophy",
          "id": "philosophy",
          "websites": [
            {
              "name": "Desolhar Philo",
              "url": "https://www.desolhar-philo.com/",
              "description": "关于哲学的笔记。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "VisualizingSEP",
              "url": "https://www.visualizingsep.com/",
              "description": "探索斯坦福哲学百科全书的交互式可视化和搜索引擎。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hakob's Sandbox",
              "url": "https://hakobsandbox.openetext.utoronto.ca/",
              "description": "关于科学史和科学哲学入门的在线图书。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Philosophy A Level",
              "url": "https://philosophyalevel.com/",
              "description": "A-Level哲学学习资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Deniz Cemon Duygu - History of Philosophy",
              "url": "https://www.denizcemonduygu.com/philo/browse/",
              "description": "总结和可视化的哲学史。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Interactive timeline of philosophical ideas",
              "url": "https://www.denizcemonduygu.com/portfolio/the-history-of-philosophy/",
              "description": ". - 重要哲学家及其思想的视觉表示和探索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Beyng",
              "url": "https://www.beyng.com/",
              "description": "致力于马丁·海德格尔哲学的资源，提供他的作品和思想的英文译本和讨论。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Greg Egan's Official Website",
              "url": "https://www.gregegan.net/",
              "description": "科幻作家和计算机程序员Greg Egan的作品、哲学和项目信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ayn Rand Institute Courses",
              "url": "https://courses.aynrand.org/",
              "description": "提供安·兰德哲学免费课程的教育平台，包括客观主义及其在生活和社会中的应用。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "社会科学",
          "originalName": "Social Sciences",
          "id": "social-sciences",
          "websites": [
            {
              "name": "Temple Manifold - All Projects",
              "url": "https://temple.manifoldapp.org/projects/all",
              "description": "各种社会科学的优秀资源集合。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "历史",
          "originalName": "History",
          "id": "history",
          "websites": [
            {
              "name": "Histography",
              "url": "https://histography.io/",
              "description": "跨越140亿年历史（从大爆炸到2015年）的交互式时间线。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Unenumerated Blog",
              "url": "https://unenumerated.blogspot.com/",
              "description": "涵盖各种历史主题的全面详细博客。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Human Origins - Human Family Tree",
              "url": "https://humanorigins.si.edu/evidence/human-family-tree",
              "description": "探索人类进化史的交互式人类家谱。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OldEra Timeline",
              "url": "https://timeline.oldera.org/",
              "description": "允许您在可缩放界面上查看整个历史的交互式时间线。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Nuclear Secrecy Blog",
              "url": "https://blog.nuclearsecrecy.com/",
              "description": "关于核机密的历史信息，深入探讨核武器的发展和影响。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "The Ascent of Humanity",
              "url": "https://ascentofhumanity.com/",
              "description": "探索分离时代、重聚时代的概念，以及塑造人类历史转型的危机汇聚。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Today in Science History",
              "url": "https://todayinsci.com/",
              "description": "提供特定日期历史事件的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Khipu Field Guide",
              "url": "https://khipufieldguide.com/guidebook/Introduction.html",
              "description": "提供奇普（古印加结绳记录系统）信息的指南手册。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hellenistic History",
              "url": "https://www.hellenistichistory.com/",
              "description": "希腊化历史研究资源，涵盖从亚历山大大帝时代到罗马帝国的政治、文化和社会发展。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Perseus Digital Library",
              "url": "https://www.perseus.tufts.edu/hopper/",
              "description": "提供大量古典文本、图像和资源，用于研究古希腊和古罗马文化。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Futility Closet",
              "url": "https://www.futilitycloset.com/",
              "description": "历史、文学、语言、艺术、哲学和数学中有趣奇事的集合，旨在帮助您尽可能愉快地消磨时间。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "地球科学",
          "originalName": "Geoscience",
          "id": "geoscience",
          "websites": [
            {
              "name": "River Runner Global",
              "url": "https://river-runner-global.samlearner.com/",
              "description": "提供从世界任何地点的雨滴到其终点路径的可视化。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Celestrak Satellite Visualization",
              "url": "https://celestrak.com/cesium/orbit-viz.php?tle=/pub/TLE/catalog.txt&satcat=/pub/satcat.txt&referenceFrame=1",
              "description": "提供卫星可视化工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mars Now",
              "url": "https://mars.nasa.gov/explore/mars-now/",
              "description": "显示火星卫星的当前位置，为火星探索提供见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Physical Geology",
              "url": "https://temple.manifoldapp.org/projects/physical-geology",
              "description": "涵盖物理地质学的基本主题。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Flood Site",
              "url": "https://floodsite.net/juniorfloodsite/",
              "description": "关于洪水的教育资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "NEMO - Nucleus for European Modeling of the Ocean",
              "url": "https://www.nemo-ocean.eu/",
              "description": "由欧洲联盟开发的平台，提供与多种空间和时间尺度海洋建模相关的信息和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "USGS Earthquake Map",
              "url": "https://earthquake.usgs.gov/earthquakes/map/?extent=25.56227,4.08691&extent=45.08904,70.00488&list=false",
              "description": "美国地质调查局显示最近地震活动的地图。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LatLong.net",
              "url": "https://www.latlong.net/",
              "description": "在地图上查找任何位置纬度和经度坐标的工具，提供地理坐标和相关数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Latitude.to",
              "url": "https://latitude.to/",
              "description": "允许用户查找世界任何地址或位置的GPS坐标，具有详细的地图功能。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "生物学",
          "originalName": "Biology",
          "id": "biology",
          "websites": [
            {
              "name": "Sectional Anatomy",
              "url": "https://www.sectional-anatomy.org/",
              "description": "放射学横断面解剖学的免费在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Deep Sea",
              "url": "https://neal.fun/deep-sea/",
              "description": "探索海洋深处的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ask Nature",
              "url": "https://asknature.org/",
              "description": "生物学启发的策略、创新或教育资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "We Are Hosts for Memes",
              "url": "https://wearehostsformemes.com/",
              "description": "您已接触到元迷因。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Birdwatching Zone",
              "url": "https://birdwatching.zone/",
              "description": "专门用于观鸟的网站，记录了超过300种鸟类，为爱好者提供识别技巧和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OneZoom",
              "url": "https://www.onezoom.org/",
              "description": "生命之树，展示地球上所有生命如何连接。探索220万个物种之间的关系，查看超过10万张图像，了解物种如何从共同祖先进化而来。通过树状结构缩放，发现地球上生命的多样性。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Reproducibility Institute",
          "url": "https://reproducibilityinstitute.org/w/",
          "description": "提供学术论文、速查表和付费课程种子的项目。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "EBSCO",
          "url": "https://www.ebsco.com/",
          "description": "研究数据库、电子期刊、杂志订阅、电子书和发现服务的提供商。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Zooniverse",
          "url": "https://www.zooniverse.org/",
          "description": "参与真实研究，拥有超过50个活跃的在线公民科学项目。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Experiment",
          "url": "https://experiment.com/",
          "description": "帮助资助下一波科学研究。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Closer to Truth",
          "url": "https://www.closertotruth.com/",
          "description": "Robert Lawrence Kuhn探索宇宙的基本问题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Nature Scitable",
          "url": "https://www.nature.com/scitable/",
          "description": "科学概述图书馆。定制您自己的电子书，创建在线课堂，贡献和分享内容，并与同事网络连接。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Vinaire",
          "url": "https://vinaire.me/",
          "description": "物理和数学课程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "VisualPDE",
          "url": "https://visualpde.com/",
          "description": "探索科学和数学的交互式平台，提供波浪、病毒和反应扩散模式等主题的模拟。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Whole Earth",
          "url": "https://wholeearth.info/",
          "description": "Whole Earth出版物的近乎完整档案，这是Stewart Brand和POINT基金会从1968年到2002年出版的一系列期刊和杂志，为学术、教育和研究目的提供。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Sketchplanations",
          "url": "https://sketchplanations.com/categories/science",
          "description": "科学概念的简单草图和视觉解释集合，旨在使复杂主题更易理解。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "物理",
      "originalName": "Physics",
      "id": "physics",
      "subcategories": [
        {
          "name": "量子物理",
          "originalName": "Quantum",
          "id": "quantum",
          "websites": [
            {
              "name": "Webb Telescope",
              "url": "https://webbtelescope.org/",
              "description": "詹姆斯·韦伯太空望远镜的官方网站。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "量子游戏",
          "originalName": "Quantum Games",
          "id": "quantum-games",
          "websites": [
            {
              "name": "Virtual Lab by Quantum Flytrap",
              "url": "https://quantumflytrap.com/lab",
              "description": "模拟多粒子量子系统的光学实验，支持量子密钥分发和纠缠探索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hello Quantum",
              "url": "https://quantum-computing.ibm.com/lab",
              "description": "通过交互式益智游戏介绍量子电路和门，具有简化量子概念的图形界面。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Particle in a Box",
              "url": "https://learnqm.gatech.edu",
              "description": "通过对比经典物理和量子物理的2D单人平台游戏演示量子叠加和能级。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Psi and Delta",
              "url": "https://learnqm.gatech.edu",
              "description": "通过专注于叠加和量子概率的合作游戏鼓励量子力学的协作学习。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "QPlayLearn",
              "url": "https://qplaylearn.com",
              "description": "提供交互式工具、视频和游戏进行多层次量子物理教育，满足不同学习者的需求。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Quantum Odyssey",
              "url": "https://quarksinteractive.com",
              "description": "通过游戏化界面可视化量子算法开发，辅助量子计算逻辑和状态演化教育。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Quantum Moves 2",
              "url": "https://www.scienceathome.org/games/quantum-moves-2",
              "description": "让公民科学家参与优化量子实验，解决量子优化中的现实挑战。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "The Virtual Quantum Optics Laboratory",
              "url": "https://www.vqol.org/",
              "description": "支持量子光学实验的设计和仿真，为教育目的连接经典力学和量子力学。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Arxiv Paper - Quantum Games and Interactive Tools for Quantum Technologies Outreach and Education",
              "url": "https://arxiv.org/abs/2202.07756",
              "description": "关于使用游戏和交互工具让公众理解量子技术的全面讨论。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "天文学",
          "originalName": "Astronomy",
          "id": "astronomy",
          "websites": [
            {
              "name": "Fear of Physics",
              "url": "https://www.fearofphysics.com/",
              "description": "提供免费的\"天文学101\"课程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Galileo's Applets",
              "url": "https://galileo.phys.virginia.edu/classes/109N/more_stuff/Applets/home.html",
              "description": "提供关于早期月球观测和各种运动相关主题的小程序。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "100,000 Stars",
              "url": "https://stars.chromeexperiments.com/",
              "description": "展示十万颗附近恒星的可视化，提供沉浸式体验。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "The Million Earth Solar System",
              "url": "https://planetplanet.net/2018/06/01/the-million-earth-solar-system/",
              "description": "探索拥有一百万颗类地行星的太阳系概念。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Space Telescope Live",
              "url": "https://spacetelescopelive.org/webb",
              "description": "实时访问詹姆斯·韦伯太空望远镜的数据和观测结果，让用户探索太空研究和发现。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Telescope Optics",
              "url": "https://www.telescope-optics.net/",
              "description": "为业余望远镜制造者提供的资源，详细介绍望远镜光学系统的设计和构造。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Orbital Basics",
              "url": "https://t-neumann.github.io/space/OrbitalBasics/",
              "description": "解释轨道力学基本概念的教育资源，专为太空科学初学者设计。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "The Theoretical Minimum",
          "url": "https://theoreticalminimum.com/home",
          "description": "由世界知名物理学家伦纳德·苏斯金德教授的斯坦福大学继续教育课程系列。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Theoretical Physics - Cambridge",
          "url": "https://www.damtp.cam.ac.uk/user/tong/index.html",
          "description": "来自剑桥大学的理论物理资源和信息。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "University of Virginia Classical and Modern Physics II",
          "url": "https://galileo.phys.virginia.edu/classes/632.ral5q.summer06/lectures.html",
          "description": "弗吉尼亚大学经典与现代物理学II课程的讲义和材料。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mueller Group - Cornell",
          "url": "https://muellergroup.lassp.cornell.edu/index.html",
          "description": "康奈尔大学原子和固体物理实验室。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ptable",
          "url": "https://ptable.com/?lang=en",
          "description": "提供元素及其性质信息的交互式可定制周期表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "National Institute of Standards and Technology – Fundamental Physical Constants",
          "url": "https://www.nist.gov/pml/fundamental-physical-constants",
          "description": "提供权威的物理常数数据，为研究和技术应用提供基本测量数据。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mechanics Map",
          "url": "https://mechanicsmap.psu.edu/index.html",
          "description": "来自宾夕法尼亚州立大学的交互式资源，用于探索机械系统和理解基本力学原理。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "数学",
      "originalName": "Mathematics",
      "id": "mathematics",
      "subcategories": [
        {
          "name": "数学艺术",
          "originalName": "Math + Art",
          "id": "math---art",
          "websites": [
            {
              "name": "Paul Nylander's website",
              "url": "https://bugman123.com/",
              "description": "保罗·尼兰德最喜欢的爱好和兴趣，特别是科学和艺术",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "NYC DOE CS4All",
              "url": "https://nycdoe-cs4all.github.io/",
              "description": "使用p5.js进行计算媒体介绍",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Texample",
              "url": "https://texample.net/",
              "description": "LaTeX示例和社区",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Snowflakes Project",
              "url": "https://www.dynamicmath.xyz/collective-math-art/",
              "description": "集体数学艺术的雪花项目",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Open Logic Text",
          "url": "https://openlogicproject.org/about/",
          "description": "开源、模块化、协作编写的形式（元）逻辑和形式方法教学材料集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "OEIS - The On-Line Encyclopedia of Integer Sequences",
          "url": "https://oeis.org/",
          "description": "提供综合整数序列数据库的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "First Principles of Mathematics",
          "url": "https://pdodds.w3.uvm.edu/",
          "description": "涵盖各种数学主题第一原理的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "38 Best Math Websites for Students",
          "url": "https://blog.symbaloo.com/webmixes/11/best-math-websites",
          "description": "为学生推荐的38个数学网站汇编。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Math Hints",
          "url": "https://mathhints.com/",
          "description": "为各种数学主题提供简单解释和示例的免费网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Better Explained",
          "url": "https://betterexplained.com/",
          "description": "专注于理解数学概念而非死记硬背的平台，提供虚数和指数等主题的清晰课程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mathway",
          "url": "https://www.mathway.com/",
          "description": "免费数学问题求解器。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ChiliMath",
          "url": "https://www.chilimath.com/",
          "description": "数学主题的在线资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Visual and Interactive Introduction to Complex Analysis",
          "url": "https://complex-analysis.com/",
          "description": "提供复分析可视化和交互式介绍的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Matrices as Tensor Network Diagrams",
          "url": "https://www.math3ma.com/blog/matrices-as-tensor-network-diagrams",
          "description": "讨论矩阵表示为张量网络图的文章。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "What is Category Theory Anyway?",
          "url": "https://www.math3ma.com/blog/what-is-category-theory-anyway",
          "description": "用范畴论探索数学事物的宏观体系。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Algebra Practice Problems",
          "url": "https://www.algebrapracticeproblems.com/",
          "description": "提供代数练习题和清晰解释的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Matrixology",
          "url": "https://pdodds.w3.uvm.edu/teaching/courses/2016-08UVM-122/",
          "description": "矩阵学课程资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Math Courses at NITK",
          "url": "https://sam.nitk.ac.in/courses-taught.html",
          "description": "卡纳塔克邦国家理工学院（NITK）数学课程列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Easy Mathematical Tricks from Counting Through Calculus",
          "url": "https://mathhints.com/",
          "description": "从计数到微积分的简易数学技巧集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Math Salamanders",
          "url": "https://www.math-salamanders.com/",
          "description": "为儿童或学生提供有用计算器（如反向百分比计算器）和数学工作表的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "3Blue1Brown Non-Videos",
          "url": "https://some.3b1b.co/non-videos",
          "description": "3Blue1Brown的SoME非视频内容获奖作品。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mathigon – The Mathematical Playground",
          "url": "https://mathigon.org/",
          "description": "提供教育内容和资源的交互式数学平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Math Warehouse",
          "url": "https://www.mathwarehouse.com/",
          "description": "提供交互式数学活动、演示、定义和示例课程、工作表和其他资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Cut the Knot",
          "url": "https://www.cut-the-knot.org/",
          "description": "提供交互式数学谜题、问题和可视化的资源，通过解决问题来探索数学概念。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Trigonography",
          "url": "https://trigonography.com/?page_id=230",
          "description": "探索三角学及其应用的网站，具有视觉辅助工具、公式和解题技巧。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Lamar Math Tutorials",
          "url": "https://tutorial.math.lamar.edu/",
          "description": "综合的数学教程和练习题集合，涵盖从代数到微积分和微分方程的主题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Art of Problem Solving",
          "url": "https://artofproblemsolving.com/company",
          "description": "专注于培养各学科问题解决技能，包括数学、物理、编程和语言艺术。提供严格的在线课程、实体学院和旨在鼓励学生批判性思维、实验和坚持的引人入胜的课程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mathematics Genealogy Project",
          "url": "https://genealogy.math.ndsu.nodak.edu/index.php",
          "description": "旨在汇编和分享全球数学家的综合信息，从学术机构和个人贡献者收集数据。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Enjeck Complicated Math Equation Generator",
          "url": "https://enjeck.com/num2math/?input=4&submit=Generate",
          "description": "根据用户输入生成复杂数学方程，提供练习或可视化数学问题的工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Erdos Problems",
          "url": "https://www.erdosproblems.com/",
          "description": "专门探索与保罗·埃尔德什相关的开放数学问题的网站，包括数论和组合学中的持续挑战。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "The Electronic Journal of Combinatorics",
          "url": "https://www.combinatorics.org/ojs/index.php/eljc",
          "description": "发表组合学研究论文和文章的开放获取期刊，组合学是处理计数、排列和结构的数学领域。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Zeta by Amir Hirsch",
          "url": "https://amirhirsch.com/zeta/index.html",
          "description": "致力于探索黎曼假设的网站，为对数论感兴趣的人提供交互式工具和资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tungsteno",
          "url": "https://www.tungsteno.io/",
          "description": "提供教学工具的平台，让每个人都能接触数学，完全免费，基于开放协作。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "PlanetMath",
          "url": "https://planetmath.org/",
          "description": "在线数学资源和协作平台，提供开放获取的数学内容，包括定义、定理和证明。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Geomstats Tutorials",
          "url": "https://geomstats.github.io/tutorials/index.html",
          "description": "几何统计学教程和资源，提供在流形上实现统计技术的实际示例和代码。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ximera - MOOCulus",
          "url": "https://ximera.osu.edu/mooculus",
          "description": "为在线课程设计的交互式数学模块和学习资源集合，专注于微积分和相关学科。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "工程",
      "originalName": "Engineering",
      "id": "engineering",
      "subcategories": [
        {
          "name": "土木工程",
          "originalName": "Civil Engineering",
          "id": "civil-engineering",
          "websites": [
            {
              "name": "Floor Plan Lab",
              "url": "https://floorplanlab.com/",
              "description": "创建和可视化平面图的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Engineers Edge",
              "url": "https://www.engineersedge.com/",
              "description": "提供结构和机械工程主题文章和资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "3D House Planner",
              "url": "https://3dhouseplanner.com/",
              "description": "网络上的免费3D平面图规划应用程序。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "机械工程",
          "originalName": "Mechanical Engineering",
          "id": "mechanical-engineering",
          "websites": [
            {
              "name": "507 Movements",
              "url": "https://507movements.com/toc.html",
              "description": "以动画机械运动为特色的网站，提供对各种机械系统的视觉理解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MadeHow",
              "url": "https://www.madehow.com/",
              "description": "解释和详述各种产品制造过程的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Comprehensive Structural Analysis Book",
              "url": "https://temple.manifoldapp.org/projects/structural-analysis",
              "description": "提供结构分析综合书籍的在线资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Awesome MechEng",
              "url": "https://github.com/m2n037/awesome-mecheng#heat-transfer",
              "description": "优秀的机械工程资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Animated Dynamics",
              "url": "https://dynref.engr.illinois.edu/ref.html",
              "description": "可视化动力学仿真的交互式参考资料，旨在帮助用户更好地理解复杂的机械系统。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wikimedia Commons - Engine Animations",
              "url": "https://commons.wikimedia.org/wiki/Category:Animations_of_engines",
              "description": "演示各种类型发动机（从燃烧到电动）功能的动画图像和视频集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mechanisms/menu-gear",
              "url": "https://www.mekanizmalar.com/menu-gear.html",
              "description": "齿轮机构动画。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mechanism Animations",
              "url": "https://people.ohio.edu/williams/html/MechanismAnimations.html",
              "description": "提供演示机械系统和机构的交互式动画，帮助用户可视化和理解其运动和功能。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CalcResource",
              "url": "https://calcresource.com/resources.html",
              "description": "定期更新的专注于力学和静力学的资源列表，有助于学习和理解这些学科。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Engineering Toolbox",
              "url": "https://www.engineeringtoolbox.com/",
              "description": "涵盖广泛工程主题的综合资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "StructX",
              "url": "https://structx.com/",
              "description": "为专业人士和学生提供结构工程资源的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Amesweb",
              "url": "https://www.amesweb.info/",
              "description": "包含计算器和涵盖各种工程主题文章的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RoyMech",
              "url": "https://www.roymech.co.uk/",
              "description": "提供结构和机械工程主题深度文章的参考网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Arcelor-Mittal Design Software",
              "url": "https://sections.arcelormittal.com/design_aid/design_software/EN",
              "description": "阿赛洛-米塔尔提供的钢结构免费设计软件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "BU Moss",
              "url": "https://www.bu.edu/moss/",
              "description": "专注于理解细长结构的课程和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Homestyler",
              "url": "https://www.homestyler.com/",
              "description": "面向专业人士和业余爱好者的简单省时的在线室内设计工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "How a Car Works",
              "url": "https://www.howacarworks.com/",
              "description": "解释汽车工作原理的完整免费指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IIHS Ratings",
              "url": "https://www.iihs.org/ratings",
              "description": "来自公路安全保险协会的碰撞测试评级和安全信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Euro NCAP",
              "url": "https://www.euroncap.com/en/",
              "description": "为车辆提供安全评级的欧洲新车评估计划。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Toaster Museum",
              "url": "http://toastermuseum.com/",
              "description": "专门收藏古董烤面包机的展示，为烤面包机爱好者和收藏家展示复古型号，同时提供历史信息和保存细节。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "材料纳米技术",
          "originalName": "Materials / Nanotechnology",
          "id": "materials---nanotechnology",
          "websites": [
            {
              "name": "DoITPoMS",
              "url": "https://www.doitpoms.ac.uk/index.php",
              "description": "来自剑桥大学材料科学与冶金系的DoITPoMS",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Nanoscience Resources",
              "url": "https://ssd.phys.strath.ac.uk/",
              "description": "斯特拉斯克莱德大学物理系的纳米科学资源",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "StatNano",
              "url": "https://product.statnano.com/",
              "description": "关于纳米技术产品信息的可靠来源，目前广泛应用于各种工业领域",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MyMiniFactory",
              "url": "https://www.myminifactory.com/",
              "description": "世界上最大的免费下载具有文化意义的3D可打印物体生态系统",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Roy Mech",
              "url": "https://roymech.org/",
              "description": "提供与机械工程和工程材料相关的有用信息、表格、时间表和公式的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "电子工程",
          "originalName": "Electronics Engineering",
          "id": "electronics-engineering",
          "websites": [
            {
              "name": "Electrical4U",
              "url": "https://www.electrical4u.com/",
              "description": "学习电气工程的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Electronics Tutorials",
              "url": "https://www.electronics-tutorials.ws/",
              "description": "提供电子学各个方面教程的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Octopart",
              "url": "https://octopart.com/",
              "description": "电子元件平台，帮助用户查找和比较组件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TechSpot - How CPUs Are Designed and Built",
              "url": "https://www.techspot.com/article/1821-how-cpus-are-designed-and-built/",
              "description": "涵盖计算机架构、处理器电路设计、超大规模集成电路、芯片制造和计算未来趋势的系列文章。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Circuits Book",
              "url": "https://www.opencircuitsbook.com/",
              "description": "\"开放电路\"是对日常电子产品内部精美设计的摄影探索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Digi-Key Electronics",
              "url": "https://www.digikey.com/",
              "description": "电子元件在线市场，为工程师和制造商提供大量零件、工具和资源选择。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Telematic Connections Timeline",
              "url": "http://telematic.walkerart.org/timeline/index.html",
              "description": "探索远程信息处理和网络通信历史与影响的交互式时间轴，专注于艺术和技术。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Cybergraph",
              "url": "https://cybergraph.dubberly.com/#",
              "description": "控制论的视觉探索，提供网络的交互式表示。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Animagraffs",
          "url": "https://animagraffs.com/",
          "description": "以详细动画信息图表（animagraffs）为特色的教育网站，直观地解释各学科的复杂主题和过程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Technology Student",
          "url": "https://technologystudent.com/index.htm",
          "description": "包含大量信息表、练习和动画的资源，涵盖各种技术相关主题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Nuclear Power",
          "url": "https://www.nuclear-power.com/",
          "description": "旨在学习核能和反应堆物理基础的非营利项目，涵盖核电站、反应堆物理、热工程、材料和辐射防护等主题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Knovel",
          "url": "https://app.knovel.com/kn",
          "description": "为各种工程和科学目的提供工具和计算器的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Engineering Library",
          "url": "https://engineeringlibrary.org/reference/",
          "description": "工程主题参考图书馆。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Engineering Toolbox",
          "url": "https://www.engineeringtoolbox.com/index.html",
          "description": "提供工程工具和信息的在线资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Text to CAD",
          "url": "https://text-to-cad.zoo.dev/",
          "description": "将文字描述转换为CAD模型的工具，帮助用户从书面输入生成技术图纸。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "McMaster-Carr",
          "url": "https://www.mcmaster.com/",
          "description": "硬件、工具、原材料、工业材料和维护设备供应商，通过广泛的产品目录为各行业提供服务。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Engineer on a Disk: Modeling",
          "url": "https://engineeronadisk.com/book_modeling/modelTOC.html",
          "description": "为工程师和开发人员提供系统建模和仿真实践指南的书籍，包含实用示例和解释。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Bartosz Ciechanowski Archives",
          "url": "https://ciechanow.ski/archives/",
          "description": "巴托什·齐哈诺夫斯基关于设计和技术的文章集合。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "计算机科学",
      "originalName": "Computer Science",
      "id": "computer-science",
      "subcategories": [
        {
          "name": "数据结构算法",
          "originalName": "Data Structures and Algorithms (DS&A)",
          "id": "data-structures-and-algorithms--ds-a-",
          "websites": [
            {
              "name": "Dictionary of Algorithms and Data Structures",
              "url": "https://xlinux.nist.gov/dads/",
              "description": "NIST算法和数据结构词典。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visualgo",
              "url": "https://visualgo.net/en",
              "description": "通过动画可视化数据结构和算法的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Adventures In Coding & Algorithms",
              "url": "https://entcheva.github.io/",
              "description": "探索编程和算法的博客。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Damn Cool Algorithms",
              "url": "https://blog.notdot.net/tag/damn-cool-algorithms",
              "description": "专门介绍特别有趣算法的博客。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Skiena's Algorithms Lectures",
              "url": "https://www3.cs.stonybrook.edu/~algorith/video-lectures/",
              "description": "史蒂文·斯基纳的算法视频讲座。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visual Algorithms",
              "url": "https://thimbleby.gitlab.io/algorithm-wiki-site/",
              "description": "呈现算法视觉表示的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Sinon - Algorithms",
              "url": "https://sinon.org/algorithms/",
              "description": "涵盖计算机科学重要成果的总结页面。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "大O记号",
          "originalName": "Big-O notation",
          "id": "big-o-notation",
          "websites": [
            {
              "name": "Algorithms and Data Structures Big-O Notation",
              "url": "https://cooervo.github.io/Algorithms-DataStructures-BigONotation/",
              "description": "涵盖各种算法和数据结构大O表示法的综合资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Big-O Cheat Sheet",
              "url": "https://www.bigocheatsheet.com/",
              "description": "展示计算机科学中常用算法空间和时间复杂度（大O）的网页。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Comp160 Data Cheat",
              "url": "https://www.clear.rice.edu/comp160/data_cheat.html",
              "description": "提供计算机科学中数据结构和相关概念信息的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Webopedia",
          "url": "https://www.webopedia.com/",
          "description": "计算机和IT术语的在线技术词典、学习指南和评论。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Teach Yourself Computer Science",
          "url": "https://teachyourselfcs.com/",
          "description": "计算机科学自学的综合指南。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Open Source Society University - Computer Science",
          "url": "https://github.com/open-source-society/computer-science",
          "description": "开源社会大学提供的计算机科学学习课程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Functional Computer Science Curriculum",
          "url": "https://functionalcs.github.io/curriculum/",
          "description": "专注于计算机科学中函数式编程概念的课程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Menimagerie",
          "url": "https://www.menimagerie.com/",
          "description": "探索理论计算机科学概念，从数系到康托无穷、哥德尔定理和自动机。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Computer Science Library",
          "url": "https://www.compscilib.com/",
          "description": "通过自动化分步解决方案和练习题帮助掌握计算机科学和数学课程概念的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Computer Jargon",
          "url": "https://www.computerhope.com/jargon.htm",
          "description": "计算机相关术语和技术术语词汇表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Talks by Alan Kay",
          "url": "https://tinlizzie.org/IA/index.php/Talks_by_Alan_Kay",
          "description": "计算机科学家艾伦·凯演讲集。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Everything Computer Science",
          "url": "https://everythingcomputerscience.com/",
          "description": "涵盖计算机科学主题的广泛文章和教程，包括编程、算法、数据结构和软件开发实践。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Richard Sutton Video Lectures",
          "url": "https://videolectures.net/search/?query=Richard%20sutton",
          "description": "理查德·萨顿视频讲座和演讲集，他是强化学习和人工智能领域的著名研究者。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "人工智能",
      "originalName": "AI/ML",
      "id": "ai-ml",
      "subcategories": [
        {
          "name": "机器人",
          "originalName": "Robotics",
          "id": "robotics",
          "websites": [
            {
              "name": "Robots Guide",
              "url": "https://robotsguide.com/",
              "description": "为机器人学初学者和爱好者提供指南、评论和见解的综合资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Control Challenges",
              "url": "https://janismac.github.io/ControlChallenges/",
              "description": "机器人学的LeetCode。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "大语言模型",
          "originalName": "LLMs",
          "id": "llms",
          "websites": [
            {
              "name": "Hacker Llama",
              "url": "https://osanseviero.github.io/hackerllama/blog/posts/hitchhiker_guide/",
              "description": "关于加入本地大语言模型社区时需要了解的有用术语的博客文章。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Moebio Mind",
              "url": "https://moebio.com/mind/",
              "description": "探索语言模型内部工作原理，可视化GPT-3 API生成的语义空间和词汇补全轨迹。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LLM Visualization",
              "url": "https://bbycroft.net/llm",
              "description": "用于可视化大语言模型并探索其结构、行为和输出的交互式工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RR LM Game",
              "url": "https://rr-lm-game.herokuapp.com/",
              "description": "基于浏览器的语言建模游戏。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "提示工程",
          "originalName": "Prompt Engineering",
          "id": "prompt-engineering",
          "websites": [
            {
              "name": "PromptPerfect",
              "url": "https://promptperfect.jina.ai/",
              "description": "为大语言模型、大型模型和LMOps设计的尖端提示优化器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Jailbreak Chat",
              "url": "https://www.jailbreakchat.com/",
              "description": "大语言模型越狱提示集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MidJourney Styles and Keywords Reference",
              "url": "https://github.com/willwulfken/MidJourney-Styles-and-Keywords-Reference",
              "description": "AI图像生成的样式和关键词参考。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "QuickRef ChatGPT",
              "url": "https://quickref.me/chatgpt",
              "description": "ChatGPT速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OpenAI Cookbook",
              "url": "https://cookbook.openai.com/",
              "description": "OpenAI提供的AI工作实用指南手册。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Prompting Guide",
              "url": "https://www.promptingguide.ai/",
              "description": "为AI模型创建有效提示的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Instaprompt",
              "url": "https://www.instaprompt.ai/?ref=producthunt",
              "description": "提供即时写作提示的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Midjourney Prompt Helper",
              "url": "https://promptfolder.com/midjourney-prompt-helper/",
              "description": "帮助用户为Midjourney生成详细有效提示的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Free Midjourney Prompt",
              "url": "https://www.freemidjourneyprompt.com/",
              "description": "提供大量免费Midjourney提示，让用户轻松通过优化的自然语言提示生成图像。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Prompt Engineering Guide",
              "url": "https://learnprompting.org/docs/introduction",
              "description": "提示工程综合指南，提供为AI模型和创意任务制作有效提示的基本原则和技术。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "AI工具",
          "originalName": "AI tools",
          "id": "ai-tools",
          "websites": [
            {
              "name": "Future Tools",
              "url": "https://www.futuretools.io/",
              "description": "寻找符合您需求的精确AI工具的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WarpSound AI",
              "url": "https://www.warpsound.ai/",
              "description": "仅用提示在几秒钟内创建高质量生成式AI音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AI Tools Arena",
              "url": "https://aitoolsarena.com/",
              "description": "展示各种AI工具和资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Klavier AI",
              "url": "https://klavier.ai/",
              "description": "允许您在选择的网页和文档上与ChatGPT进行问答。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Aiva Valley",
              "url": "https://aivalley.ai/",
              "description": "最新的AI工具和提示来源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bardeen AI",
              "url": "https://www.bardeen.ai/",
              "description": "几分钟内无需代码即可自动化手动工作和任务的AI工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Speechify",
              "url": "https://speechify.com/",
              "description": "通过领先的AI文本转语音阅读器聆听文档、文章、PDF、邮件等任何内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Humata AI",
              "url": "https://www.humata.ai/",
              "description": "上传任何PDF或文档，几秒钟内获得答案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Syntelly",
              "url": "https://app.syntelly.com/search",
              "description": "用于有机化学和医学化学的人工智能。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Warp.dev - Warp AI",
              "url": "https://www.warp.dev/warp-ai?source=producthunt&ref=producthunt",
              "description": "无需编写代码即可构建API的AI驱动工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Feedback by AI",
              "url": "https://feedbackbyai.com/?ref=producthunt",
              "description": "利用AI为写作生成可操作反馈的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Seeing the World through Your Eyes",
              "url": "https://world-from-eyes.github.io/",
              "description": "重建通过人类眼睛反映的世界。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Auxiliary Tools",
              "url": "https://www.auxiliary.tools/",
              "description": "为人类提供AI实验和工具的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hemingway Editor",
              "url": "https://hemingwayapp.com/",
              "description": "针对文体风格的拼写检查器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "FuturePedia",
              "url": "https://www.futurepedia.io/",
              "description": "领先的AI资源平台，致力于帮助各行业专业人士利用AI技术进行创新和发展。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ExplainPaper",
              "url": "https://www.explainpaper.com/",
              "description": "用户可以上传研究论文、高亮困惑文本并获得解释的平台，使研究论文更易理解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Summarize Tech",
              "url": "https://www.summarize.tech/",
              "description": "AI驱动的工具，可获取任何长YouTube视频的摘要，如讲座、直播活动或政府会议。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "YouTubeTranscript",
              "url": "https://youtubetranscript.com/",
              "description": "获取YouTube视频转录和摘要。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "数据科学",
          "originalName": "Data Science",
          "id": "data-science",
          "websites": [
            {
              "name": "DataTau",
              "url": "https://www.datatau.com/news",
              "description": "专注于数据的Hackernews",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DatasetList",
              "url": "https://www.datasetlist.com/",
              "description": "来自网络各处的机器学习数据集列表",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Stat Learning",
              "url": "https://www.statlearning.com/",
              "description": "学习统计学和相关主题的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "数据库",
          "originalName": "Databases",
          "id": "databases",
          "websites": [
            {
              "name": "ImageNet",
              "url": "https://www.image-net.org/",
              "description": "根据WordNet层次结构组织的图像数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DataSheets.com",
              "url": "https://www.datasheets.com/",
              "description": "可搜索的电子元件数据表和采购信息数据库，专为设计工程师和电子采购代理设计。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Academic Torrents",
              "url": "https://academictorrents.com/",
              "description": "社区驱动的大型数据集共享平台，为学术研究提供种子下载，包括科学论文、数据集和多媒体。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DBLP",
              "url": "https://dblp.org/",
              "description": "提供主要计算机科学期刊、会议和论文集书目信息的综合开放式数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Doublespeak Chat",
          "url": "https://doublespeak.chat/#/handbook",
          "description": "关于大语言模型黑客攻击的经验性、非学术性和实用指南。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Fast.ai Course",
          "url": "https://course.fast.ai/",
          "description": "深度学习和机器学习在线课程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Papers with Code",
          "url": "https://paperswithcode.com/sota",
          "description": "机器学习任务最先进结果及相关代码的集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Delta Academy Maps",
          "url": "https://maps.joindeltaacademy.com/",
          "description": "机器学习数学地图。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Inside the Matrix by PyTorch",
          "url": "https://pytorch.org/blog/inside-the-matrix/",
          "description": "PyTorch的博客文章，可视化矩阵乘法、注意力机制等内容。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Directory of AI Models",
          "url": "https://docs.google.com/spreadsheets/d/1gc6yse74XCwBx028HV_cvdxwXkmXejVjkO-Mz2uwE0k/edit?pli=1#gid=0",
          "description": "提供各种AI模型目录的Google表格文档，包含名称、日期、参数、组织等信息。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Papers with Code",
          "url": "https://paperswithcode.com/",
          "description": "提供最新机器学习研究论文及代码实现和基准测试的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Globe Engineer Explorer",
          "url": "https://explorer.globe.engineer/",
          "description": "应用AI公司，制作为人类理解优化信息表示的产品。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Colah's Blog",
          "url": "https://colah.github.io/",
          "description": "克里斯托弗·奥拉关于深度学习和人工智能的博客，包含详细解释、教程和研究见解。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "TensorFlow Playground",
          "url": "https://playground.tensorflow.org",
          "description": "用于实验神经网络的交互式工具，让用户可视化不同配置对模型训练和分类任务的影响。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ben's Bites",
          "url": "https://bensbites.com/catalog",
          "description": "提供各种一口大小AI资源的目录。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "Web开发",
      "originalName": "Web Development",
      "id": "web-development",
      "subcategories": [
        {
          "name": "前端开发",
          "originalName": "Front-end",
          "id": "front-end",
          "websites": [
            {
              "name": "SafeRules",
              "url": "https://anthonyhobday.com/sideprojects/saferules/",
              "description": "每次都可以安全遵循的视觉设计规则。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Can I Email",
              "url": "https://www.caniemail.com/",
              "description": "检查HTML和CSS功能的电子邮件客户端兼容性。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Egg Gradients",
              "url": "https://www.eggradients.com/",
              "description": "渐变背景颜色集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Frontend Checklist",
              "url": "https://frontendchecklist.io/",
              "description": "在将网站/HTML页面发布到生产环境之前需要拥有/测试的所有元素的详尽列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Frontend Mentor",
              "url": "https://www.frontendmentor.io/",
              "description": "在处理专业设计时解决现实世界HTML、CSS和JavaScript挑战的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodePen",
              "url": "https://codepen.io/",
              "description": "构建、测试和发现前端代码的平台。探索[主题](https://codepen.io/topics/)。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Electron",
              "url": "https://www.electronjs.org/",
              "description": "使用JavaScript、HTML和CSS构建跨平台桌面应用程序的框架。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Homepage Gallery",
              "url": "https://homepage.gallery/",
              "description": "展示500多个网站以获得Web设计灵感的画廊。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HTML Dog",
              "url": "https://htmldog.com/",
              "description": "HTML、CSS和JavaScript的综合资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learn.shayhowe",
              "url": "https://learn.shayhowe.com/",
              "description": "学习编写HTML和CSS代码。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "This vs That",
              "url": "https://thisthat.dev/",
              "description": "探索前端开发概念之间的差异。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Know-It-All",
              "url": "https://know-it-all.io/",
              "description": "列出您在Web开发方面知道和不知道的内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "You Might Not Need jQuery",
              "url": "https://youmightnotneedjquery.com/",
              "description": "jQuery替代方案。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "HTML",
          "originalName": "HTML",
          "id": "html",
          "websites": [
            {
              "name": "Hyperscript",
              "url": "https://hyperscript.org/",
              "description": "使用普通标记编写的网站变得令人愉快。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "htmx",
              "url": "https://htmx.org/",
              "description": "htmx通过属性直接在HTML中为您提供AJAX、CSS过渡、WebSockets和服务器发送事件的访问权限，因此您可以利用超文本的简洁性和强大功能构建现代用户界面。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "CSS",
          "originalName": "CSS",
          "id": "css",
          "websites": [
            {
              "name": "CSS Solid",
              "url": "https://www.csssolid.com/",
              "description": "CSS参考、教程和文章。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CSS-Tricks",
              "url": "https://css-tricks.com/",
              "description": "由Digital Ocean支持的Web设计社区。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CSS Box Shadow Code Snippet",
              "url": "https://onaircode.com/css-box-shadow-code-snippet/",
              "description": "创建CSS盒子阴影的代码片段。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "JavaScript",
          "originalName": "JavaScript",
          "id": "javascript",
          "websites": [
            {
              "name": "The Coding Cards",
              "url": "https://thecodingcards.com/",
              "description": "JavaScript和数据结构抽认卡。包含语法和示例的基本编程概念。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "StandardJS",
              "url": "https://standardjs.com/",
              "description": "JavaScript风格指南、代码检查器和格式化程序。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Modern JavaScript Cheatsheet",
              "url": "https://mbeaudru.github.io/modern-js-cheatsheet/",
              "description": "现代项目中经常遇到的JavaScript知识速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JSONPlaceholder",
              "url": "https://jsonplaceholder.typicode.com/",
              "description": "用于测试和原型设计的免费虚假API。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Vue.js Cheat Sheet",
              "url": "https://marozed.com/vue-cheatsheet/",
              "description": "Vue.js速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JS Bin",
              "url": "https://jsbin.com/",
              "description": "开源协作Web开发调试工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JavaScript Event Keycode Info",
              "url": "https://www.toptal.com/developers/keycode",
              "description": "JavaScript事件键码信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "jsDelivr",
              "url": "https://www.jsdelivr.com/",
              "description": "开源免费CDN。快速、可靠且自动化。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "后端开发",
          "originalName": "Back-End",
          "id": "back-end",
          "websites": [
            {
              "name": "RunSidekick",
              "url": "https://www.runsidekick.com/",
              "description": "无需停止和重新部署应用程序即可按需收集跟踪和生成日志。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Vantage Instances",
              "url": "https://instances.vantage.sh/",
              "description": "比较亚马逊的实例类型、定价和其他页面。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "API",
          "originalName": "APIs",
          "id": "apis",
          "websites": [
            {
              "name": "Public APIs Directory",
              "url": "https://publicapis.dev/",
              "description": "发现公共API",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Public APIs on GitHub",
              "url": "https://github.com/public-apis/public-apis",
              "description": "供软件和Web开发使用的免费API集合列表",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "REST API Tutorial",
              "url": "https://www.restapitutorial.com/",
              "description": "学习REST",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Spotify API Documentation",
              "url": "https://developer.spotify.com/documentation/web-api",
              "description": "Spotify的API文档",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Stripe API Documentation",
              "url": "https://stripe.com/docs/api",
              "description": "Stripe的API文档",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "SQL",
          "originalName": "SQL",
          "id": "sql",
          "websites": [
            {
              "name": "SQLZoo",
              "url": "https://sqlzoo.net/wiki/SQL_Tutorial",
              "description": "分阶段学习SQL",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bipp SQL Tutorial",
              "url": "https://bipp.io/sql-tutorial",
              "description": "免费SQL教程",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Select Star SQL",
              "url": "https://selectstarsql.com/",
              "description": "学习SQL的交互式书籍",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Medium-Hard Data Analyst SQL Interview Questions",
              "url": "https://quip.com/2gwZArKuWk7W",
              "description": "SQL面试题集合",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SQL Translate",
              "url": "https://www.sqltranslate.app/",
              "description": "SQL到自然语言和自然语言到SQL的翻译器。100%免费和开源",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Servers for Hackers",
              "url": "https://serversforhackers.com/",
              "description": "程序员的服务器管理。学习开发和生产的服务器技术。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "网站分析",
          "originalName": "Web Analytics",
          "id": "web-analytics",
          "websites": [
            {
              "name": "Pirsch",
              "url": "https://pirsch.io/",
              "description": "开源、无Cookie的网络分析平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Websites Milonic",
              "url": "https://websites.milonic.com/",
              "description": "为网站提供整洁报告的重要数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WooRank",
              "url": "https://www.woorank.com/",
              "description": "网站评估和SEO检查器。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "Semji",
              "url": "https://semji.com/",
              "description": "通过创建高性能SEO内容来提高内容投资回报率的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Web.dev",
              "url": "https://web.dev/",
              "description": "允许您测量网站性能并提供可操作的见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Download Time Calculator",
              "url": "https://downloadtimecalculator.com/",
              "description": "根据您的传输速度估算下载任何文件所需的时间，而无需实际下载文件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "W3C Link Checker",
              "url": "https://validator.w3.org/checklink",
              "description": "检查网页或完整网站中的链接和锚点。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "URLVoid",
              "url": "https://www.urlvoid.com/",
              "description": "网站声誉/安全检查器，帮助检测潜在恶意网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SimilarSites",
              "url": "https://www.similarsites.com/",
              "description": "帮助找到相似的网站并提供有关其统计信息的信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LocaBrowser",
              "url": "https://www.locabrowser.com/",
              "description": "实时测试您的网站在不同国家的外观。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "StatusVista",
              "url": "https://statusvista.com/",
              "description": "您的产品所依赖系统的一体化状态页面。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "BuiltWith",
              "url": "https://builtwith.com/",
              "description": "发现网站使用的技术，使用包含59,905+种网络技术和超过6.73亿个网站的数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CamelCamelCamel",
              "url": "https://camelcamelcamel.com/",
              "description": "免费的亚马逊价格追踪器，监控数百万种产品并在价格下降时提醒您。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CloudPing",
              "url": "https://cloudping.bastionhost.org/en/",
              "description": "执行HTTP ping以测量从您的浏览器到世界各地各种云数据中心的网络延迟。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DownForEveryoneOrJustMe",
              "url": "https://downforeveryoneorjustme.com/",
              "description": "检查网站是否宕机。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DownDetector",
              "url": "https://downdetector.in/",
              "description": "实时问题和中断监控。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WebhookWizard",
              "url": "https://webhookwizard.com/",
              "description": "使用webhooks解锁数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Lighthouse",
              "url": "https://chrome.google.com/webstore/detail/lighthouse/blipmdconlkpinefehnmjammfjpmpbjk?hl=en",
              "description": "用于提高Web应用程序性能、质量和正确性的开源自动化工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Archive.md",
              "url": "https://archive.md/",
              "description": "网页的时间胶囊。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hypothesis",
              "url": "https://web.hypothes.is/",
              "description": "覆盖整个网络的对话层，在任何地方都能工作，无需任何底层站点实施（开放社区项目）。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "测试",
          "originalName": "Testing",
          "id": "testing",
          "websites": [
            {
              "name": "Fast.com",
              "url": "https://fast.com/",
              "description": "测量您的互联网速度、延迟和上传速度。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Pingdom Tools",
              "url": "https://tools.pingdom.com/",
              "description": "允许您测试页面加载时间、分析并找到瓶颈。",
              "isPaid": true,
              "isStudentFriendly": false
            },
            {
              "name": "GTmetrix",
              "url": "https://gtmetrix.com/",
              "description": "分析您的网站性能，识别缓慢的原因，并发现优化机会。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Loader.io",
              "url": "https://loader.io/",
              "description": "免费负载测试服务，用数千个并发连接对Web应用程序和API进行压力测试。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WebPageTest",
              "url": "https://www.webpagetest.org/",
              "description": "测量您网站的碳足迹并运行无代码实验以找到改进方法。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Azure Speed Test",
              "url": "https://www.azurespeed.com/Azure/Latency",
              "description": "测试Azure服务速度和延迟的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Web3加密货币",
          "originalName": "Web 3.0 Dev and Cryptocurrencies",
          "id": "web-3-0-dev-and-cryptocurrencies",
          "websites": [
            {
              "name": "Web3 is Going Great",
              "url": "https://web3isgoinggreat.com/",
              "description": "追踪区块链/加密货币/web3技术领域挑战的例子。时间轴涵盖了自2021年初以来加密货币和基于区块链技术的事件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Blockchain Demo",
              "url": "https://andersbrownworth.com/blockchain/coinbase",
              "description": "区块链工作原理的演示。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DappRadar",
              "url": "https://dappradar.com/",
              "description": "在DeFi、NFT和游戏世界中发现、追踪和交易。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DeFi Pulse",
              "url": "https://www.defipulse.com/",
              "description": "去中心化金融（DeFi）项目及其统计数据的列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GlassChain",
              "url": "https://glasschain.org/home",
              "description": "总部位于瑞士的非营利组织，检查地址、交易、钱包或提供商，以追回因犯罪行为而被盗或丢失的比特币（或其中一部分）。比特币、莱特币、比特币现金和狗狗币地址被聚类到基于UTXO的区块链钱包中。提供实时、可靠且100%正确的数据。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "Mozilla Developer Network",
          "url": "https://developer.mozilla.org/en-US/",
          "description": "Web技术文档，包括CSS、HTML和JavaScript。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "W3C",
          "url": "https://www.w3.org/",
          "description": "万维网联盟官方网站，使Web技术能够与不同语言、脚本和文化一起使用。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "UX Core",
          "url": "https://keepsimple.io/uxcore",
          "description": "UX Core允许您在创建软件时探索许多认知偏见。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Coggle",
          "url": "https://coggle.it/diagram/Vz9LvW8byvN0I38x/t/web-development",
          "description": "用于可视化和组织Web开发相关想法的协作思维导图工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Web.dev Learn",
          "url": "https://web.dev/learn",
          "description": "Google提供的Web开发资源和教程教育平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "WebDevHome",
          "url": "https://webdevhome.github.io/",
          "description": "Web开发资源和教程集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Web Dev Resources",
          "url": "https://web-dev-resources.com/#/",
          "description": "为开发者精选的出色Web开发资源列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BBC Microbit Editor",
          "url": "https://bbcmic.ro/",
          "description": "BBC提供的Microbit编程基础编辑器。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "DemoFox",
          "url": "https://blog.demofox.org/",
          "description": "包含与编程和开发相关的各种链接的博客。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Timeline of Web Browsers",
          "url": "https://super-static-assets.s3.amazonaws.com/bc2689f0-a124-4777-93dc-416ee1aa4858/images/7db6532c-14f1-4c51-a337-b3021a7293bf.svg",
          "description": "2019年前Web浏览器时间轴的可视化。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Is Houdini Ready Yet?",
          "url": "https://ishoudinireadyyet.com/",
          "description": "检查Web浏览器中各种Houdini规范就绪状态的工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Artillery",
          "url": "https://www.artillery.io/",
          "description": "用于SRE（站点可靠性工程）和DevOps的现代负载测试和冒烟测试工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Rentry",
          "url": "https://rentry.org/",
          "description": "具有预览、自定义URL和编辑功能的Markdown粘贴板服务，提供快速、简单和免费使用，条目永久保存。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Intab Resources",
          "url": "https://intab.io/resources/",
          "description": "精选的2021年Web开发工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Chrome Extension Kit",
          "url": "https://chromeextensionkit.com/",
          "description": "包含17个经过实战检验的Chrome扩展构建入门模板的工具包，节省设置时间并专注于交付。",
          "isPaid": true,
          "isStudentFriendly": false
        },
        {
          "name": "Esoteric Codes",
          "url": "https://esoteric.codes/",
          "description": "探索深奥编程语言、基于约束的编码、代码艺术、代码诗歌等的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "FreeFormatter",
          "url": "https://freeformatter.com/",
          "description": "为开发者提供的免费在线工具，包括格式化器、验证器、代码压缩器、字符串转义器、编码器/解码器、消息摘要器等。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "dbdesigner.net",
          "url": "https://www.dbdesigner.net/",
          "description": "免费的在线数据库模式设计和建模工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Jvns blog",
          "url": "https://jvns.ca/blog/2022/04/12/a-list-of-new-ish--command-line-tools/>",
          "description": "新命令行工具列表",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "VisBug",
          "url": "https://visbug.web.app/",
          "description": "用于Web开发的开源浏览器设计工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Web Design Repo",
          "url": "https://webdesignrepo.com/",
          "description": "为设计师和开发者提供的免费Web设计资源库。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Devopedia",
          "url": "https://devopedia.org/",
          "description": "涵盖技术和软件开发各种主题的知识中心。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "MadeWithBubble",
          "url": "https://www.madewithbubble.xyz/",
          "description": "发现用Bubble（可视化Web开发平台）创建的有趣应用程序和网站的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Interneting is Hard",
          "url": "https://www.internetingishard.com/",
          "description": "为完全初学者设计的友好Web开发教程。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Airtable Toolkit",
          "url": "https://www.airtable.com/home/<USER>",
          "description": "允许用户构建连接工作不同部分的应用程序平台，确保业务内的同步。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Grey Software Resources",
          "url": "https://resources.grey.software/",
          "description": "由全球专业人士和学者策划和众包的最新软件资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Free-for.dev",
          "url": "https://free-for.dev/#/",
          "description": "为开发者提供免费套餐的软件（SaaS、PaaS、IaaS等）和其他服务列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Free Privacy Policy Generator",
          "url": "https://www.freeprivacypolicy.com/",
          "description": "生成免费隐私政策的工具，确保符合CCPA、GDPR、CalOPPA、Google Analytics和AdSense等要求。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Mailinator",
          "url": "https://www.mailinator.com/",
          "description": "允许开发者和QA测试团队测试电子邮件和短信工作流程的平台，包括2FA验证、注册和密码重置。",
          "isPaid": true,
          "isStudentFriendly": false
        },
        {
          "name": "Atlaq",
          "url": "https://atlaq.com/",
          "description": "域名生成器。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Addy's Toolkit",
          "url": "https://toolkit.addy.codes/",
          "description": "为Web设计师和开发者精选的806个工具和资源集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BrowserBench - Speedometer 3.0",
          "url": "https://www.browserbench.org/Speedometer3.0/",
          "description": "测量Web浏览器性能的基准测试工具，专门测试现代Web应用程序的响应性和速度。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "UserAgents.me",
          "url": "https://www.useragents.me/",
          "description": "提供在Web上所有设备类型、操作系统和浏览器中最新和最常见用户代理的自更新列表。数据始终新鲜，每周更新。此用户代理列表非常适合希望融入的网络爬虫、开发者、网站管理员和研究人员。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "软件工程",
      "originalName": "Software Engineering",
      "id": "software-engineering",
      "subcategories": [
        {
          "name": "Android开发",
          "originalName": "Android Development",
          "id": "android-development",
          "websites": [
            {
              "name": "Fossdroid",
              "url": "https://fossdroid.com/",
              "description": "发现和分享开源Android应用的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Android Weekly",
              "url": "https://androidweekly.net/",
              "description": "与Android开发相关的精选新闻、文章和资源的每周通讯。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "F-Droid Repository Search",
              "url": "https://apt.izzysoft.de/fdroid/index.php",
              "description": "允许您搜索F-Droid存储库中可用的应用程序，这是一个免费开源Android应用程序的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Exodus Privacy",
              "url": "https://reports.exodus-privacy.eu.org/en/",
              "description": "帮助检查Android应用的权限和跟踪器，提供有关不同应用程序隐私方面的见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Don't Kill My App",
              "url": "https://dontkillmyapp.com/",
              "description": "倡导反对Android上激进的应用后台进程限制的网站，这可能会负面影响应用性能。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mobile X Files",
              "url": "https://mobilexfiles.com/",
              "description": "智能手机的秘密代码和其他隐藏功能。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MobileRead Wiki",
              "url": "https://wiki.mobileread.com/wiki/Main_Page",
              "description": "电子阅读器、电子书和相关技术的综合资源，为各种移动阅读设备的用户提供指南、常见问题解答和教程。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "游戏开发",
          "originalName": "Game Development",
          "id": "game-development",
          "websites": [
            {
              "name": "itch.io",
              "url": "https://itch.io/",
              "description": "提供免费在线查找和分享独立游戏的简单方式的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "System Requirements Lab",
              "url": "https://www.systemrequirementslab.com/cyri",
              "description": "在几秒钟内分析您的计算机规格，提供您的系统是否满足各种游戏要求的信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Kenney",
              "url": "https://kenney.nl/",
              "description": "提供免费游戏资产、图形和游戏开发资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Flash Games Archive",
              "url": "https://flasharch.com/en",
              "description": "Flash游戏档案，保存这些经典游戏供未来享受。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OpenGameArt",
              "url": "https://opengameart.org/",
              "description": "为开发者和游戏创作者提供免费使用游戏资产的平台，包括2D和3D艺术、音效和音乐。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Nexus Mods",
              "url": "https://www.nexusmods.com/",
              "description": "最大的游戏修改在线社区之一，为流行视频游戏提供大量mod集合，以增强游戏体验、添加内容或个性化体验。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "游戏理论",
          "originalName": "Game Theory",
          "id": "game-theory",
          "websites": [
            {
              "name": "Ncase",
              "url": "https://ncase.me/",
              "description": "通过简单游戏解释博弈论。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Alberta Games Research",
              "url": "https://webdocs.cs.ualberta.ca/~games/",
              "description": "阿尔伯塔大学的网站，提供与博弈论、计算模型和作为计算研究的游戏相关的资源和研究。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Combinatorics.org",
              "url": "https://www.combinatorics.org/",
              "description": "A. S. Fraenkel的组合博弈论文献目录。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Geometry and Graph Theory",
              "url": "https://ics.uci.edu/~eppstein/cgt/",
              "description": "组合博弈论资源，包含解释、示例和组合问题算法研究。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "宝可梦",
          "originalName": "Pokemon",
          "id": "pokemon",
          "websites": [
            {
              "name": "Pokemon Database",
              "url": "https://pokemondb.net/",
              "description": "包含新闻和更新的综合宝可梦数据库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Serebii",
              "url": "https://serebii.net/",
              "description": "提供有关宝可梦的各种新闻、功能、档案和解释。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "国际象棋",
          "originalName": "Chess",
          "id": "chess",
          "websites": [
            {
              "name": "The Chess Website",
              "url": "https://www.thechesswebsite.com/",
              "description": "学习、练习和下国际象棋。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Lichess",
              "url": "https://en.lichess.org/",
              "description": "由志愿者和捐赠支持的免费/自由开源国际象棋服务器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Chess.com",
              "url": "https://www.chess.com/",
              "description": "在线国际象棋平台。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "嵌入",
          "originalName": "Embeddings",
          "id": "embeddings",
          "websites": [
            {
              "name": "FPGA4Fun",
              "url": "https://www.fpga4fun.com/",
              "description": "专注于FPGA（现场可编程门阵列）技术的教育资源和教程，为初学者和高级用户提供实用指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Wokwi",
              "url": "https://wokwi.com/",
              "description": "在线ESP32、STM32、Arduino模拟器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "x86 Instruction Set Reference",
              "url": "https://c9x.me/x86/",
              "description": "x86指令集\"Into the Void\"参考的镜像，为x86架构提供汇编语言和处理器指令的详细指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Analog Devices Wiki",
              "url": "https://wiki.analog.com/",
              "description": "包含模拟和混合信号设备技术文档、教程和资源的知识库，面向工程师和开发人员。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IEEE-754 Floating Point Converter",
              "url": "https://www.h-schmidt.net/FloatConverter/IEEE754.html",
              "description": "基于IEEE-754标准，在数字的十进制表示和现代CPU使用的二进制格式之间转换的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Guide to x86 Assembly",
              "url": "https://www.cs.virginia.edu/~evans/cs216/guides/x86.html",
              "description": "弗吉尼亚大学计算机科学的x86汇编指南",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Linux",
          "originalName": "Linux",
          "id": "linux",
          "websites": [
            {
              "name": "Linux Journey",
              "url": "https://linuxjourney.com/",
              "description": "学习Linux的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Run Linux in Your Browser",
              "url": "https://bellard.org/jslinux/",
              "description": "在浏览器中运行Linux或其他操作系统。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OS Directory",
              "url": "https://os.directory/",
              "description": "在Web浏览器中模拟Linux发行版的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DistroWatch",
              "url": "https://distrowatch.com/",
              "description": "Linux和BSD发行版资源，提供新闻、评论和比较，帮助用户找到并安装合适的操作系统。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SS64",
              "url": "https://ss64.com/",
              "description": "包含最常见计算命令语法和示例的参考指南，涵盖数据库和操作系统。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SS64 Bash Keyboard Shortcuts",
              "url": "https://ss64.com/bash/syntax-keyboard.html",
              "description": "bash键盘快捷键参考指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Command Line for Beginners",
              "url": "https://ubuntu.com/tutorials/command-line-for-beginners#1-overview",
              "description": "面向初学者的Linux命令行概述。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Linux Journey",
              "url": "https://linuxjourney.com/",
              "description": "提供免费、交互式Linux课程的教育网站，涵盖从基本命令到高级系统管理的所有内容。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ExplainShell",
              "url": "https://explainshell.com/",
              "description": "解释Linux shell命令的工具，提供命令语法和功能的详细分解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LibreHunt",
              "url": "https://librehunt.org/",
              "description": "在您的Linux发行版（以及潜在的自由软件）搜索中为您提供帮助。回答简单问题，根据这些回答获得满足您需求的Linux发行版推荐。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Vim",
          "originalName": "Vim",
          "id": "vim",
          "websites": [
            {
              "name": "Vim Cheat Sheet",
              "url": "https://vim.rtorr.com/",
              "description": "Vim速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learn Vim",
              "url": "https://learnvim.irian.to/",
              "description": "智能学习Vim的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Vim Awesome",
              "url": "https://vimawesome.com/",
              "description": "搜索列出的Vim插件",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Vim Adventures",
              "url": "https://vim-adventures.com/",
              "description": "一个交互式游戏，旨在通过引人入胜的谜题和挑战教用户Vim文本编辑器的基础知识。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Vimified",
              "url": "https://www.vimified.com/",
              "description": "学习和练习Vim的平台，一个免费开源的基于屏幕的文本编辑器程序。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Git",
          "originalName": "Git",
          "id": "git",
          "websites": [
            {
              "name": "Git - The Simple Guide",
              "url": "https://rogerdudler.github.io/git-guide/",
              "description": "Git入门的直接指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Git Sheet",
              "url": "https://gitsheet.wtf",
              "description": "常见Git命令的快速参考指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MiXLab on Google Colab",
              "url": "https://colab.research.google.com/github/shirooo39/MiXLab/blob/master/MiXLab.ipynb#scrollTo=e-0yDs4C0HkB",
              "description": "从GitHub编译的Google Colab笔记本的混合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learn Git Branching",
              "url": "https://learngitbranching.js.org/",
              "description": "通过模拟git存储库学习Git分支的交互式工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "GitHub",
          "originalName": "GitHub",
          "id": "github",
          "websites": [
            {
              "name": "GitStalk",
              "url": "https://gitstalk.netlify.app/",
              "description": "发现GitHub社区中个人正在做什么的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitExplorer",
              "url": "https://gitexplorer.com/",
              "description": "无需在网络中搜索即可找到正确Git命令的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitStats",
              "url": "https://gitstats.me/",
              "description": "开源GitHub贡献分析器，提供对您的GitHub活动的见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Gitcoin",
              "url": "https://gitcoin.co/",
              "description": "个人可以通过为各种编程语言和领域的开源软件做贡献而获得报酬的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Oh Shit, Git!",
              "url": "https://ohshitgit.com/",
              "description": "为常见Git错误提供解决方案以及如何从中恢复的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitHub Trending Archive",
              "url": "https://github.motakasoft.com/trending/",
              "description": "GitHub上趋势存储库的档案，允许用户随时间探索流行项目和编程趋势。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Map of GitHub",
              "url": "https://anvaka.github.io/map-of-github/",
              "description": "GitHub存储库网络的可视化表示。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "集成开发环境",
          "originalName": "IDEs",
          "id": "ides",
          "websites": [
            {
              "name": "OneLang IDE",
              "url": "https://ide.onelang.io/",
              "description": "将代码从一种编程语言转换为另一种的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Theia IDE",
              "url": "https://theia-ide.org/",
              "description": "灵活可扩展的云和桌面IDE平台，使用现代Web技术实现IDE和工具的高效开发和交付。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "VSCode Themes",
              "url": "https://vscodethemes.com/",
              "description": "为Visual Studio Code提供各种主题的平台，允许用户通过不同的配色方案和样式个性化其编码环境的外观和感觉。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "NoviceDock",
          "url": "https://novicedock.com/",
          "description": "提供各种软件工程领域资源和解释的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Morioh",
          "url": "https://morioh.com/",
          "description": "开发者社交网络，讨论bug和问题，分享知识并连接全球数百万程序员和开发者的才能。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Algorithmica - High-Performance Computing",
          "url": "https://en.algorithmica.org/hpc/",
          "description": "Sergey Slotin的《现代硬件算法》。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ExBook",
          "url": "https://markm208.github.io/exbook/",
          "description": "Elixir的动画介绍。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ben Grosser Projects",
          "url": "https://bengrosser.com/projects/",
          "description": "Ben Grosser的项目作品集。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Cybercademy Project Ideas",
          "url": "https://cybercademy.org/project-ideas/",
          "description": "探索网络安全项目想法的资源，为教育或实际网络安全倡议提供灵感。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "隐私安全",
      "originalName": "Privacy",
      "id": "privacy",
      "subcategories": [
        {
          "name": "密码学",
          "originalName": "Cryptography",
          "id": "cryptography",
          "websites": [
            {
              "name": "Cryptologie",
              "url": "https://www.cryptologie.net/",
              "description": "涵盖密码学各种主题的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Invisible",
              "url": "https://mikebradley.me/invisible/index.html",
              "description": "在图像中隐藏文本并允许您查找隐藏文本的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "GAFA替代品",
          "originalName": "GAFA Alternatives",
          "id": "gafa-alternatives",
          "websites": [
            {
              "name": "DeGoogle",
              "url": "https://degoogle.jmoore.dev/#mobile-applications-mobile-apps-installable-from-stores",
              "description": "Google产品替代方案的庞大列表。隐私提示、技巧和链接。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Degooglisons Internet",
              "url": "https://degooglisons-internet.org/en/",
              "description": "FAANG的替代方案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AccountKiller",
              "url": "https://www.accountkiller.com/en/home",
              "description": "AccountKiller收集直接链接和删除说明，使账户终止变得容易。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JustDeleteMe",
              "url": "https://backgroundchecks.org/justdeleteme/",
              "description": "从Web服务删除您的账户的直接链接目录。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "SmartTubeNext",
              "url": "https://github.com/yuliskov/SmartTubeNext",
              "description": "在Android TV盒子上观看YouTube视频的无广告应用。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Libredirect",
              "url": "https://libredirect.github.io/",
              "description": "将YouTube、Twitter、Instagram请求重定向到隐私友好的替代前端和后端的Web扩展。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "广告拦截",
          "originalName": "Ad Blocker",
          "id": "ad-blocker",
          "websites": [
            {
              "name": "Adblock Tester",
              "url": "https://adblock-tester.com/",
              "description": "检查AdBlock扩展有效性的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "12ft",
              "url": "https://12ft.io/",
              "description": "移除付费墙并获得文章访问权限。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Incoggo",
              "url": "https://incoggo.com/",
              "description": "针对付费墙的广告拦截器。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "邮箱",
          "originalName": "Emails",
          "id": "emails",
          "websites": [
            {
              "name": "BurnerMail",
              "url": "https://burnermail.io/",
              "description": "保护您的个人电子邮件地址，控制谁可以向您发送电子邮件，一键生成新的临时地址。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Kill the Newsletter",
              "url": "https://kill-the-newsletter.com/",
              "description": "将电子邮件通讯转换为Atom提要以便于消费。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Dead Man's Switch",
              "url": "https://www.deadmansswitch.net/",
              "description": "如果您在指定时间内不与其交互，则将您的电子邮件发送给指定收件人的服务。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "YAMM",
              "url": "https://yamm.com/",
              "description": "Gmail的邮件合并，允许您使用Gmail发送大量邮件，确保投递到主收件箱。直接从Google Sheets实时跟踪结果。",
              "isPaid": true,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "临时邮箱",
          "originalName": "Disposable Email",
          "id": "disposable-email",
          "websites": [
            {
              "name": "Erine Email",
              "url": "https://erine.email/",
              "description": "为您现有电子邮件地址提供的反垃圾邮件服务。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Maildrop",
              "url": "https://maildrop.cc/",
              "description": "当您不想透露真实电子邮件地址时使用Maildrop。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mailsac",
              "url": "https://mailsac.com/",
              "description": "为测试和开发目的提供临时电子邮件服务。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "InboxKitten",
              "url": "https://inboxkitten.com/",
              "description": "开源的临时电子邮件服务。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Guerrilla Mail",
              "url": "https://www.guerrillamail.com/inbox",
              "description": "允许您撰写电子邮件并确定临时电子邮件的域名、主密码短语和密码。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "EmailDrop",
              "url": "https://www.emaildrop.io/",
              "description": "以高度简约的设计创建具有自定义或随机域名的电子邮件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GetNada",
              "url": "https://getnada.com/",
              "description": "提供临时电子邮件地址。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GetNotify",
              "url": "https://www.getnotify.com/",
              "description": "免费的电子邮件跟踪服务，当您发送的电子邮件被阅读时通知您。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MXToolbox",
              "url": "https://mxtoolbox.com/",
              "description": "在一个地方提供免费的DNS和电子邮件工具，简化故障排除。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Postale",
              "url": "https://postale.io/",
              "description": "允许您在几分钟内轻松创建域名电子邮件地址。",
              "isPaid": true,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "数据泄露",
          "originalName": "Data Breach",
          "id": "data-breach",
          "websites": [
            {
              "name": "Have I Been Pwned",
              "url": "https://haveibeenpwned.com/",
              "description": "检查您的电子邮件或手机是否在数据泄露中。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TinEye",
              "url": "https://tineye.com/",
              "description": "查找图像在线出现的位置。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IPLeak",
              "url": "https://ipleak.net/",
              "description": "查看您访问的所有网站可以看到和收集的关于您的信息类型。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Cover Your Tracks",
              "url": "https://coveryourtracks.eff.org/",
              "description": "测试您的浏览器，查看您在跟踪和指纹识别方面的保护程度。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Am I FLoCed",
              "url": "https://amifloced.org/",
              "description": "检查Google是否在您的Chrome浏览器上测试FLoC。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "搜索",
          "originalName": "Search",
          "id": "search",
          "websites": [
            {
              "name": "Startpage",
              "url": "https://www.startpage.com/",
              "description": "在整个网络上获得隐私保护。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Neeva",
              "url": "https://neeva.com/",
              "description": "无广告的私人搜索引擎，采用订阅模式。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AndiSearch",
              "url": "https://andisearch.com/",
              "description": "无广告和匿名搜索引擎。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Plex Page",
              "url": "https://plex.page/",
              "description": "搜索摘要工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Marginalia Search",
              "url": "https://search.marginalia.nu/",
              "description": "专注于非商业内容的独立DIY搜索引擎。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Unlisted Videos",
              "url": "https://unlistedvideos.com/",
              "description": "用于提交、搜索和观看未列出的YouTube视频。无需注册。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Filmot",
              "url": "https://filmot.com/",
              "description": "在YouTube字幕中搜索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "XN--1-ZFA",
              "url": "https://xn--1-zfa.com/",
              "description": "Google、DuckDuckGo、Twitter、YouTube、Reddit的高级搜索。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Seekr",
              "url": "https://www.seekr.com/",
              "description": "利用AI分析和评分内容质量的搜索引擎，从新闻文章开始。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Million Short",
              "url": "https://millionshort.com/",
              "description": "发现前百万搜索结果之外的内容。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "互联网",
          "originalName": "Internet",
          "id": "internet",
          "websites": [
            {
              "name": "Internet Live Stats",
              "url": "https://www.internetlivestats.com/",
              "description": "提供有关互联网的实时统计信息，包括网站数量、电子邮件等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Internet Map",
              "url": "https://internet-map.net/",
              "description": "可视化表示互联网的交互式地图，显示各种网站之间的关系。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Test IPv6",
              "url": "https://test-ipv6.com/",
              "description": "允许您测试IPv6连接性并提供有关网络设置的信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TLS 1.2 Explained",
              "url": "https://tls12.xargs.org/",
              "description": "提供TLS连接中每个字节的详细解释。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CIDR.xyz",
              "url": "https://cidr.xyz/",
              "description": "交互式IP地址和CIDR范围可视化工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "I Can Haz IP",
              "url": "https://icanhazip.com/",
              "description": "在页面顶部仅显示您的IP地址。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IP Location",
              "url": "https://iplocation.io/",
              "description": "提供输入IP地址的免费位置跟踪，包括城市、国家、纬度和经度。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ifconfig.co",
              "url": "https://ifconfig.co/",
              "description": "帮助您找到自己的IP地址并提供相关信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "IPinfo.io",
              "url": "https://ipinfo.io/",
              "description": "为各种用例提供准确的IP地址数据。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Visual Subnet Calculator",
              "url": "https://www.davidc.net/sites/default/subnets/subnets.html",
              "description": "帮助进行IP地址和子网计算的可视化子网计算器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Ping Test",
              "url": "https://www.meter.net/ping-test/",
              "description": "通过向指定服务器发送测试数据包（ping）来测量网络延迟的在线工具，提供连接稳定性和速度的见解。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LibreSpeed",
              "url": "https://librespeed.org/",
              "description": "开源互联网速度测试工具，提供下载、上传和延迟的准确测量，无需安装额外软件。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "DNS",
          "originalName": "DNS",
          "id": "dns",
          "websites": [
            {
              "name": "How DNS Works",
              "url": "https://howdns.works/",
              "description": "通过漫画的帮助提供DNS工作原理的生动解释。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DNS Checker",
              "url": "https://dnschecker.org/",
              "description": "提供免费的DNS查找服务，检查域名系统记录对全球选定DNS服务器列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AdGuard DNS Providers",
              "url": "https://kb.adguard.com/en/general/dns-providers",
              "description": "DNS提供商列表，您可以配置使用以替代路由器或ISP提供的默认设置。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "DNS Speed Test",
              "url": "https://dnsspeedtest.online/",
              "description": "快速DNS服务器速度测试工具，允许用户找到最佳DNS服务器以实现更快的互联网浏览，无需安装。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "URL工具",
          "originalName": "URL",
          "id": "url",
          "websites": [
            {
              "name": "URL Tools",
              "url": "https://shrtco.de/tools/",
              "description": "各种URL工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OneLink",
              "url": "https://www.onelink.to/",
              "description": "为您的应用创建链接和二维码的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "QR Code Generator",
              "url": "https://freecodetools.org/qr-code-generator/",
              "description": "生成二维码的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "AppOpener",
              "url": "https://appopener.com/",
              "description": "创建智能链接以从URL打开所需应用，无需登录。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Who Is Hosting Any Domain",
              "url": "https://digital.com/best-web-hosting/who-is/",
              "description": "查找任何域名的托管商，包括网络主机、IP地址、名称服务器等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Webhook.Site",
              "url": "https://webhook.site/",
              "description": "检查、测试和自动化任何传入HTTP请求或电子邮件的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GoQR.Me",
              "url": "https://goqr.me/",
              "description": "生成适合打印的高分辨率二维码的生成器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Graph Generator",
              "url": "https://freecodetools.org/ogp/",
              "description": "生成Open Graph图像的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "短链接",
          "originalName": "URL Shortener",
          "id": "url-shortener",
          "websites": [
            {
              "name": "Rebrandly",
              "url": "https://www.rebrandly.com/",
              "description": "使用自定义域名品牌化、跟踪和分享短URL的链接管理平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bit.do",
              "url": "https://bit.do/",
              "description": "免费为您的链接提供实时流量统计。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "s.id",
              "url": "https://home.s.id/",
              "description": "链接缩短器和微网站构建器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TinyURL",
              "url": "https://tinyurl.com/app",
              "description": "提供可选的短链接结尾。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Tiny.cc",
              "url": "https://tiny.cc/",
              "description": "提供可选的短链接结尾。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bitly",
              "url": "https://bitly.com/",
              "description": "Web/移动链接管理和活动管理分析以及品牌链接。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "iplogger.org",
              "url": "https://iplogger.org/",
              "description": "具有高级分析功能的URL缩短器，分析通过您的链接的流量、在线商店、博客或网站的访客。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "cutr.ml",
              "url": "https://cutr.ml/",
              "description": "URL缩短器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "is.gd",
              "url": "https://is.gd/",
              "description": "URL缩短器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "gg.gg",
              "url": "https://gg.gg/",
              "description": "URL缩短器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "shrunken.com",
              "url": "https://www.shrunken.com/",
              "description": "URL缩短器。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "VPN",
          "originalName": "VPN",
          "id": "vpn",
          "websites": [
            {
              "name": "Top10VPN",
              "url": "https://www.top10vpn.com/",
              "description": "提供各种VPN服务信息和评论的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "What's My Browser",
              "url": "https://www.whatsmybrowser.org/",
              "description": "检查您的浏览器详细信息，包括版本和插件。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "VPN Comparison Spreadsheet",
              "url": "https://docs.google.com/spreadsheets/d/1ijfqfLrJWLUVBfJZ_YalVpstWsjw-JGzkvMd6u2jqEk/edit#gid=231869418",
              "description": "包含分析和比较的所有VPN详细列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Njalla",
              "url": "https://njal.la/",
              "description": "提供从您的计算机到互联网的加密隧道，将您的真实IP地址隐藏在他们的IP地址后面。",
              "isPaid": true,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "虚假信息生成",
          "originalName": "Fake Information Generation",
          "id": "fake-information-generation",
          "websites": [
            {
              "name": "Fake Name Generator",
              "url": "https://www.fakenamegenerator.com/",
              "description": "生成虚假个人资料信息，包括37种语言和31个国家的姓名、地址和电话号码。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Burner",
              "url": "https://www.burnerapp.com/",
              "description": "创建临时第二个电话号码用于通话和短信的应用，对隐私保护和避免垃圾信息有用。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Random.org",
              "url": "https://www.random.org/",
              "description": "生成真随机数、列表、字符串和映射的服务。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "密码生成",
          "originalName": "Password Generation",
          "id": "password-generation",
          "websites": [
            {
              "name": "Everything About Passwords and Internet Security",
              "url": "https://www.healthypasswords.com/index-2.html",
              "description": "关于密码和互联网安全的综合信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Random Password Generator",
              "url": "https://random-password-gen.web.app/",
              "description": "生成随机和安全的密码。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "How Secure Is My Password?",
              "url": "https://www.security.org/how-secure-is-my-password/",
              "description": "检查密码的安全强度。输入是安全的，不会被存储或分享。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Password Generator",
              "url": "https://freecodetools.org/password-generator/",
              "description": "轻松生成安全密码。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "ToS;DR",
          "url": "https://tosdr.org/",
          "description": "服务条款；没有阅读（简称：ToS;DR）。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Nothing Private",
          "url": "https://www.nothingprivate.ml/",
          "description": "检查为什么使用隐私浏览模式或无痕模式时您并不匿名。您也可以在[这里](https://github.com/gautamkrishnar/nothing-private)阅读。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "TrustPage",
          "url": "https://trustpage.com/directory",
          "description": "查找和比较数千家公司的安全政策，根据从网络上获取的安全政策选择合适的软件和服务。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Arkenfox User.js",
          "url": "https://github.com/arkenfox/user.js/wiki/4.1-Extensions",
          "description": "隐私和安全相关的浏览器扩展。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Cookiepedia",
          "url": "https://cookiepedia.co.uk/",
          "description": "最大的预分类Cookie和在线跟踪技术数据库。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Security Planner",
          "url": "https://securityplanner.consumerreports.org/",
          "description": "获得定制建议以减少数据收集并防止黑客攻击。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "MyWhisper",
          "url": "https://mywhisper.net/",
          "description": "基于AES密码学的文本加密工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BeEncrypted",
          "url": "https://beencrypted.com/",
          "description": "变得更加加密的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Prism Break",
          "url": "https://prism-break.org/en/all/",
          "description": "最常用应用程序和服务的私密安全替代方案。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Reddit Piracy Megathread",
          "url": "https://www.reddit.com/r/piracy/wiki/megathread/tools",
          "description": "应用程序、工具和Web服务列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ressources",
          "url": "https://gitlab.com/tzkuat/Ressources",
          "description": "不同领域网站列表，如安全、OSINT等。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Privacy Tools List by CHEF-KOCH",
          "url": "https://chef-koch.bearblog.dev/privacy-tools-list-by-chef-koch/",
          "description": "CHEF-KOCH的综合隐私工具列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Privacy Analyzer",
          "url": "https://privacy.net/analyzer/#pre-load",
          "description": "分析网站的隐私设置。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Shut Up Trackers",
          "url": "https://shutuptrackers.com/",
          "description": "提供保护数据安全和隐私的信息。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "EFF Surveillance Self-Defense",
          "url": "https://ssd.eff.org/",
          "description": "更安全在线通信的提示、工具和操作指南。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Router Passwords",
          "url": "https://www.routerpasswords.com/",
          "description": "互联网上最新的默认路由器密码库。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BugMeNot",
          "url": "https://bugmenot.com/",
          "description": "查找和分享各种网站的登录信息。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Security.org",
          "url": "https://www.security.org/security-score/",
          "description": "了解您的安全评分。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "LibreProjects",
          "url": "https://libreprojects.net/#favs=wikipedia,joindiaspora-com,nextcloud,openstreetmap,jamendo,plos",
          "description": "118个开源托管Web服务列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Privnote",
          "url": "https://privnote.com/",
          "description": "发送阅读后会自毁的笔记。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Dangerzone",
          "url": "https://dangerzone.rocks/",
          "description": "将潜在不安全的PDF、Office文档或图像转换为安全可查看格式的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ExifTool",
          "url": "https://exiftool.org/",
          "description": "读取、写入和编辑图像、音频和视频文件元数据的软件。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "HideMyWeb",
          "url": "https://hidemyweb.wordpress.com/",
          "description": "隐藏、模糊和突出显示网页内容的工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Browser.lol",
          "url": "https://browser.lol/",
          "description": "在浏览器内使用免费虚拟环境匿名浏览。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "OneTimeSecret",
          "url": "https://onetimesecret.com/",
          "description": "使用一次性链接安全分享密码、秘密消息或私人链接的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "MetaDefender",
          "url": "https://metadefender.opswat.com/",
          "description": "扫描和分析文件安全威胁的在线工具，提供潜在风险的详细报告。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "FotoForensics",
          "url": "https://fotoforensics.com/",
          "description": "分析和验证数字图像的在线工具，提供检测照片修改和编辑的取证工具。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "软件工具",
      "originalName": "Softwares",
      "id": "softwares",
      "subcategories": [
        {
          "name": "代码片段",
          "originalName": "Snippets",
          "id": "snippets",
          "websites": [
            {
              "name": "CodeClippet",
              "url": "https://codeclippet.com/",
              "description": "在以社区为中心的环境中分享代码片段的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Kod.so",
              "url": "https://kod.so/",
              "description": "创建可视化代码片段的平台，具有下载和分享功能。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodePNG",
              "url": "https://www.codepng.app/",
              "description": "通过将代码转换为图像来从源代码创建图片的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodeMyUI",
              "url": "https://codemyui.com/",
              "description": "提供网页设计和UI灵感以及精选代码片段的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "30 Seconds of Code",
              "url": "https://www.30secondsofcode.org/list/p/1",
              "description": "为各种开发需求精选的短代码片段集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "W3Schools HowTo",
              "url": "https://www.w3schools.com/howto/default.asp",
              "description": "W3Schools提供HTML、CSS和JavaScript代码片段的部分。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Snipplr",
              "url": "https://snipplr.com/",
              "description": "将常用代码片段保存在一个可访问位置的平台，允许用户分享和发现代码片段。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "LittleSnippets",
              "url": "https://littlesnippets.net/",
              "description": "分享和发现小代码片段的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodeToGo",
              "url": "https://codetogo.io/",
              "description": "查找JavaScript和React用例的最新代码片段的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TweetSnippet",
              "url": "https://tweetsnippet.com/",
              "description": "来自Twitter的技巧和窍门精选列表，以代码片段形式呈现。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CSS-Tricks Snippets",
              "url": "https://css-tricks.com/snippets/",
              "description": "涵盖各种设计和布局技术的CSS代码片段集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Crontab Guru",
              "url": "https://crontab.guru/",
              "description": "用于cron调度表达式的快速简单编辑器，提供人类可读的解释。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Crontab Generator",
              "url": "https://crontab-generator.org/",
              "description": "生成cron调度表达式的在线工具，具有用户友好的界面。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Carbon",
              "url": "https://carbon.now.sh/",
              "description": "创建和分享具有语法高亮的源代码片段美丽图像的基于Web的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "代码检查",
          "originalName": "Linters",
          "id": "linters",
          "websites": [
            {
              "name": "FromLatest.io",
              "url": "https://www.fromlatest.io/#/",
              "description": "用于检查Dockerfile语法和最佳实践的Dockerfile代码检查器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "YAMLlint",
              "url": "https://www.yamllint.com/",
              "description": "检查YAML代码有效性并提供为Ruby优化的干净UTF-8版本的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "K8sYaml",
              "url": "https://k8syaml.com/",
              "description": "用于创建Kubernetes配置文件的Kubernetes YAML生成器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Puppet Validator",
              "url": "https://validate.puppet.com/",
              "description": "检查Puppet代码语法有效性的工具，无需编译或强制执行目录。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ShellCheck",
              "url": "https://www.shellcheck.net/",
              "description": "通过分析并提供改进建议来查找shell脚本中bug的在线平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Beautifier.io",
              "url": "https://beautifier.io/",
              "description": "美化、解压或反混淆JavaScript和HTML，并使JSON/JSONP可读的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JSONLint",
              "url": "https://jsonlint.com/",
              "description": "JSON验证器和重新格式化工具，为混乱的JSON代码提供整理和验证。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Algorithm Visualizer",
              "url": "https://algorithm-visualizer.org/",
              "description": "从代码可视化算法以帮助理解其执行的交互式平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodeBeautify",
              "url": "https://codebeautify.org/",
              "description": "提供代码格式化工具的在线平台，包括JSON美化器、XML查看器、十六进制转换器等。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ExplainShell",
              "url": "https://explainshell.com/",
              "description": "通过显示每个参数的帮助文本来解释命令行命令的工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ShowTheDocs",
              "url": "https://showthedocs.com/",
              "description": "为您的代码查找相关文档的文档浏览器，使探索和理解变得更容易。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Mango - Specification Interpretation",
              "url": "https://mango-slra1-ckwssph7iq-ue.a.run.app/readme",
              "description": "解释规格说明并自动识别逻辑矛盾、过度复杂性和实体名称不一致等缺陷的技术。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Code Minifier",
              "url": "https://freecodetools.org/minifier/",
              "description": "用于压缩代码以减少文件大小和提高加载时间的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Markdown Preview",
              "url": "https://freecodetools.org/markdown-preview/",
              "description": "预览和格式化Markdown文本的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Code Beautifier",
              "url": "https://freecodetools.org/beautifier/",
              "description": "美化和格式化代码以提高可读性的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodePad",
              "url": "https://codepad.org/",
              "description": "在线编译器/解释器和协作工具，允许用户使用短URL运行和分享代码片段。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "JSON Formatter",
              "url": "https://jsonformatter.org/json-parser",
              "description": "在线JSON解析器和格式化工具。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "测试",
          "originalName": "Testing",
          "id": "testing",
          "websites": [
            {
              "name": "OWASP Fuzzing Project",
              "url": "https://owasp.org/www-community/Fuzzing",
              "description": "OWASP模糊测试项目的官方页面，旨在改善模糊测试工具、技术和流程的整体状态。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Awesome Fuzzing",
              "url": "https://github.com/secfigo/Awesome-Fuzzing",
              "description": "模糊测试资源的精选列表，包括工具、教程、研究论文等。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "正则表达式",
          "originalName": "Regex",
          "id": "regex",
          "websites": [
            {
              "name": "Regex Cheat Sheet",
              "url": "https://dev.to/emmabostian/regex-cheat-sheet-2j2a",
              "description": "正则表达式的有用指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "RegExr",
              "url": "https://regexr.com/",
              "description": "学习、构建和测试正则表达式（RegEx/RegExp）的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Regulex",
              "url": "https://jex.im/regulex/",
              "description": "用于理解和可视化正则表达式的JavaScript正则表达式可视化工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Regex101",
              "url": "https://regex101.com/",
              "description": "在线正则表达式测试器和调试器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Debuggex",
              "url": "https://www.debuggex.com/",
              "description": "可视化正则表达式测试器，让您了解正则表达式的工作原理。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "UI Bakery Regex Library",
              "url": "https://uibakery.io/regex-library/",
              "description": "用于UI设计的正则表达式集合。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "无代码",
          "originalName": "No-Code",
          "id": "no-code",
          "websites": [
            {
              "name": "No Code List",
              "url": "https://nocodelist.co/",
              "description": "浏览类别以发现超过300个无代码工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "No-Code Things",
              "url": "https://www.spacebarcounter.net/no-code-tools",
              "description": "无代码工具集合。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "许可证",
          "originalName": "Licensing",
          "id": "licensing",
          "websites": [
            {
              "name": "ChooseALicense - Appendix",
              "url": "https://choosealicense.com/appendix/",
              "description": "与开源许可证相关的附加信息和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitHub Docs - Licensing a Repository",
              "url": "https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/licensing-a-repository",
              "description": "GitHub关于存储库许可的文档。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Public License Selector",
              "url": "https://ufal.github.io/public-license-selector/",
              "description": "帮助您根据偏好选择开源许可证。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Creative Commons License Chooser",
              "url": "https://creativecommons.org/choose/",
              "description": "选择适合您内容分享偏好的知识共享许可证。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "License Buttons",
              "url": "https://licensebuttons.net/",
              "description": "获取指示您内容许可条款的网站按钮。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Shields.io",
              "url": "https://shields.io/",
              "description": "为您的GitHub存储库创建自定义徽章（盾牌），提供许可证、版本等信息。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "OlderGeeks",
          "url": "https://oldergeeks.com/",
          "description": "由捐赠支持的无广告软件下载网站，提供无忧体验。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "AlternativeTo",
          "url": "https://alternativeto.net/",
          "description": "基于用户推荐发现流行软件的最佳替代方案。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Open Source Alternative",
          "url": "https://www.opensourcealternative.to/",
          "description": "提供专有软件开源替代方案的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Dark Mode List",
          "url": "https://darkmodelist.com/",
          "description": "支持暗黑模式的300个应用列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "LocalStack",
          "url": "https://localstack.cloud/",
          "description": "离线开发和测试云和无服务器应用。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Markwhen",
          "url": "https://markwhen.com/",
          "description": "将类markdown文本转换为级联时间线的文本到时间线工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Asciinema",
          "url": "https://asciinema.org/",
          "description": "以纯文本方式记录和分享您的终端会话。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Apache Guacamole",
          "url": "https://guacamole.apache.org/",
          "description": "支持VNC、RDP和SSH等协议的无客户端远程桌面网关。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "DockerSwarm.rocks",
          "url": "https://dockerswarm.rocks/",
          "description": "使用Docker Compose文件将应用程序堆栈部署到分布式集群的生产环境。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "EasyCron",
          "url": "https://www.easycron.com/",
          "description": "在线定时任务服务（付费）。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CDecl",
          "url": "https://cdecl.org/",
          "description": "将C语言难懂的声明翻译为英语，帮助您理解复杂的C声明。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "NirSoft",
          "url": "https://www.nirsoft.net/",
          "description": "Windows小工具集合，包括系统工具、密码恢复工具等。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Ninite",
          "url": "https://ninite.com/",
          "description": "一次安装和更新多个程序，无工具栏或不必要的点击。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "ReadWok",
          "url": "https://app.readwok.com/lib",
          "description": "具有渐进式查看模式的在线文本阅读器，允许逐段阅读和编辑。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "BitwiseCMD",
          "url": "https://bitwisecmd.com/",
          "description": "在线位运算和转换。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Commands.dev",
          "url": "https://www.commands.dev/?ref=producthunt",
          "description": "可搜索的模板化流行终端命令目录，从互联网各处精选。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "SourceForge",
          "url": "https://sourceforge.net/",
          "description": "基于Web的服务，允许您比较、下载和开发开源和商业软件。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "编程语言",
      "originalName": "Programming Languages",
      "id": "programming-languages",
      "subcategories": [
        {
          "name": "Haskell",
          "originalName": "Haskell",
          "id": "haskell",
          "websites": [
            {
              "name": "Learn You a Haskell for Great Good!",
              "url": "https://learnyouahaskell.github.io/chapters.html",
              "description": "Miran Lipovača著的\"Learn You a Haskell\"在线图书版本。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Real World Haskell",
              "url": "https://book.realworldhaskell.org/read/",
              "description": "Bryan O'Sullivan、Don Stewart和John Goerzen著的\"Real World Haskell\"在线图书版本。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CIS 194 - Introduction to Haskell (Spring 2013)",
              "url": "https://www.seas.upenn.edu/~cis1940/spring13/lectures.html",
              "description": "Haskell编程的讲座材料和资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitHub - Programming in Haskell",
              "url": "https://github.com/topics/programming-in-haskell",
              "description": "GitHub上Haskell相关存储库和项目的集合。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "Python",
          "originalName": "Python",
          "id": "python",
          "websites": [
            {
              "name": "Python Documentation",
              "url": "https://docs.python.org/3/",
              "description": "Python编程语言的官方文档。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Python Wiki",
              "url": "https://wiki.python.org/moin/FrontPage",
              "description": "提供Python相关信息和资源的协作平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Awesome Python",
              "url": "https://awesome-python.com/",
              "description": "精选的优秀Python框架、库、软件和资源列表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Project Python",
              "url": "https://projectpython.net/",
              "description": "提供Python教程和学习开发资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learn Python",
              "url": "https://www.learnpython.org/",
              "description": "提供教程和练习来学习Python编程的交互式平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CSCircles",
              "url": "https://cscircles.cemc.uwaterloo.ca/",
              "description": "滑铁卢大学开发的提供交互式Python练习和资源的在线平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PythonBasics",
              "url": "https://pythonbasics.org/",
              "description": "提供学习Python基础资源和教程的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Majyori",
              "url": "https://www.majyori.com/",
              "description": "为所有级别学习者提供Python教程和资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PyFormat",
              "url": "https://pyformat.info/",
              "description": "比较不同版本Python格式化样式并提供示例的网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Learn by Example - Python Resources",
              "url": "https://learnbyexample.github.io/py_resources/",
              "description": "各种主题的Python资源和教程集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Data to Fish",
              "url": "https://datatofish.com/python-tutorials/",
              "description": "提供清晰Python教程的平台，涵盖机器学习、数据库、pandas、GUI等主题。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Notion Programming Course",
              "url": "https://www.notion.so/Programming-Course-4d4331de1a0c4ae894133cb1ca1e9315",
              "description": "在Notion上托管的学习用Django构建基本Web应用程序的自学课程。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "C++",
          "originalName": "C++",
          "id": "c--",
          "websites": [
            {
              "name": "LearnCpp.com",
              "url": "https://www.learncpp.com/",
              "description": "学习C++编程的综合资源。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "EbookFoundation-Free-Programming-Books",
          "url": "https://github.com/EbookFoundation/free-programming-books/blob/main/books/free-programming-books-langs.md",
          "description": "免费和开源编程书籍",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Codecademy Catalog",
          "url": "https://www.codecademy.com/catalog",
          "description": "提供学习各种编程语言和技术课程目录的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Learn X in Y Minutes",
          "url": "https://learnxinyminutes.com/",
          "description": "提供各种编程语言快速概览的资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "eComputerNotes",
          "url": "https://ecomputernotes.com/",
          "description": "在线教育学习资源，涵盖广泛的计算机科学和编程主题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Libraries.io",
          "url": "https://libraries.io/",
          "description": "发现新的开源包、模块和框架，并跟踪依赖关系的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "LearnByExample",
          "url": "https://www.learnbyexample.org/",
          "description": "通过示例和实际解释学习Python、SQL和R语言的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "PythonTutor",
          "url": "https://pythontutor.com/",
          "description": "通过逐步可视化代码执行帮助用户学习Python、JavaScript、C、C++和Java编程的工具。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Classic Papers in Programming Languages and Logic",
          "url": "https://www.cs.cmu.edu/~crary/819-f09/",
          "description": "卡内基梅隆大学精选的编程语言和逻辑开创性学术论文集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "RubyGems Guides",
          "url": "https://guides.rubygems.org/",
          "description": "学习RubyGems的工作原理以及如何制作自己的gem",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "编程练习",
      "originalName": "Coding Practice / Competitive Programming",
      "id": "coding-practice---competitive-programming",
      "subcategories": [
        {
          "name": "CTF",
          "originalName": "Capture the Flag",
          "id": "capture-the-flag",
          "websites": [
            {
              "name": "CTF101",
              "url": "https://ctf101.org/",
              "description": "为Capture The Flag竞赛新手提供教育资源的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CTFtime",
              "url": "https://ctftime.org/",
              "description": "Capture The Flag事件、团队和时间线的档案。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Google CTF on GitHub",
              "url": "https://github.com/google/google-ctf",
              "description": "GitHub上可用的Google Capture The Flag竞赛资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Meusec CTF Resources",
              "url": "https://www.meusec.com/ctf/capture-the-flags-in-cybersecurity/",
              "description": "Meusec在网络安全领域的Capture The Flag (CTF)资源集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Trail of Bits - CTF Guide",
              "url": "https://trailofbits.github.io/ctf/",
              "description": "Trail of Bits提供参与Capture The Flag竞赛的见解和技巧的指南。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "项目",
          "originalName": "Projects",
          "id": "projects",
          "websites": [
            {
              "name": "Projects-Solutions on GitHub",
              "url": "https://github.com/karan/Projects-Solutions",
              "description": "提供基于项目的编程挑战以通过实际应用学习编程的GitHub存储库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Build Your Own X",
              "url": "https://github.com/codecrafters-io/build-your-own-x#build-your-own-neural-network",
              "description": "提供从零开始构建您喜爱技术指南的GitHub存储库。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Arduino Project Hub",
              "url": "https://projecthub.arduino.cc/",
              "description": "分享和发现Arduino项目的中心。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Projects in Networking",
              "url": "https://projectsinnetworking.com/",
              "description": "为计算机网络和安全领域的学生、毕业生和专业人士提供网络项目、网络安全项目、网络安全案例研究和源代码的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "开源",
          "originalName": "Open Source",
          "id": "open-source",
          "websites": [
            {
              "name": "LibHunt",
              "url": "https://www.libhunt.com/",
              "description": "发现趋势开源项目及其替代方案的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Awesome Open Source",
              "url": "https://awesomeopensource.com/",
              "description": "通过在各种类别和项目中搜索、浏览和组合主题来查找开源项目的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Source Libs",
              "url": "https://opensourcelibs.com/",
              "description": "世界最佳开源软件的大量集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CodeTriage",
              "url": "https://www.codetriage.com/",
              "description": "您可以贡献的开源存储库问题列表，如果注册可选择通过电子邮件接收问题。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitLab Explore",
              "url": "https://gitlab.com/explore/projects/starred/",
              "description": "在GitLab上探索项目。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Open Source Guide",
              "url": "https://opensource.guide/",
              "description": "提供启动和发展开源项目资源的指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "The Architecture of Open Source Applications",
              "url": "https://aosabook.org/en/index.html",
              "description": "关于各种开源软件项目设计和架构的书籍系列。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "OSS Gallery",
              "url": "https://oss.gallery/",
              "description": "互联网上最佳开源项目的众包集合，提供发现和探索顶级软件存储库和工具的简便方法。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "黑客马拉松",
          "originalName": "Hackathons",
          "id": "hackathons",
          "websites": [
            {
              "name": "DEVPOST Hackathons",
              "url": "https://devpost.com/hackathons",
              "description": "查找在线和线下黑客马拉松。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "GitHub Education Events",
              "url": "https://education.github.com/events",
              "description": "在您附近的黑客马拉松、会议和活动中找到社区成员。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Hackathons by Hack Club",
              "url": "https://hackathons.hackclub.com/",
              "description": "精选的高中黑客马拉松列表，包含27个州和18个国家的699个活动。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Cerebral Valley AI Events",
              "url": "https://events.cerebralvalley.ai/",
              "description": "提供即将到来的AI相关活动、黑客马拉松和共同工作机会的信息。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "LeetCode",
          "url": "https://leetcode.com/",
          "description": "专注于算法挑战的编程问题练习平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "NeetCode",
          "url": "https://neetcode.io/",
          "description": "通过现实项目和协作编程提升技能的编程练习网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "HackerRank",
          "url": "https://www.hackerrank.com/dashboard",
          "description": "提供从算法到人工智能各领域挑战的编程平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CodeWars",
          "url": "https://www.codewars.com/",
          "description": "社区驱动的平台，通过同行评审解决方案提供编程挑战以改进技能。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Project Euler",
          "url": "https://projecteuler.net/about",
          "description": "以数学为导向的编程平台，通过具有挑战性的问题鼓励通过编程解决问题。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Kattis Guide",
          "url": "https://unh-cpc.github.io/kattisguide.html",
          "description": "使用Kattis平台解决竞技编程问题的指南，为参与者提供技巧、解题策略和资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Kaggle",
          "url": "https://www.kaggle.com/",
          "description": "举办竞赛、数据集和笔记本的数据科学平台，促进该领域的合作和创新。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Replit",
          "url": "https://replit.com/",
          "description": "促进协作编程的在线编程环境，具有实时分享和广泛语言支持等功能。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "AlgoLeague",
          "url": "https://www.algoleague.com/",
          "description": "练习算法问题解决的竞技编程平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Codeforces",
          "url": "https://codeforces.com/",
          "description": "提供竞赛和大量题目集的在线竞技编程平台，吸引多样化的国际用户群体。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "AtCoder",
          "url": "https://atcoder.jp/",
          "description": "举办竞赛和练习以增强算法技能的日本竞技编程平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "InterviewBit",
          "url": "https://www.interviewbit.com/coding-interview-questions/",
          "description": "提供编程面试问题和挑战以帮助准备技术面试的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Advent of Code",
          "url": "https://adventofcode.com/",
          "description": "适合各种技能水平的小型编程谜题降临日历，可用任何编程语言解决。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CList",
          "url": "https://clist.by/",
          "description": "来自各种编程网站的竞赛列表，为即将到来的竞赛提供集中资源。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Codeply",
          "url": "https://www.codeply.com/",
          "description": "具有数十个框架、启动模板和超过50,000个代码片段的免费在线编辑器。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "URI Online Judge",
          "url": "https://www.urionlinejudge.com.br/judge/en/login",
          "description": "用于训练算法和编程挑战以增强编程技能的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Rosalind",
          "url": "https://rosalind.info/problems/locations/",
          "description": "通过解决问题学习生物信息学和编程的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Kattis",
          "url": "https://open.kattis.com/",
          "description": "拥有数百个编程问题的平台，帮助用户练习和提高编程技能。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Exercism",
          "url": "https://exercism.org/tracks",
          "description": "通过学习、练习和指导的独特结合免费开发67种编程语言流利度的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Codility",
          "url": "https://codility.com/programmers/challenges",
          "description": "提供编程挑战以评估和改进算法和问题解决技能的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "r/dailyprogrammer",
          "url": "https://www.reddit.com/r/dailyprogrammer/",
          "description": "Reddit上为编程爱好者提供每日编程挑战和讨论的子版块。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Daily Coding Problem",
          "url": "https://www.dailycodingproblem.com/",
          "description": "向订阅者发送每日编程挑战以改进编程和问题解决技能的服务。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Coderbyte",
          "url": "https://coderbyte.com/",
          "description": "具有编程挑战和面试准备资源以增强编程技能的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CodinGame",
          "url": "https://www.codingame.com/start",
          "description": "游戏化编程挑战的在线平台，使学习和练习编程更具吸引力。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "A2OJ",
          "url": "https://a2oj.netlify.app/",
          "description": "提供结构化阶梯系统以改进问题解决技能的竞技编程资源，按难度级别和主题分类。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CodeChef",
          "url": "https://www.codechef.com/",
          "description": "定期举办竞赛并拥有大型题库的竞技编程网站，面向全球编程社区。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "USACO",
          "url": "http://usaco.org/index.php?page=contests",
          "description": "美国计算奥林匹克平台，提供编程竞赛以鼓励和识别美国有才华的年轻程序员。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "USACO Guide",
          "url": "https://usaco.guide/",
          "description": "在竞技编程中指导用户从青铜级到白金级及以上的综合免费资源集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "JoinCPI",
          "url": "https://joincpi.org/",
          "description": "致力于通过资源、课程、推广和竞赛在学生中推广竞技编程的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CP-Algorithms",
          "url": "https://cp-algorithms.com/",
          "description": "提供专门为竞技编程量身定制的算法详细指南的网站，提供深入解释和示例。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "CSS Battle",
          "url": "https://cssbattle.dev/",
          "description": "挑战用户使用CSS技能以最小代码复制目标的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "JavaScript Quiz",
          "url": "https://javascriptquiz.com/",
          "description": "提供专注于测试和增强JavaScript编程语言知识的测验的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Elevator Saga",
          "url": "https://play.elevatorsaga.com/",
          "description": "学习和练习JavaScript的游戏。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Deep ML",
          "url": "https://www.deep-ml.com/",
          "description": "提供机器学习代码挑战的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Hack The Box",
          "url": "https://www.hackthebox.com/",
          "description": "提供虚拟环境和挑战以帮助个人和组织发展网络安全技能的黑客训练在线平台。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "速查表",
      "originalName": "Cheat Sheets",
      "id": "cheat-sheets",
      "subcategories": [
        {
          "name": "Python速查表",
          "originalName": "Python Cheat Sheet",
          "id": "python-cheat-sheet",
          "websites": [
            {
              "name": "Speedsheet",
              "url": "https://speedsheet.io/",
              "description": "用于更快更好编程的交互式Python速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Python Cheatsheet",
              "url": "https://www.pythoncheatsheet.org/",
              "description": "基于《用Python自动化繁琐工作》一书和其他各种来源的速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Zero to Mastery Python Cheat Sheet",
              "url": "https://zerotomastery.io/cheatsheets/python-cheat-sheet/",
              "description": "Zero to Mastery提供的Python速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "前端速查表",
          "originalName": "Front-end Cheat Sheet",
          "id": "front-end-cheat-sheet",
          "websites": [
            {
              "name": "Can I use",
              "url": "https://caniuse.com/",
              "description": "为桌面和移动浏览器提供前端Web技术的最新浏览器支持表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Easings",
              "url": "https://easings.net/en",
              "description": "帮助您为动画选择正确的缓动函数。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "WebCode.tools",
              "url": "https://webcode.tools/",
              "description": "协助前端Web项目的代码生成器。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "MarkSheet",
              "url": "https://marksheet.io/",
              "description": "提供免费的HTML和CSS教程。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Xul.fr",
              "url": "https://www.xul.fr/en/",
              "description": "CSS、HTML和JavaScript的教程和索引。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Emmet Cheat Sheet",
              "url": "https://docs.emmet.io/cheat-sheet/",
              "description": "Emmet速查表，Emmet是Web开发人员更快编写HTML和CSS代码的工具包。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "HTML速查表",
          "originalName": "HTML Cheat Sheet",
          "id": "html-cheat-sheet",
          "websites": [
            {
              "name": "HTML Cheat Sheet",
              "url": "https://htmlcheatsheet.com",
              "description": "涵盖各种元素和属性的综合HTML速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HTML5 Doctor Element Index",
              "url": "https://html5doctor.com/element-index/",
              "description": "HTML5中新增或重新定义元素的快速参考。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HTML5 Canvas Cheat Sheet",
              "url": "https://simon.html5.org/dump/html5-canvas-cheat-sheet.html",
              "description": "HTML5 Canvas速查表，提供其属性和方法的快速参考。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HTML Vocabulary",
              "url": "https://apps.workflower.fi/vocabs/html/en#children",
              "description": "提供HTML词汇表的资源，对元素及其关系进行分类。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HTML Reference",
              "url": "https://htmlreference.io/",
              "description": "免费HTML指南，包含所有HTML元素和属性的详细解释。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "CSS速查表",
          "originalName": "CSS Cheat Sheet",
          "id": "css-cheat-sheet",
          "websites": [
            {
              "name": "CSS Reference",
              "url": "https://cssreference.io",
              "description": "CSS属性和选择器的综合参考指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Grid Malven",
              "url": "https://grid.malven.co",
              "description": "CSS Grid布局的交互式指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Flexbox Malven",
              "url": "https://flexbox.malven.co/",
              "description": "CSS Flexbox布局的交互式指南。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Justin Aguilar Animations",
              "url": "https://www.justinaguilar.com/animations/",
              "description": "带实时预览的CSS动画集合。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "CSS Grid Cheat Sheet",
              "url": "https://alialaa.github.io/css-grid-cheat-sheet/",
              "description": "CSS Grid布局的可视化速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Adam Marsden CSS Cheat Sheet",
              "url": "https://adam-marsden.co.uk/css-cheat-sheet",
              "description": "带简洁解释和示例的CSS速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Responsive Web Design Cheat Sheet",
              "url": "https://uxpin.s3.amazonaws.com/responsive_web_design_cheatsheet.pdf",
              "description": "响应式Web设计原则的PDF速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Media Queries Cheat Sheet",
              "url": "https://mac-blog.org.ua/css-3-media-queries-cheat-sheet",
              "description": "CSS3媒体查询的速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bootsnipp",
              "url": "https://bootsnipp.com/",
              "description": "为Bootstrap HTML/CSS/JS框架提供设计元素、游乐场和代码片段的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Bootstrap Cheatsheet",
              "url": "https://hackerthemes.com/bootstrap-cheatsheet/",
              "description": "Bootstrap框架的可视化速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "HackerThemes",
              "url": "https://hackerthemes.com/",
              "description": "基于Bootstrap框架的UI/UX资源。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "DevHints",
          "url": "https://devhints.io",
          "description": "各种编程语言和工具的速查表集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Cheatography",
          "url": "https://cheatography.com/",
          "description": "提供用户生成的各种主题速查表的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Sound of Sorting Algorithm Cheat Sheet",
          "url": "https://panthema.net/2013/sound-of-sorting/SoS-CheatSheet.pdf",
          "description": "与\"排序算法之声\"可视化项目相关的速查表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "SANS Cheat Sheets",
          "url": "https://www.sans.org/blog/the-ultimate-list-of-sans-cheat-sheets/",
          "description": "涵盖一般IT安全主题的SANS速查表终极列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Codecademy Cheatsheets",
          "url": "https://www.codecademy.com/resources/cheatsheets/all",
          "description": "各种编程语言和概念的速查表集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Awesome Cheatsheets",
          "url": "https://lecoupa.github.io/awesome-cheatsheets/",
          "description": "涵盖广泛编程语言、工具和主题的精选优秀速查表列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "OverAPI",
          "url": "https://overapi.com/",
          "description": "为各种编程语言和框架提供集中访问速查表和快速参考的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Nota Language",
          "url": "https://nota-lang.org/",
          "description": "为浏览器设计的文档语言，提供创建基于Web文档的简化语法。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Cheat-Sheets.org",
          "url": "https://www.cheat-sheets.org/",
          "description": "涵盖各种主题的速查表、汇总、快速参考卡、指南和表格的编译。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "电脑组装",
      "originalName": "Building Computer / PC Build",
      "id": "building-computer---pc-build",
      "subcategories": [
        {
          "name": "键盘",
          "originalName": "Keyboard",
          "id": "keyboard",
          "websites": [
            {
              "name": "MechanicalKeyboards",
              "url": "https://mechanicalkeyboards.com/index.php",
              "description": "全球最大的专用机械键盘目录，提供快速配送和售后支持。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Keycaps.info",
              "url": "https://www.keycaps.info/",
              "description": "键帽爱好者的资源，提供各种键帽轮廓、设计和机械键盘兼容性信息。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Keybr",
              "url": "https://www.keybr.com/",
              "description": "提供可定制课程来改善触摸打字技能的平台网站。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "PairType",
              "url": "https://www.pairtype.com/",
              "description": "与伙伴实时练习触摸打字的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "KeyCombiner",
              "url": "https://keycombiner.com/",
              "description": "学习和练习键盘快捷键和按键组合的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Yip-Yip",
              "url": "https://www.yip-yip.com/",
              "description": "提供各种应用程序和程序键盘快捷键的在线工具。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Keebmaker",
              "url": "https://keebmaker.com/",
              "description": "创建定制机械键盘的资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Colemak - Learn",
              "url": "https://colemak.com/Learn",
              "description": "Colemak键盘布局的学习资源。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "打字练习",
          "originalName": "Typing Practice",
          "id": "typing-practice",
          "websites": [
            {
              "name": "TypeFast",
              "url": "https://typefast.io/",
              "description": "旨在通过引人入胜的挑战和练习帮助用户提高打字速度和准确性的打字练习平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "10FastFingers",
              "url": "https://10fastfingers.com/typing-test/english",
              "description": "测量英语打字速度和准确性的打字测试平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "TypingClub",
              "url": "https://www.typingclub.com/",
              "description": "提供交互式打字课程和游戏以提高打字技能的平台。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Typing.com",
              "url": "https://www.typing.com/",
              "description": "为所有级别学习者提供打字课程、游戏和测试的在线资源。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        },
        {
          "name": "键盘快捷键",
          "originalName": "Keyboard Shortcuts",
          "id": "keyboard-shortcuts",
          "websites": [
            {
              "name": "UseTheKeyboard",
              "url": "https://usethekeyboard.com/",
              "description": "Mac应用程序、Windows程序和网站的键盘快捷键集合，涵盖广泛的常用应用程序。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ASCII Tables",
              "url": "https://ascii-tables.com/",
              "description": "提供ASCII表、ALT代码、Z分数表、希腊字母表、T分布表和二进制翻译器的在线资源。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "ShortcutFoo",
              "url": "https://www.shortcutfoo.com/",
              "description": "免费练习键盘快捷键的平台，支持Mac、Windows、Linux，并为各种应用程序、语言和终端提供速查表。",
              "isPaid": false,
              "isStudentFriendly": false
            },
            {
              "name": "Microsoft Word Keyboard Shortcuts",
              "url": "https://support.microsoft.com/en-us/office/keyboard-shortcuts-in-word-************************************",
              "description": "提供Microsoft Word键盘快捷键综合列表的官方Microsoft Office支持页面。",
              "isPaid": false,
              "isStudentFriendly": false
            }
          ]
        }
      ],
      "websites": [
        {
          "name": "PCPartPicker",
          "url": "https://pcpartpicker.com/list/",
          "description": "用于规划和构建定制PC的工具，让用户创建配件列表、比较价格并确保组件之间的兼容性。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "PCBuilder",
          "url": "https://pcbuilder.net/list/",
          "description": "设计定制PC构建的平台，为用户提供组件兼容性检查、价格比较和配置选项。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "PC Builds",
          "url": "https://pc-builds.com/",
          "description": "定制PC构建资源，提供兼容组件的精选列表、指南和各种性能级别及预算的建议。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "LinearMouse",
          "url": "https://linearmouse.app/",
          "description": "为Mac提供高级鼠标和触控板自定义选项的实用程序，允许用户微调手势、按钮映射和滚动行为。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    },
    {
      "name": "网站导航",
      "originalName": "Other Websites of Websites",
      "id": "other-websites-of-websites",
      "subcategories": [],
      "websites": [
        {
          "name": "Stumbled.to",
          "url": "https://stumbled.to/",
          "description": "发现和分享有趣网站和内容的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "5000Best",
          "url": "https://5000best.com/websites/",
          "description": "各种类别中5000个最佳网站的精选集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Neal.fun",
          "url": "https://neal.fun/",
          "description": "具有娱乐性和交互性项目的网站。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tennessine",
          "url": "https://tennessine.co.uk/",
          "description": "网络政治、数学和有趣项目的集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Blakeir",
          "url": "https://blakeir.com/blog/smart-youtube",
          "description": "列出一些具有智慧和洞察力内容的YouTube页面的博客文章。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "AwesomeOSINT on GitHub",
          "url": "https://awesomeopensource.com/project/jivoi/awesome-osint#-pastebins",
          "description": "包含开源情报工具和资源精选列表的GitHub存储库。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "MadeWithBubble",
          "url": "https://www.madewithbubble.xyz/",
          "description": "发现使用Bubble开发平台创建的应用程序和网站的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "NoviceDock",
          "url": "https://novicedock.com/",
          "description": "提供学习编程和相关主题精选资源的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Hackr.io",
          "url": "https://hackr.io/",
          "description": "查找编程社区推荐的编程课程和教程的平台。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Limnology",
          "url": "https://limnology.co/en",
          "description": "20种不同语言的教育YouTube频道策划，按主题分类，可选择查找类似频道。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Tech Blogs",
          "url": "https://tech-blogs.dev/",
          "description": "优秀技术博客列表。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Track Awesome List",
          "url": "https://www.trackawesomelist.com/",
          "description": "跟踪超过500个优秀列表更新，您还可以通过RSS或新闻通讯订阅每日或每周更新。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Internet Is Fun",
          "url": "https://projects.kwon.nyc/internet-is-fun/",
          "description": "互联网上有趣和有意思网站的集合。",
          "isPaid": false,
          "isStudentFriendly": false
        },
        {
          "name": "Wiby",
          "url": "https://wiby.me/",
          "description": "经典网络搜索引擎，旨在重现互联网早期的浏览体验，特别适合老式计算机。",
          "isPaid": false,
          "isStudentFriendly": false
        }
      ]
    }
  ]
};