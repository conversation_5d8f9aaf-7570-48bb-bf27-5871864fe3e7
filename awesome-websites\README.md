# Awesome Useful Websites

一个基于React的现代化网站，展示来自AD.md的精选有用网站集合。

## 功能特点

- 🌟 **精选网站**: 展示各种分类的有用网站
- 🔍 **智能搜索**: 支持按网站名称和描述搜索
- 📱 **响应式设计**: 适配桌面和移动设备
- 🎨 **现代UI**: 美观的渐变背景和卡片设计
- 🏷️ **分类浏览**: 按主要分类和子分类组织
- 🔗 **直接访问**: 一键跳转到目标网站
- 💰 **标记系统**: 显示付费($)和学生友好(@)标记

## 技术栈

- **前端框架**: React 18
- **构建工具**: Vite
- **样式**: CSS3 (原生CSS，无框架依赖)
- **图标**: 内联SVG
- **字体**: Inter字体系列

## 项目结构

```
src/
├── components/          # React组件
│   ├── Header.jsx      # 页面头部
│   ├── SearchBar.jsx   # 搜索栏
│   ├── CategoryList.jsx # 分类列表
│   ├── WebsiteGrid.jsx # 网站网格
│   ├── WebsiteCard.jsx # 网站卡片
│   └── *.css          # 对应的样式文件
├── data/
│   └── websitesData.js # 网站数据
├── App.jsx            # 主应用组件
├── main.jsx          # 应用入口
└── index.css         # 全局样式
```

## 快速开始

1. 安装依赖:
```bash
npm install
```

2. 启动开发服务器:
```bash
npm run dev
```

3. 打开浏览器访问: http://localhost:5173

## 构建部署

```bash
npm run build
```

构建文件将生成在 `dist/` 目录中。

## 数据来源

网站数据来源于AD.md文件，包含了各种有用的在线工具和资源。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
