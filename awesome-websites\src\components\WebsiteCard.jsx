import './WebsiteCard.css'

function WebsiteCard({ website, searchTerm, animationDelay = 0 }) {
  // 高亮搜索词
  const highlightText = (text, searchTerm) => {
    if (!searchTerm) return text
    
    const regex = new RegExp(`(${searchTerm})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="highlight">{part}</mark>
      ) : part
    )
  }

  // 获取网站图标
  const getFavicon = (url) => {
    try {
      const domain = new URL(url).hostname
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`
    } catch {
      return null
    }
  }

  // 获取网站域名
  const getDomain = (url) => {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return url
    }
  }

  return (
    <div
      className="website-card"
      style={{ animationDelay: `${animationDelay}s` }}
    >
      <div className="website-header">
        <div className="website-info">
          <img 
            src={getFavicon(website.url)} 
            alt=""
            className="website-favicon"
            onError={(e) => e.target.style.display = 'none'}
          />
          <h4 className="website-name">
            {highlightText(website.name, searchTerm)}
          </h4>
        </div>
        <div className="website-badges">
          {website.isPaid && (
            <span className="badge paid" title="付费服务">$</span>
          )}
          {website.isStudentFriendly && (
            <span className="badge student" title="学生友好">@</span>
          )}
        </div>
      </div>
      
      <p className="website-description">
        {highlightText(website.description, searchTerm)}
      </p>
      
      <div className="website-footer">
        <div className="website-domain">
          {getDomain(website.url)}
        </div>
        <a
          href={website.url}
          target="_blank"
          rel="noopener noreferrer"
          className="website-link"
        >
          访问网站
          <svg className="external-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
            <polyline points="15,3 21,3 21,9"></polyline>
            <line x1="10" y1="14" x2="21" y2="3"></line>
          </svg>
        </a>
      </div>
    </div>
  )
}

export default WebsiteCard
