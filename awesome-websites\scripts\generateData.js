import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 解析函数
function parseMarkdownToJson(markdownContent) {
  const lines = markdownContent.split('\n');
  const result = {
    title: "Awesome Useful Websites",
    description: "探索互联网上的隐藏宝石，发现各种有用的在线工具和资源！",
    categories: []
  };

  let currentCategory = null;
  let currentSubCategory = null;
  let inContentSection = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 跳过前面的介绍部分，直到找到第一个主要分类
    if (line.startsWith('## Tools')) {
      inContentSection = true;
    }
    
    if (!inContentSection) continue;

    // 主要分类 (## 开头)
    if (line.startsWith('## ') && !line.includes('Contents')) {
      const categoryName = line.replace('## ', '').trim();
      const chineseName = getCategoryChineseName(categoryName);
      currentCategory = {
        name: chineseName,
        originalName: categoryName,
        id: categoryName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
        subcategories: [],
        websites: []
      };
      result.categories.push(currentCategory);
      currentSubCategory = null;
    }
    
    // 子分类 (### 开头)
    else if (line.startsWith('### ')) {
      const subCategoryName = line.replace('### ', '').trim();
      const chineseName = getSubCategoryChineseName(subCategoryName);
      currentSubCategory = {
        name: chineseName,
        originalName: subCategoryName,
        id: subCategoryName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
        websites: []
      };
      if (currentCategory) {
        currentCategory.subcategories.push(currentSubCategory);
      }
    }
    
    // 四级分类 (#### 开头)
    else if (line.startsWith('#### ')) {
      const subCategoryName = line.replace('#### ', '').trim();
      const chineseName = getSubCategoryChineseName(subCategoryName);
      currentSubCategory = {
        name: chineseName,
        originalName: subCategoryName,
        id: subCategoryName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
        websites: []
      };
      if (currentCategory) {
        currentCategory.subcategories.push(currentSubCategory);
      }
    }
    
    // 网站条目 (- 开头，包含链接)
    else if (line.startsWith('- [') && line.includes('](')) {
      const website = parseWebsiteEntry(line);
      if (website) {
        if (currentSubCategory) {
          currentSubCategory.websites.push(website);
        } else if (currentCategory) {
          currentCategory.websites.push(website);
        }
      }
    }
  }

  return result;
}

function parseWebsiteEntry(line) {
  // 匹配格式: - [网站名](链接) - 描述
  const match = line.match(/^- \[([^\]]+)\]\(([^)]+)\)(?:\s*\(\$\)|\s*\(@\))?\s*-?\s*(.*)$/);
  
  if (!match) return null;

  const [, name, url, description] = match;
  
  // 检查特殊标记
  const isPaid = line.includes('($)');
  const isStudentFriendly = line.includes('(@)');
  
  return {
    name: name.trim(),
    url: url.trim(),
    description: description.trim(),
    isPaid,
    isStudentFriendly
  };
}

// 分类中文名称映射
function getCategoryChineseName(englishName) {
  const categoryMap = {
    'Tools': '工具',
    'DIY': '动手制作',
    'Culture': '文化',
    'Language': '语言学习',
    'Travel': '旅行',
    'Health': '健康',
    'Music / Audio': '音乐音频',
    'Movies and Series': '影视娱乐',
    'Media': '媒体',
    'Economy': '经济',
    'Business': '商业',
    'Jobs': '工作求职',
    'Startups': '创业',
    'Art': '艺术',
    'Academia': '学术',
    'Science': '科学',
    'Physics': '物理',
    'Mathematics': '数学',
    'Engineering': '工程',
    'Computer Science': '计算机科学',
    'AI/ML': '人工智能',
    'Web Development': 'Web开发',
    'Software Engineering': '软件工程',
    'Privacy': '隐私安全',
    'Softwares': '软件工具',
    'Programming Languages': '编程语言',
    'Coding Practice / Competitive Programming': '编程练习',
    'Cheat Sheets': '速查表',
    'Building Computer / PC Build': '电脑组装',
    'Other Websites of Websites': '网站导航'
  };
  
  return categoryMap[englishName] || englishName;
}

// 子分类中文名称映射
function getSubCategoryChineseName(englishName) {
  const subCategoryMap = {
    'White Board': '白板工具',
    'Mind Map / Note Taking': '思维导图笔记',
    'Diagrams': '图表制作',
    'Texts': '文本处理',
    'Automating browser': '浏览器自动化',
    'Comparison': '比较工具',
    'File': '文件管理',
    'Converter / Conversion': '格式转换',
    'Unit Conversion': '单位转换',
    'Visual': '视觉工具',
    'Grammar': '语法',
    'Words & Meanings': '词汇释义',
    'Globetrotting': '环球旅行',
    'Time': '时间工具',
    'Flight': '航班',
    'Weather': '天气',
    'Air Quality': '空气质量',
    'Food': '美食',
    'Lawn/Yard care': '庭院护理',
    'Find Music': '音乐发现',
    'Free Music': '免费音乐',
    'Mix Sounds': '混音',
    'Music Theory': '音乐理论',
    'Rhyme': '押韵',
    'Spotify': 'Spotify工具',
    'Anime': '动漫',
    'X / Twitter': 'X/Twitter工具',
    'Reddit': 'Reddit工具',
    'Finance': '金融',
    'Patents': '专利',
    'Marketing': '营销',
    'Social Media': '社交媒体',
    'Trends': '趋势分析',
    'Meetings': '会议工具',
    'Remote Jobs': '远程工作',
    'Freelancing': '自由职业',
    'Portfolio / CV / Resume': '简历作品集',
    'Careers': '职业发展',
    'Failures': '失败案例',
    'Finding Ideas': '创意发现',
    'Connectivity': '连接工具',
    'Design': '设计',
    'Colors': '颜色工具',
    'Fonts': '字体',
    'Icons / Icon Packs': '图标',
    'Stock Images': '素材图片',
    'Wallpapers': '壁纸',
    'Photography': '摄影',
    'Art Communities': '艺术社区',
    'Studying': '学习',
    'Calculators': '计算器',
    'MOOC (Massive Open Online Courses)': '在线课程',
    'Biographies': '传记',
    'Books, Articles, Texts': '书籍文章',
    'Book Recommendations and Summaries': '书籍推荐',
    'Maps and Data': '地图数据',
    'Arxiv': 'Arxiv',
    'Infographic': '信息图表',
    'Philosophy': '哲学',
    'Social Sciences': '社会科学',
    'History': '历史',
    'Geoscience': '地球科学',
    'Biology': '生物学',
    'Quantum': '量子物理',
    'Quantum Games': '量子游戏',
    'Astronomy': '天文学',
    'Math + Art': '数学艺术',
    'Civil Engineering': '土木工程',
    'Mechanical Engineering': '机械工程',
    'Materials / Nanotechnology': '材料纳米技术',
    'Electronics Engineering': '电子工程',
    'Data Structures and Algorithms (DS&A)': '数据结构算法',
    'Big-O notation': '大O记号',
    'Robotics': '机器人',
    'LLMs': '大语言模型',
    'Prompt Engineering': '提示工程',
    'AI tools': 'AI工具',
    'Data Science': '数据科学',
    'Databases': '数据库',
    'Front-end': '前端开发',
    'HTML': 'HTML',
    'CSS': 'CSS',
    'JavaScript': 'JavaScript',
    'Back-End': '后端开发',
    'APIs': 'API',
    'SQL': 'SQL',
    'Web Analytics': '网站分析',
    'Testing': '测试',
    'Web 3.0 Dev and Cryptocurrencies': 'Web3加密货币',
    'Android Development': 'Android开发',
    'Game Development': '游戏开发',
    'Game Theory': '游戏理论',
    'Pokemon': '宝可梦',
    'Chess': '国际象棋',
    'Embeddings': '嵌入',
    'Linux': 'Linux',
    'Vim': 'Vim',
    'Git': 'Git',
    'GitHub': 'GitHub',
    'IDEs': '集成开发环境',
    'Cryptography': '密码学',
    'GAFA Alternatives': 'GAFA替代品',
    'Ad Blocker': '广告拦截',
    'Emails': '邮箱',
    'Disposable Email': '临时邮箱',
    'Data Breach': '数据泄露',
    'Search': '搜索',
    'Internet': '互联网',
    'DNS': 'DNS',
    'URL': 'URL工具',
    'URL Shortener': '短链接',
    'VPN': 'VPN',
    'Fake Information Generation': '虚假信息生成',
    'Password Generation': '密码生成',
    'Snippets': '代码片段',
    'Linters': '代码检查',
    'Regex': '正则表达式',
    'No-Code': '无代码',
    'Licensing': '许可证',
    'Haskell': 'Haskell',
    'Python': 'Python',
    'C++': 'C++',
    'Capture the Flag': 'CTF',
    'Projects': '项目',
    'Open Source': '开源',
    'Hackathons': '黑客马拉松',
    'Python Cheat Sheet': 'Python速查表',
    'Front-end Cheat Sheet': '前端速查表',
    'HTML Cheat Sheet': 'HTML速查表',
    'CSS Cheat Sheet': 'CSS速查表',
    'Keyboard': '键盘',
    'Typing Practice': '打字练习',
    'Keyboard Shortcuts': '键盘快捷键'
  };
  
  return subCategoryMap[englishName] || englishName;
}

// 主函数
async function main() {
  try {
    // 读取AD.md文件
    const adPath = path.join(__dirname, '..', '..', 'ad.md');
    const markdownContent = fs.readFileSync(adPath, 'utf-8');
    
    console.log('开始解析AD.md文件...');
    const jsonData = parseMarkdownToJson(markdownContent);
    
    // 计算统计信息
    let totalWebsites = 0;
    let totalSubcategories = 0;
    
    jsonData.categories.forEach(category => {
      totalWebsites += category.websites.length;
      totalSubcategories += category.subcategories.length;
      category.subcategories.forEach(sub => {
        totalWebsites += sub.websites.length;
      });
    });
    
    console.log(`解析完成！`);
    console.log(`- 主要分类: ${jsonData.categories.length}`);
    console.log(`- 子分类: ${totalSubcategories}`);
    console.log(`- 网站总数: ${totalWebsites}`);
    
    // 生成数据文件
    const outputPath = path.join(__dirname, '..', 'src', 'data', 'fullWebsitesData.js');
    const jsContent = `// 从AD.md自动生成的完整网站数据
// 生成时间: ${new Date().toISOString()}
// 总计: ${totalWebsites}个网站，${jsonData.categories.length}个主要分类，${totalSubcategories}个子分类

export const websitesData = ${JSON.stringify(jsonData, null, 2)};`;
    
    fs.writeFileSync(outputPath, jsContent);
    console.log(`数据文件已生成: ${outputPath}`);
    
  } catch (error) {
    console.error('生成数据时出错:', error);
  }
}

main();
