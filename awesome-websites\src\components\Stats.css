.stats {
  margin: 20px 0;
}

.stats-container {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 20px 25px;
  text-align: center;
  color: white;
  min-width: 120px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.stat-item.search-result {
  background: rgba(255, 235, 59, 0.2);
  border-color: rgba(255, 235, 59, 0.4);
}

.stat-item.search-result .stat-number {
  color: #fff176;
}

@media (max-width: 768px) {
  .stats-container {
    gap: 15px;
  }
  
  .stat-item {
    padding: 15px 20px;
    min-width: 100px;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
}
