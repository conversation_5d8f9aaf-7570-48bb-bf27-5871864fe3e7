# Awesome Useful Websites

[![Awesome](https://cdn.rawgit.com/sindresorhus/awesome/d7305f38d29fed78fa85652e3a63e154dd8e8829/media/badge.svg)](https://github.com/sindresorhus/awesome)

<br>

Explore the internet's hidden gems with this list of awesome useful websites!

<br>

Most of these websites are gathered from:

- [producthunt.com](https://www.producthunt.com/)
- [reddit.com/r/InternetIsBeautiful](https://www.reddit.com/r/InternetIsBeautiful/)
- [news.ycombinator.com](https://news.ycombinator.com/)
- [x.com/IndieRandWeb](https://x.com/IndieRandWeb)

<br>

Feel free to share this list, star it, and [contribute](#contributing) your own awesome finds.

<br>

## Nomenclature

Certain websites are tagged with the symbols listed below for convenience.

| Symbol | Meaning                                                            |
| :----: | ------------------------------------------------------------------ |
|   $    | Payment Required (There are no free options)                       |
|   @    | Student Friendly (Offers discounts or a free version for students) |

<br>

Each website is included only once. Some websites can fall into multiple categories. If you can't find what you need in a specific category, search for keywords or explore other relevant categories as well. To look up websites, use the [raw](https://raw.githubusercontent.com/atakanaltok/awesome-useful-websites/refs/heads/main/README.md) version.

# Contents

- [Awesome Useful Websites](#awesome-useful-websites)
  - [Nomenclature](#nomenclature)
- [Contents](#contents)
  - [Tools](#tools)
    - [White Board](#white-board)
    - [Mind Map / Note Taking](#mind-map--note-taking)
    - [Diagrams](#diagrams)
    - [Texts](#texts)
    - [Automating browser](#automating-browser)
    - [Comparison](#comparison)
    - [File](#file)
    - [Converter / Conversion](#converter--conversion)
      - [Unit Conversion](#unit-conversion)
    - [Visual](#visual)
  - [DIY](#diy)
  - [Culture](#culture)
  - [Language](#language)
    - [Grammar](#grammar)
    - [Words \& Meanings](#words--meanings)
  - [Travel](#travel)
    - [Globetrotting](#globetrotting)
    - [Time](#time)
    - [Flight](#flight)
    - [Weather](#weather)
  - [Health](#health)
    - [Air Quality](#air-quality)
    - [Food](#food)
    - [Lawn/Yard care](#lawnyard-care)
  - [Music / Audio](#music--audio)
    - [Find Music](#find-music)
    - [Free Music](#free-music)
    - [Mix Sounds](#mix-sounds)
    - [Music Theory](#music-theory)
      - [Rhyme](#rhyme)
    - [Spotify](#spotify)
  - [Movies and Series](#movies-and-series)
    - [Anime](#anime)
  - [Media](#media)
    - [X / Twitter](#x--twitter)
    - [Reddit](#reddit)
  - [Economy](#economy)
  - [Business](#business)
    - [Finance](#finance)
    - [Patents](#patents)
    - [Marketing](#marketing)
      - [Social Media](#social-media)
    - [Trends](#trends)
    - [Meetings](#meetings)
  - [Jobs](#jobs)
    - [Remote Jobs](#remote-jobs)
    - [Freelancing](#freelancing)
    - [Portfolio / CV / Resume](#portfolio--cv--resume)
    - [Careers](#careers)
  - [Startups](#startups)
    - [Failures](#failures)
    - [Finding Ideas](#finding-ideas)
    - [Connectivity](#connectivity)
    - [Design](#design)
      - [Colors](#colors)
      - [Fonts](#fonts)
      - [Icons / Icon Packs](#icons--icon-packs)
      - [Stock Images](#stock-images)
      - [Wallpapers](#wallpapers)
  - [Art](#art)
    - [Photography](#photography)
    - [Art Communities](#art-communities)
  - [Academia](#academia)
    - [Studying](#studying)
    - [Calculators](#calculators)
    - [MOOC (Massive Open Online Courses)](#mooc-massive-open-online-courses)
  - [Science](#science)
    - [Biographies](#biographies)
    - [Books, Articles, Texts](#books-articles-texts)
      - [Book Recommendations and Summaries](#book-recommendations-and-summaries)
    - [Maps and Data](#maps-and-data)
      - [Arxiv](#arxiv)
    - [Infographic](#infographic)
    - [Philosophy](#philosophy)
    - [Social Sciences](#social-sciences)
    - [History](#history)
    - [Geoscience](#geoscience)
    - [Biology](#biology)
  - [Physics](#physics)
    - [Quantum](#quantum)
      - [Quantum Games](#quantum-games)
    - [Astronomy](#astronomy)
  - [Mathematics](#mathematics)
    - [Math + Art](#math--art)
  - [Engineering](#engineering)
    - [Civil Engineering](#civil-engineering)
    - [Mechanical Engineering](#mechanical-engineering)
      - [Materials / Nanotechnology](#materials--nanotechnology)
    - [Electronics Engineering](#electronics-engineering)
  - [Computer Science](#computer-science)
    - [Data Structures and Algorithms (DS\&A)](#data-structures-and-algorithms-dsa)
    - [Big-O notation](#big-o-notation)
  - [AI/ML](#aiml)
    - [Robotics](#robotics)
    - [LLMs](#llms)
      - [Prompt Engineering](#prompt-engineering)
    - [AI tools](#ai-tools)
    - [Data Science](#data-science)
    - [Databases](#databases)
  - [Web Development](#web-development)
    - [Front-end](#front-end)
      - [HTML](#html)
      - [CSS](#css)
      - [JavaScript](#javascript)
    - [Back-End](#back-end)
      - [APIs](#apis)
      - [SQL](#sql)
    - [Web Analytics](#web-analytics)
      - [Testing](#testing)
    - [Web 3.0 Dev and Cryptocurrencies](#web-30-dev-and-cryptocurrencies)
  - [Software Engineering](#software-engineering)
    - [Android Development](#android-development)
    - [Game Development](#game-development)
      - [Game Theory](#game-theory)
      - [Pokemon](#pokemon)
      - [Chess](#chess)
    - [Embeddings](#embeddings)
    - [Linux](#linux)
    - [Vim](#vim)
    - [Git](#git)
    - [GitHub](#github)
    - [IDEs](#ides)
  - [Privacy](#privacy)
    - [Cryptography](#cryptography)
    - [GAFA Alternatives](#gafa-alternatives)
    - [Ad Blocker](#ad-blocker)
    - [Emails](#emails)
      - [Disposable Email](#disposable-email)
    - [Data Breach](#data-breach)
    - [Search](#search)
    - [Internet](#internet)
      - [DNS](#dns)
    - [URL](#url)
      - [URL Shortener](#url-shortener)
    - [VPN](#vpn)
    - [Fake Information Generation](#fake-information-generation)
    - [Password Generation](#password-generation)
  - [Softwares](#softwares)
    - [Snippets](#snippets)
    - [Linters](#linters)
    - [Testing](#testing-1)
    - [Regex](#regex)
    - [No-Code](#no-code)
      - [Licensing](#licensing)
  - [Programming Languages](#programming-languages)
    - [Haskell](#haskell)
    - [Python](#python)
    - [C++](#c)
  - [Coding Practice / Competitive Programming](#coding-practice--competitive-programming)
    - [Capture the Flag](#capture-the-flag)
    - [Projects](#projects)
    - [Open Source](#open-source)
    - [Hackathons](#hackathons)
  - [Cheat Sheets](#cheat-sheets)
    - [Python Cheat Sheet](#python-cheat-sheet)
    - [Front-end Cheat Sheet](#front-end-cheat-sheet)
      - [HTML Cheat Sheet](#html-cheat-sheet)
      - [CSS Cheat Sheet](#css-cheat-sheet)
  - [Building Computer / PC Build](#building-computer--pc-build)
    - [Keyboard](#keyboard)
      - [Typing Practice](#typing-practice)
      - [Keyboard Shortcuts](#keyboard-shortcuts)
  - [Other Websites of Websites](#other-websites-of-websites)
- [Contributing](#contributing)
- [DISCLAIMER](#disclaimer)
- [LICENSE](#license)

<br>

## Tools

- [5000Best Tools](https://5000best.com/tools/) - 5000个工具。
- [10015.io](https://10015.io/) - 免费的多合一工具箱，用于各种任务。
- [UnTools](https://untools.co/) - 思维工具和框架集合。
- [TimeTravel Memento](https://timetravel.mementoweb.org/) - 在Internet Archive、Archive-It、大英图书馆、archive.today、GitHub等地方查找备忘录。
- [discu.eu](https://discu.eu/) - 通过每周新闻通讯、社交和机器人、浏览器扩展、书签工具跟上您关心的话题。
- [PromoWizard](https://promowizard.softr.app/) - 无需在YouTube上观看数小时内容即可获取促销代码。
- [Everybodywiki](https://en.everybodywiki.com/Everybodywiki:Welcome) - 从维基百科中拯救已删除的文章和被拒绝的草稿，支持多种语言，并欢迎新文章。
- [Lunar](https://lunar.fyi/) - 控制显示器的多功能应用程序。
- [GetHuman](https://gethuman.com/) - 更快地与知名公司的代表通话并获得更好的帮助。
- [S-ings Scratchpad](https://www.s-ings.com/scratchpad/) - 为快速笔记、计算和非正式写作设计的在线草稿本工具。
- [UFreeTools](https://www.ufreetools.com/) - 您的在线免费工具包。

### White Board

- [TypeHere](https://typehere.co/) - 只能输入文字的空白网站。
- [PixelPaper](https://pixelpaper.io/) - 永久免费的数字白板，无需注册，可嵌入SaaS产品。
- [Excalidraw](https://excalidraw.com/) - 可以绘制手绘风格图表的白板。
- [Excalideck](https://excalideck.com/) - 基于Excalidraw创建手绘风格幻灯片的应用程序。
- [Blank Page](https://blank.page/) - 显示空白白页的简单网页。
- [Kid Pix](https://kidpix.app/) - 为儿童设计的位图绘图程序，提供有趣、用户友好的界面用于创意和交互式数字艺术作品。
- [Krita](https://krita.org/en/) - 为艺术家设计的免费开源数字绘画软件，提供插图、概念艺术和纹理绘画的高级工具。

### Mind Map / Note Taking

- [Relanote](https://relanote.com/) - 将您的笔记相互链接，形成思维网络。
- [Bubbl.us](https://bubbl.us/) - 在线思维导图。
- [MindMup](https://www.mindmup.com/) - 免费在线思维导图。
- [Anotepad](https://anotepad.com/) - 在线记事本。无需登录。可将笔记下载为PDF或Word文档。
- [Notes.io](https://notes.io/) - 基于Web的笔记应用程序。

### Diagrams

- [Creately](https://creately.com/) - 用于头脑风暴、规划、执行和获取知识的数据连接可视化工作空间。
- [draw.io](https://www.drawio.com/) - 用于构建图表应用程序的开源、安全优先技术栈。可用于[网页使用](https://app.diagrams.net/?src=about)。
- [OrgPad](https://orgpad.com/?ref=producthunt) - 交互式在线思维导图。
- [Lucidchart](https://www.lucidchart.com/pages/) - 一个图表和流程图应用程序，将团队聚集在一起做出更好的决策并构建未来。
- [Learn Anything](https://learn-anything.xyz/) - 组织世界知识、探索连接并策划学习路径的平台。

### Texts

- [Word Counter](https://freecodetools.org/word-counter/) - 字数统计工具。
- [Text Faces](https://textfac.es/) - 编写Unicode表情符号。
- [Title Case](https://titlecase.com/) - 将文本转换为各种大小写格式。
- [Fancy Text Generator](https://lingojam.com/FancyTextGenerator) - 将文本转换为各种字体样式。
- [Calligraphr](https://www.calligraphr.com/en/) - 将您的手写字体或书法转换为字体文件。
- [Dongerlist](https://dongerlist.com/) - 由Unicode字符组成的文本表情符号集合。
- [ASCII-art Tutorial](https://stonestoryrpg.com/ascii_tutorial.html) - ASCII艺术教程。
- [Emoji Combos](https://emojicombos.com/) - 表情符号组合和序列集合。
- [ASCII World](https://www.asciiworld.com/) - 以ASCII艺术和教程为特色的平台。
- [Rentry](https://rentry.co/FMHY) - 在线协作markdown编辑器。

### Automating browser

- [Automa](https://chrome.google.com/webstore/detail/automa/infppggnoaenmfagbfknfkancpbljcca?ref=producthunt) - 用于自动化浏览器操作的Chrome扩展。
- [Browse.AI](https://www.browse.ai/) - 从任何网站提取、设置点击和监控数据的平台。
- [Tango](https://www.tango.us/) - 用截图创建分步文档、操作手册和产品指南的工具。
- [BookmarkOS](https://bookmarkos.com/bookmark-manager-finder) - 允许您筛选各种书签管理器。

### Comparison

- [Social Media Messaging Apps Comparison](https://docs.google.com/spreadsheets/d/1-UlA4-tslROBDS9IqHalWVztqZo7uxlCeKPQ-8uoFOU/edit#gid=0) - 社交媒体消息应用的详细比较和分析。
- [DxOMark](https://www.dxomark.com/category/smartphone-reviews/) - 对智能手机、传感器、镜头和扬声器进行科学测试和数据分析。
- [TechSpecs Compare Phones](https://techspecs.io/vs/) - 比较手机规格参数。
- [TechSpecs](https://techspecs.io/) - 消费电子产品的搜索引擎。
- [Kimovil](https://www.kimovil.com/en/) - 比较智能手机和平板电脑的规格和价格。
- [DiffChecker](https://www.diffchecker.com/) - 比较文本、图像、PDF等文件以找出差异。
- [CodingFont](https://www.codingfont.com/) - 通过游戏化体验比较和寻找编程字体。
- [This vs That](https://thisvsthat.io/) - 输入两个事物来比较它们。
- [Secure Messaging Apps Comparison](https://www.securemessagingapps.com/) - 安全消息应用的比较平台。
- [RTINGS](https://www.rtings.com/) - 提供音视频设备的深度评测和比较，包括电视、显示器、耳机和音响，含详细测试和评级。

### File

- [Wormhole](https://wormhole.app/) - 通过端到端加密分享文件的平台，链接会自动过期。
- [Keybase](https://keybase.io/) - 端到端加密的安全消息和文件共享平台。
- [MediaFire](https://www.mediafire.com/) - 文件存储和共享平台（提供订阅选项）。
- [Zippyshare](https://www.zippyshare.com/) - 无需注册、无下载限制、免费且无限磁盘空间的文件分享服务。

### Converter / Conversion

- [PDF2DOC](https://pdf2doc.com/) - 免费在线PDF转DOC转换器。
- [Online-Convert](https://www.online-convert.com/) - 在线转换不同格式的媒体文件。
- [JPG to PNG](https://jpg.to-png.com/) - 各种格式的免费文件转换工具。
- [Conversion-Tool](https://www.conversion-tool.com/) - 提供广泛的免费在线转换工具。
- [Zamzar](https://www.zamzar.com/) - 转换1100多种格式的文档、图像、视频和音频。
- [Web2PDFConvert](https://www.web2pdfconvert.com/) - 将网页或HTML转换为PDF或图像格式。
- [SmallPDF](https://smallpdf.com/) - 21种免费PDF转换、压缩和编辑工具。
- [Corrupt-a-File](https://corrupt-a-file.net/) - 在线损坏任何文件（使用风险自负）。
- [CloudConvert](https://cloudconvert.com/) - 支持200多种格式的在线文件转换器。
- [OnlineOCR](https://www.onlineocr.net/) - 带OCR支持的图片转文字转换器。
- [PDF Candy](https://pdfcandy.com/) - 处理PDF文件的在线工具。
- [PDFescape](https://www.pdfescape.com/) - 免费在线PDF编辑器和表单填写工具。
- [PrintIt](https://printit.work/about) - 将网页打印为PDF的服务，提供各种自定义选项。

#### Unit Conversion

- [UnitConverters](https://www.unitconverters.net/) - 转换各种单位的在线平台。
- [OnlineConversion](https://onlineconversion.com/) - 几乎可以将任何东西转换为任何其他东西的工具，包含数千个单位和数百万种转换。
- [Text to Binary Conversion](https://www.online-toolz.com/tools/text-binary-convertor.php) - 文本转二进制的在线工具。
- [CSSUnitConverter](https://cssunitconverter.com/) - 在PX、EM、REM、PT、英寸、厘米等单位之间转换，适用于网页和印刷设计。

### Visual

- [Unscreen](https://www.unscreen.com/) - 自动免费移除视频背景。
- [Remove.bg](https://www.remove.bg/) - 自动免费移除图像背景。
- [Foco Clipping](https://www.fococlipping.com/) - 移除图像背景。
- [Designify](https://www.designify.com/) ($) - 通过自动移除背景、增强颜色、调整智能阴影等功能创建AI驱动的设计。
- [PfpMaker](https://pfpmaker.com/) - 从任何照片制作头像。
- [JPEG-Optimizer](https://jpeg-optimizer.com/) - 免费的在线数字照片和图像调整和压缩工具。
- [Extract.pics](https://extract.pics/) - 使用虚拟浏览器从任何公共网站提取图像。
- [Generated.Photos](https://generated.photos/) - 独特、无忧（AI生成）、免费下载的模特照片。
- [Zoom.it](https://zoom.it/) - 创建高分辨率、可缩放的图像。
- [VectorMagic](https://vectormagic.com/) - 将位图（JPG、PNG、GIF）转换为矢量图（PDF、SVG、EPS）。
- [Screenshot.Guru](https://screenshot.guru/) - 对网站和推文进行高分辨率屏幕截图。
- [Stolen Camera Finder](https://www.stolencamerafinder.com/) - 使用照片中存储的序列号在网上搜索用同一台相机拍摄的其他照片。
- [Ribbet](https://www.ribbet.com/) - 照片编辑工具。
- [Crossfade.io](https://crossfade.io/) - 从您喜爱的网站制作基于网络的视频混剪。
- [GoProHeroes](https://goproheroes.com/) - 网络上的GoPro视频。
- [Synthesia](https://www.synthesia.io/) ($) - 在几分钟内从文本创建视频的AI视频创作平台。
- [ClipDrop](https://clipdrop.co/) - 在AI驱动下几秒钟内创建令人惊叹的视觉效果。
- [Reface](https://hey.reface.ai/) - 创建换脸视频，由AI驱动的移动应用。
- [PhotoSonic](https://photosonic.writesonic.com/) - 用像素描绘您梦想的AI，DALL-E的另一个版本。
- [Shottr](https://shottr.cc/) - 小巧快速的macOS截图工具，具有注释、滚动截图和云上传功能。
- [3D GIF Maker](https://www.3dgifmaker.com/) - 轻松从您的图像创建3D GIF。
- [EZGIF](https://ezgif.com/) - 用于创建和编辑GIF的在线GIF制作器和图像编辑器。
- [PimEyes](https://pimeyes.com/en) - 面部识别搜索引擎和反向图像搜索，用于查找包含特定人员的图像。
- [Visual Illusions](https://sites.socsci.uci.edu/~ddhoff/illusions.html) - 视觉错觉和演示的集合。
- [ByClickDownloader](https://www.byclickdownloader.com/) - 使用他们的软件以HD、MP3、MP4、AVI和其他格式备份各种网站的视频。
- [Reanimate](https://reanimate.github.io/) - 使用SVG和Haskell构建声明式动画。

## DIY

- [WikiHow](https://www.wikihow.com/Main-Page) - 创建和分享操作指南的协作平台。
- [ManualsLib](https://www.manualslib.com/) - 用户手册和指南的在线存储库。
- [This to That](https://thistothat.com/) - 学习如何将不同材料粘合在一起。
- [HowStuffWorks](https://www.howstuffworks.com/) - 通过深入的解释和文章探索事物的工作原理。
- [WonderHowTo](https://www.wonderhowto.com/) - 通过教学视频和分步指南学习几乎任何事情。
- [Dummies](https://www.dummies.com/) - 一系列教学/参考书籍。
- [DoItYourself](https://www.doityourself.com/) - DIY项目和家庭装修的资源。
- [JScreenFix](https://www.jscreenfix.com/) - 用于修复缺陷像素的像素修复算法，对卡住的像素特别有效。无需安装，且免费。
- [Donkey Car](https://www.donkeycar.com/) - 小型汽车的开源DIY自动驾驶平台。它将遥控车与树莓派结合，由Python（tornado、keras、tensorflow、opencv等）驱动。
- [Instructables](https://www.instructables.com/) - 发现和分享DIY项目的平台。
- [iFixit](https://www.ifixit.com/) - 为各种电子产品、家电和其他产品提供免费维修指南和手册，由社区贡献，赋予用户自己修理物品的能力。
- [Fix It Club](https://fixitclub.com/) - 通过有用的指南节省家庭维修费用。
- [BookCrossing](https://bookcrossing.com/) - 将您的书籍"放归野外"供陌生人发现，或对另一个BookCrossing成员进行"受控释放"，并通过来自世界各地的日记条目跟踪它们的旅程。
- [Dimensions](https://www.dimensions.com/) - 为各个类别提供尺寸和测量视觉参考设计的平台。
- [Repair Clinic](https://www.repairclinic.com/) - 北美历史最悠久的正品家电、暖通空调和户外动力设备零件来源，提供专家建议、操作指导资源和DIY维修支持。受到专业人士和房主信赖，该网站提供高质量的OEM零件和指导，帮助用户成功完成维修。

## Culture

- [Cultural Atlas](https://culturalatlas.sbs.com.au/) - 提供文化背景综合信息的教育资源。
- [QuoteMaster](https://www.quotemaster.org/) - 拥有98,683个类别和1,488,431条引语的平台。
- [FactSlides](https://www.factslides.com/) - 提供1001个关于各种主题的事实，并附有来源。
- [Starkey Comics](https://starkeycomics.com/) - 展示关于文化和语言的彩色图像和帖子的网站。
- [Unusual Wikipedia Articles](https://en.wikipedia.org/wiki/Wikipedia:Unusual_articles) - 不寻常维基百科文章的汇编。
- [List of Common Misconceptions](https://en.wikipedia.org/wiki/List_of_common_misconceptions) - 列出常见误解的维基百科页面。
- [Behind the Name](https://www.behindthename.com/) - 提供名字的词源和历史。
- [Behind the Surname](https://surnames.behindthename.com/) - 提供姓氏的词源和历史。
- [Nameberry](https://nameberry.com/) - 专家婴儿取名平台，包括流行名字、独特名字、女孩名字、男孩名字和性别中性名字。
- [Library of Juggling](https://libraryofjuggling.com/) - 将所有流行（以及可能不那么流行）的杂技技巧整理在一个有组织的地方的在线资源。
- [Toaster Central](https://toastercentral.com/) - 工作中的古董烤面包机收藏。
- [All About Berlin](https://allaboutberlin.com/) - 为计划在柏林定居的个人提供指南和信息的平台。包括获得签证、找工作、租房等详细信息。
- [Unita](https://unita.co/) - 发现、比较和评论30多个类别中最佳社区、智囊团和在线群组的平台。
- [Escape Room Tips](https://escaperoomtips.com/) - 密室逃脱的技巧、诀窍和谜题

## Language

- [YouGlish](https://youglish.com/) - 使用YouTube提高您的英语发音。
- [Voscreen](https://www.voscreen.com/) - 提供英语句子视频片段的平台；通过选择释义句子来测试您的理解能力。
- [News in Levels](https://www.newsinlevels.com/) - 为英语学习者量身定制的世界新闻。
- [Pink Trombone](https://dood.al/pinktrombone/) - 使用动画语音盒对人类口腔及其声音的交互式模拟。
- [Japanese Wiki Corpus](https://www.japanese-wiki-corpus.org/) - 从维基百科京都文章的日英双语语料库生成的资源。
- [Latin Phrases](https://latin-phrases.co.uk/) - 查找拉丁短语翻译的参考资料。
- [Prismatext](https://prismatext.com/) - 将最有用的外语单词和短语融入您最喜爱的小说和故事中。
- [Ponly](https://ponly.com/about/) - 包含有趣幽默内容和笑话的网站。
- [Tongue-Twister](https://tongue-twister.net/) - 世界最大的绕口令集合，包含118种语言的3660个条目
- [OLAC (Open Language Archives Community)](http://olac.ldc.upenn.edu/) - 致力于创建语言资源全球虚拟图书馆的国际机构和个人网络，专注于数字档案实践并提供可互操作的语言数据访问存储库。

### Grammar

- [GrammarBook](https://www.grammarbook.com/english_rules.asp) - 英语语法规则的综合指南。
- [The Punctuation Guide](https://www.thepunctuationguide.com/index.html) - 提供美式标点符号规则指南的资源。
- [Progressive Punctuation](https://progressivepunctuation.com/) - 非标准标点符号的集合。
- [Purdue OWL](https://owl.purdue.edu/site_map.html) - 普渡大学在线写作实验室，提供写作、语法和引用格式的资源。
- [Towson University Online Writing Support](https://webapps.towson.edu/ows/index.asp) - 在线写作支持和语法资源。
- [Grammar Monster](https://www.grammar-monster.com/index.html) - 提供免费英语语法课程和测试的平台。
- [Fraze It](https://fraze.it/) - 拥有超过1亿个句子的平台。

### Words & Meanings

- [Educalingo](https://educalingo.com/en/dic-en) - 查找单词的同义词、用法、趋势、统计等信息的平台。
- [Fine Dictionary](https://www.finedictionary.com/) - 关于单词的综合信息。
- [Crown Academy English](https://www.crownacademyenglish.com/articles/) - 提供各种英语语言概念的简单、简洁和清晰解释。
- [Ask Difference](https://www.askdifference.com/) - 提供两个相似主题之间简短而简洁的差异。
- [Key Differences](https://keydifferences.com/) - 专注于呈现差异和比较的网站。
- [DifferenceBetween.info](https://www.differencebetween.info/) - 提供不同概念之间的描述性分析和比较。
- [Wayne State University's List of Words](https://wordwarriors.wayne.edu/list) - 韦恩州立大学策划的值得更广泛使用的单词汇编。
- [All Acronyms](https://www.allacronyms.com/) - 社区驱动的缩略词和简称词典。
- [How to Professionally Say](https://howtoprofessionallysay.akashrajpurohit.com/) - 日常专业交流指南，帮助您以专业语调应对各种情况。
- [Business English Resources](https://www.businessenglishresources.com/) - 用于提高商务英语技能的免费资源集合。
- [Digital Glossary](https://www.digital-glossary.com/) - 为数字环境提供土耳其语、英语和德语术语词汇。
- [Bilim Terimleri](https://terimler.org/) - 为各种术语提供解释和定义的土耳其语平台。

## Travel

- [Airheart - Travel Restrictions](https://airheart.com/travel-restrictions/united-states-vaccinated) - 了解您的旅行限制和要求，包括COVID-19限制。
- [Country Code](https://countrycode.org/) - 打电话到世界任何地方的指南，提供国际拨号的国家代码。
- [Passport Index](https://www.passportindex.org/) - 探索来自世界各地的护照信息，包括排名和详细信息。
- [Countries Been](https://www.countriesbeen.com/) - 跟踪和列出您访问过的国家的移动应用，提供各种功能。
- [Couchsurfing](https://www.couchsurfing.com/) - 与免费分享家园和体验的全球社区联系。
- [Puffin Maps](https://www.puffinmaps.com/) - 无广告的一体化旅行规划器，帮助您组织旅行计划。
- [AllTrails](https://www.alltrails.com/) - 探索包含30万条步道的数据库，附有户外爱好者的评论和照片。
- [Wanderprep](https://www.wanderprep.com/) - 之前提供装备、应用和旅行技巧建议，让旅程更智能。
- [Looria](https://looria.com/) - 查找诚实产品信息的可信平台。
- [Trip Destination App (iOS)](https://apps.apple.com/us/app/id1580599572) - 用于搜索和规划旅行目的地的免费iPhone应用。
- [Freecycle](https://www.freecycle.org/) - 人们在当地社区免费赠送和获取物品的网络。
- [Roadtrippers](https://roadtrippers.com/) - 规划您的路线并使用逐步导航探索公路旅行中的各种景点。
- [Mountain Project](https://www.mountainproject.com/) - 免费的、众包的世界攀岩目的地指南。
- [Welcome to My Garden](https://welcometomygarden.org/) - 为慢旅行者在私人花园中提供免费露营点的非营利网络。
- [Warmshowers](https://www.warmshowers.org/) - 自行车旅行者和在旅途中支持他们的主人的社区。
- [Slowby](https://www.slowby.travel/) - 提供精心策划的慢旅行行程的平台，获得独特的旅行体验。

### Globetrotting

- [Random Street View](https://randomstreetview.com/) - 足不出户探索世界各地的街道。
- [Virtual Vacation](https://virtualvacation.us/) - 在家中舒适地进行环球虚拟旅行。
- [MapCrunch](https://www.mapcrunch.com/) - 通过传送到随机位置，通过Google街景体验世界。

### Time

- [Every Time Zone](https://everytimezone.com/) - 不同国家的可视化时区比较。
- [Time and Date](https://www.timeanddate.com/) - 提供日历、时钟和各种时间相关信息。
- [Time.is](https://time.is/) - 以51种语言显示任何时区的精确官方原子钟时间，覆盖超过700万个地点。

### Flight

- [SeatGuru](https://seatguru.com/) - 探索1,278架飞机的座位图找到您的座位。
- [Flightradar24](https://www.flightradar24.com/43,24.35/7) - 全球航班跟踪服务，提供世界各地数千架飞机的实时信息。
- [Skyscanner](https://www.skyscanner.co.in/) - 航班搜索引擎，允许用户按日期、价格和预算搜索航班。

### Weather

- [Hint.fm Wind Map](https://hint.fm/wind/) - 美国上空风流的精美轨迹。
- [Windy](https://www.windy.com/) - 任何地点的综合天气信息。
- [Zoom Earth](https://zoom.earth/) - 世界的实时可视化，跟踪热带风暴、飓风、恶劣天气、野火等。
- [Earth Nullschool](https://earth.nullschool.net/) - 超级计算机预测的全球天气状况可视化，每三小时更新一次。
- [OpenWeatherMap](https://openweathermap.org/) - 以快速优雅的方式提供天气预报、新闻播报和历史天气数据的平台。
- [Radiosondy](https://radiosondy.info/) - 跟踪气象无线电探空仪，提供当前和过去探空仪飞行的数据库，包括发射点、类型、最后帧、航向、速度、高度和频率等信息。（注：无线电探空仪是通常由气象气球携带进入大气层的电池供电遥测仪器。）

## Health

- [MuscleWiki](https://musclewiki.com/) - 了解您的身体和肌肉的平台。
- [Just a Minute](https://jinay.dev/just-a-minute/) - 在一分钟内测试您的时间感知能力（对时间流逝的感觉）。
- [FutureMe](https://www.futureme.org/) - 给未来的自己写信的平台。
- [Strobe.Cool](https://strobe.cool/) - 使用频闪效果创建视觉刺激。
- [UnTools](https://untools.co/) - 思维工具和框架的集合，帮助解决问题、决策制定和理解系统。
- [Puzzle Loop](https://www.puzzle-loop.com/) - 提供规则简单但解决方案具有挑战性的逻辑谜题的平台。
- [What Should You Do with Your Life?](https://guzey.com/personal/what-should-you-do-with-your-life/) - 提供人生决策方向和建议的文章。
- [InnerBody](https://www.innerbody.com/) - 研究健康产品、服务等的评论和研究。
- [Sleep Calculator](https://sleepcalculator.com/) - 帮助用户根据期望的起床时间确定最佳就寝时间的工具，优化睡眠周期以获得更好的休息和警觉性。
- [SimpleLab](https://gosimplelab.com/) - 使用基于云的平台在美国提供快速、可靠的环境测试，用于高效的采样、测试和数据管理。

### Air Quality

- [Air Quality Index (European Environment Agency)](http://airindex.eea.europa.eu/) - 提供欧洲实时空气质量数据，具有可视化空气污染水平及其对公共健康影响的交互式地图。
- [Berkeley Earth](http://berkeleyearth.org/) - 专注于提供准确和全面的空气质量和气候数据的非营利组织，提供全球环境数据可视化工具。
- [World Air Quality Index](https://waqi.info/) - 提供来自世界各地的实时空气质量信息，提供交互式地图和污染水平及其对健康影响的详细数据。
- [IQAir](https://www.iqair.com/) - 运营世界上最大的免费实时空气质量监测平台，为个人、研究人员和政府提供关键数据，以监测和解决空气污染问题，最终帮助保护全球公共健康。

### Food

- [GoBento](https://www.gobento.com/) - 专注于改善高风险成员健康和福祉的参与平台，特别是那些面临食品不安全问题的成员。
- [MyFridgeFood](https://myfridgefood.com/) - 允许用户勾选他们拥有的食材来寻找可以用这些食材制作的食谱的平台。
- [Just the Recipe](https://www.justtherecipe.com/) - 提供来自任何食谱网站的直接说明，无广告和弹窗的平台。
- [Two Peas and Their Pod](https://www.twopeasandtheirpod.com/) - 提供各种食谱的食谱网站。
- [HelloFresh](https://www.hellofresh.com/) - 餐食套装配送服务，用户可以选择食谱，HelloFresh将食材直接送到他们家门口。

### Lawn/Yard care

- [Healthy Yards](https://healthyyards.org) - 提供环保和可持续草坪护理实践的资源和技巧，重点关注对当地野生动物和环境的益处。
- [Homegrown National Park](https://homegrownnationalpark.org) - 提供关于通过种植本土物种创建野生动物走廊和促进生物多样性的信息。
- [Butterfly Conservation](https://butterfly-conservation.org) - 分享蝴蝶和飞蛾保护的详细信息，包括物种保护和参与机会。
- [Xerces Society](https://xerces.org) - 提供传粉媒介和无脊椎动物保护指导，包括栖息地恢复和可持续实践。
- [Beyond Pesticides](https://beyondpesticides.org) - 提供有机和可持续实践的资源，以减少农药使用并保护公众健康和环境。
- [National Pesticide Information Center](https://npic.orst.edu) - 为消费者和健康专业人士提供基于科学的农药使用信息、安全指南和事实表。

## Music / Audio

- [KHInsider](https://downloads.khinsider.com/) - 提供MP3和无损格式的视频和PC游戏原声音乐下载。
- [Online Tone Generator](https://onlinetonegenerator.com/) - 生成音调，您也可以以WAV格式下载它们。
- [Tello Music Charts](https://music.tello.app/) - 查找世界各国的最新音乐排行榜。
- [Music Lab](https://musiclab.chromeexperiments.com/Experiments) - 通过有趣的动手实验使音乐学习更加容易。
- [Rap4Ever](https://www.rap4ever.org/) - 探索说唱歌曲、歌词、混音带、专辑、艺术家等。
- [Every Noise](https://everynoise.com/) - 算法生成的音乐流派空间散点图的持续尝试。
- [MP3Cut](https://mp3cut.net/) - 在线修剪或切割任何音频文件。
- [MP3Gain](https://mp3gain.flowsoft7.com/) - 增加、减少和标准化MP3音频文件的音量水平（每个文件60M大小限制）。
- [Magic Playlist](https://create.magicplaylist.co/#/?_k=m5jobg) - 输入您最喜欢的歌曲并创建完美的播放列表。
- [Moises AI](https://moises.ai/) - 以任何调性、任何速度与您最喜欢的艺术家一起演奏。
- [Drumeo](https://www.drumeo.com/) - 与世界最好的老师学习打鼓。
- [SoundLove](https://soundlove.se/) - 不寻常的合成算法，为音乐增添随机性和不可预测性。
- [Audionautix](https://audionautix.com/) - 由Jason Shaw作曲和制作的音乐，免费下载和使用（甚至可用于商业目的）。
- [Typatone](https://typatone.com/) - 通过在键盘上打字生成您自己的音乐。
- [Incredibox](https://www.incredibox.com/) - 音乐应用程序，让您在快乐的打击乐手团队帮助下创建自己的音乐。
- [Sampurr](https://www.sampurr.com/) - 从网络上采样音频。
- [Audiocheck](https://www.audiocheck.net/) - 在线测试您的音频设备。
- [Mixlr](https://mixlr.com/) ($) - 在线分享高质量实时音频。使用任何音源广播，邀请人们收听，并实时聊天（仅收听免费）。
- [Learn Choral Music](https://www.learnchoralmusic.co.uk/) - John的MIDI文件集合，包含以声音为重点的知名合唱作品，可免费下载。
- [Bandura Festival](https://bandura.ukrzen.in.ua/en#lvivbandurfest) - 在线班杜拉琴，一种传统的乌克兰乐器。
- [AllMusic](https://www.allmusic.com/) - 提供关于专辑、艺术家、歌曲和乐队的全面深入信息，为音乐爱好者提供宝贵资源。
- [ASMR Microphones](https://asmrmicrophones.com) - 提供各种ASMR麦克风的评测、比较和专家意见，帮助用户选择最合适的设备。

### Find Music

- [Lalal.ai](https://www.lalal.ai/) - 从任何音频中提取人声、伴奏和各种乐器。
- [Audd.io](https://audd.io/) - 从声音或流媒体中识别音乐或正在播放的内容。
- [Music-Map](https://www.music-map.com/) - 根据您的偏好发现相似的音乐。
- [Commercial Tunage](https://www.commercialtunage.com/) - 识别广告中播放的歌曲。

### Free Music

- [Pretzel Rocks](https://www.pretzel.rocks/) - 为Twitch和YouTube提供流媒体安全音乐。
- [Incompetech](https://incompetech.com/) - 免版税音乐集合。
- [Chosic](https://www.chosic.com/) - 免费背景音乐，可用于商业和非商业用途。

### Mix Sounds

- [Hidden Life Radio](https://hiddenliferadio.com/) - 由马萨诸塞州剑桥市树木生物数据生成的音乐直播流。
- [Noisli](https://www.noisli.com/) - 创建和收听背景声音，以提高专注力和生产力，或放松身心。
- [Soundrop](https://naim30.github.io/soundrop/) - 交互式音乐播放器，您可以创建生成音乐的美丽图案。
- [SoundLove](https://www.producthunt.com/posts/soundlove) - 帮助您根据心情发现和创建播放列表的工具。
- [Rainy Mood](https://rainymood.com/) - 享受舒缓的雨声，用于放松、睡眠和学习。
- [I Miss the Office](https://imisstheoffice.eu/) - 办公室噪音生成器，提供现代办公室生活的环境声音，帮助在家工作时重现办公室氛围。

### Music Theory

- [Teoria](https://www.teoria.com/)
- [MusicTheory.net](https://www.musictheory.net/)
- [All About Music Theory](https://www.allaboutmusictheory.com/) - 钢琴键盘、音乐记谱法、大调音阶
- [Studio Guru - Note Frequency Chart](https://studioguru.co/producer-tools/note-frequency-chart/)

#### Rhyme

- [RhymeZone](https://www.rhymezone.com/) - 查找押韵词、同义词、形容词等。
- [Rhymer](https://rhymer.com/) - 免费在线押韵词典。

### Spotify

- [Chosic](https://www.chosic.com/spotify-playlist-analyzer/) - 通过分析您的播放列表发现新音乐。
- [Pudding](https://pudding.cool/2020/12/judge-my-spotify/) - 训练来评估音乐品味的A.I.。
- [Discoverify Music](https://www.discoverifymusic.com/login) - 根据您的品味发现新音乐。
- [Spottr](https://spottr.vercel.app/login) - 查看您的Spotify统计数据。
- [Playlist Mutator](https://playlistmutator.com/) - 在React中变异现有播放列表。
- [TuneMyMusic](https://www.tunemymusic.com/) - 在音乐服务之间传输播放列表。

## Movies and Series

- [Kanopy](https://www.kanopy.com/en/) (@) - 在图书馆或大学支持下免费提供数千部电影的流媒体平台。
- [Movie Map](https://www.movie-map.com/) - 根据您的偏好找到相似的电影。
- [Reddit Movie Suggestions](https://www.reddit.com/r/MovieSuggestions/wiki/faq) - 各种类型的电影推荐列表。
- [A Good Movie to Watch](https://agoodmovietowatch.com/) - 精心挑选的高评分电影和电视剧。
- [Tubi TV](https://tubitv.com/home) - 提供各种电影和电视剧的免费流媒体服务。
- [Tiii.me](https://tiii.me/) - 计算您观看电视剧的总时间。
- [JustWatch](https://www.justwatch.com/) - 查找和流播电影与电视剧的指导平台。
- [Movie Settings Database](https://www.moviesettingsdatabase.com/) - 按场景整理的超过30,000部电影和电视剧的有序集合。
- [Reelgood Netflix Roulette](https://reelgood.com/roulette/netflix) - 随机推荐Netflix上可观看的电影或电视剧。
- [Reelgood](https://reelgood.com/) - 浏览、搜索并观看来自150多个服务的电视剧和电影，包括Netflix、Hulu、HBO、Disney+、Prime Video等。
- [Movie Sounds](https://movie-sounds.org/) - 提供短音频片段和特效的免费电影引言档案。
- [Tunefind](https://www.tunefind.com/) - 发现您喜爱的电视剧和电影中的音乐。
- [IMSDB](https://imsdb.com/) - 网络上最大的电影剧本集合。
- [Physics in Film and TV](https://physicsinfilmandtv.wordpress.com/) - 探索电影和电视中物理概念呈现的博客。
- [RareFilm](https://rarefilm.net/) - 珍稀和老电影平台。
- [I Have No TV](https://ihavenotv.com/) - 观看免费在线纪录片。
- [WCostream](https://m.wcostream.com/) - 免费卡通和动漫系列流播。
- [Watch Documentaries](https://watchdocumentaries.com/) - 提供各种主题纪录片集合的网站。
- [Senses of Cinema](https://www.sensesofcinema.com/) - 最早的在线电影期刊之一，以其与电影研究和行业趋势相关的专业高质量内容而闻名。

### Anime

- [Reddit Top Anime Streaming Sites](https://reddit.com/r/streamingAnime/wiki/topsites/) - Reddit上列出顶级动漫流媒体网站的Wiki页面。
- [Reddit Legal Anime Streams](https://reddit.com/r/anime/wiki/legal_streams/) - 提供Reddit上合法动漫流媒体网站列表的Wiki页面。

## Media

- [Radio Garden](https://radio.garden/) - 探索以绿点形式在Google Earth地图上显示的实时广播电台，只需一键即可收听任何一个电台。
- [Radiooooo](https://radiooooo.com/) - "音乐时光机"，您可以探索不同时代和地点的音乐。
- [Lightyear.fm](https://www.lightyear.fm/) - 展示广播信号以光速从地球传播的距离。
- [TasteDive](https://tastedive.com/) - 基于您的偏好发现音乐、书籍、电影等的平台。
- [PIDGI Wiki](https://www.pidgi.net/wiki/Main_Page) - 社区驱动的视频游戏媒体数据库，包括艺术作品、宣传材料、标志等。
- [Kassellabs](https://kassellabs.io/) - 在著名电影和电视剧片头中创建您自己的文本。
- [USTVGO](https://ustvgo.tv/) - 免费在线直播电视频道。
- [All You Can Read](https://www.allyoucanread.com/) - 互联网上最大的杂志和报纸数据库，收录了来自世界各地约25,000份出版物。
- [LexiCap](https://karpathy.ai/lexicap/) - Lex Fridman播客节目的文字记录。
- [Brett Hall's TokCast Transcripts](https://www.aniketvartak.com/html/hall-index.html) - Brett Hall's TokCast播客的文字记录。
- [Thumbly](https://thumbly.ai/) - 将您的脚本转换为引人注目的缩略图，可以增加您的YouTube观看量。

### X / Twitter

- [TweetDeck](https://tweetdeck.twitter.com/) - Twitter仪表板应用程序，用于Twitter账户的管理、跟踪和组织。
- [Twitter Video Downloader](https://twittervideodownloader.com) - 在线工具，直接将任何Twitter视频下载到您的手机或电脑。
- [ThreadReaderApp](https://threadreaderapp.com/) - 帮助您将Twitter线程保存为PDF的平台，让阅读和分享变得更加容易。
- [Tweepsmap](https://tweepsmap.com/) - AI驱动的Twitter分析和管理工具。
- [Shadowban Checker](https://shadowban.yuzurisa.com/) - 检查用户名是否在Twitter上被影子封禁的工具。
- [Twitter Name Generator](https://twitternamegenerator.com/) - 免费在线工具，为Twitter生成漂亮的昵称。
- [Thread Hunt](https://threadhunt.xyz/) - 发现优质Twitter线程的平台。
- [Small World](https://smallworld.kiwi/signin) - 利用您Twitter个人资料中的位置信息查找附近朋友。
- [Murmel](https://murmel.social/top) - 策划来自Twitter宇宙的最新发人深省的故事。
- [Chirpty](https://chirpty.com/) - 创建您自己的Twitter互动圈。
- [Capture My Tweet](https://capturemytweet.in/) - 免费将您的推文转换为精美图片。
- [Twitter Card Generator](https://freecodetools.org/twitter-card-generator/) - 生成Twitter卡片的工具，免费为推文附加丰富内容。
- [Rattibha](https://rattibha.com/) - 按类别和作者策划Twitter线程，提供时间、语言和排序选项。
- [Musk Messages](https://muskmessages.com/) - 编译和分类Elon Musk在Twitter上直接消息的平台，方便访问他的想法和声明。

### Reddit

- [RedditList](https://redditlist.com/) - 按各种主题分类的子版块综合列表。
- [Redsim](https://anvaka.github.io/redsim/) - 根据您的兴趣查找相似的子版块。
- [Reveddit](https://www.reveddit.com/about/) - 揭示Reddit被删除的内容。您可以按用户名、子版块(r/)、链接或域名搜索。
- [Spacebar Counter - Reddit List](https://www.spacebarcounter.net/reddit-list) - 100多个子版块的集合。
- [Unreadit](https://unreadit.com/) - Reddit驱动的每周新闻通讯，从各种子版块策划内容。
- [What Is This Thing](https://whatisthisthing.vercel.app/) - 汇总来自r/whatisthisthing子版块的帖子和答案。
- [Gummy Search](https://gummysearch.com/) - 在Reddit上探索痛点、内容想法，发现人们急于付费的内容。
- [RedditSearch.io](https://redditsearch.io/) - 高级Reddit搜索引擎，允许您过滤和自定义搜索。
- [Better Reddit Search](https://betterredditsearch.web.app/) - 通过改进的功能和特性增强您的Reddit搜索体验。

## Economy

- [Investopedia](https://www.investopedia.com/) - 为投资者提供金融教育、新闻和研究的资源。
- [Money](https://money.com/) - 个人理财和金融新闻的综合资源。
- [The Balance Money](https://www.thebalancemoney.com/) - 金融教育和资源。
- [CNN Fear and Greed Index](https://edition.cnn.com/markets/fear-and-greed) - 了解当前推动市场的情绪。
- [Where's Willy](https://www.whereswilly.com/) - 致力于在全球范围内追踪加拿大纸币的国际非营利志愿项目。
- [Where's George](https://www.wheresgeorge.com/) - 类似于"Where's Willy"的项目，用于在全球追踪美国纸币。
- [EuroBillTracker](https://en.eurobilltracker.com/) - 致力于在全球范围内追踪欧元纸币的国际非营利志愿项目。
- [TradingView](https://www.tradingview.com/) - 全球超过3000万交易员和投资者使用的平台和社交网络，用于发现全球市场机会。
- [LendingTree](https://www.lendingtree.com/) - 通过寻找贷款而非制造贷款来为您省钱的市场平台。
- [Masterworks](https://www.masterworks.com/) - 投资蓝筹艺术品的专属社区。
- [EquityBee](https://equitybee.com/) - 通过连接全球投资者网络，为初创公司员工提供行使股票期权所需资金。
- [EquityZen](https://equityzen.com/) - 允许您通过EquityZen基金在二级市场投资或出售股份。
- [WTF Happened in 1971](https://wtfhappenedin1971.com/) - 探索和突出1971年发生的各种经济、社会和金融事件的网站。
- [Ergodicity Economics](https://ergodicityeconomics.com/) - 提供遍历性经济学和相关概念见解的网站。

## Business

- [Crunchbase](https://www.crunchbase.com/) - 发现创新公司、初创企业和商业世界关键人物的平台。提供关于公司、投资和行业趋势的综合数据和见解。
- [Business Model Toolbox](https://bmtoolbox.net/) - 学习各种商业概念和模式的资源。
- [Gumroad](https://gumroad.com/) - 创作者直接向客户销售数字产品的电子商务平台。
- [Humble Bundle](https://www.humblebundle.com/) - 销售游戏、电子书、软件和数字内容的平台，致力于支持慈善事业，同时以实惠价格提供优质内容。
- [Google Takeout](https://takeout.google.com/) - 导出您所有Google数据的副本。
- [Honey](https://www.joinhoney.com/) - 在全球超过30,000个网站上自动搜索优惠券的浏览器扩展。
- [BotHelp](https://bothelp.io/widget) - 为网站提供免费聊天按钮小部件。
- [CertificateClaim](https://www.certificateclaim.com/) - 创建和发送各种类型证书的数字服务。
- [Respresso](https://respresso.io/) ($) - 管理应用本地化文本、图像、颜色、字体等的工具，只需一键即可自动传送到您的项目中。
- [Bonanza](https://www.bonanza.com/) - 让企业家基于回头客建立可持续业务的在线市场。
- [Pipl](https://pipl.com/) - 通过搜索Pipl的全球身份信息索引，使用电子邮件地址、社交用户名或电话号码识别、搜索和验证员工，减少客户摩擦，打击欺诈，节省审查和研究时间。

### Finance

- [FIGR](https://www.figr.app/) - Google Docs + 计算器 = 个人理财
- [LiveFortunately](https://app.livefortunately.com/) - 为更好的财务未来制定计划。创建完整的财务计划，充分利用您的资金。
- [KeeperTax](https://www.keepertax.com/ask-an-ai-accountant-2-0) - AI会计师，帮助您获得帮助和计算税务相关问题。

### Patents

- [Espacenet](https://worldwide.espacenet.com/) - 免费访问超过1.3亿份专利文件。
- [Google Patents](https://patents.google.com/) - 搜索并阅读来自世界各地的专利全文。
- [WIPO Patentscope](https://patentscope.wipo.int/search/en/search.jsf) - 搜索1.05亿份专利文件，包括430万份已发布的国际专利申请(PCT)。
- [Patently Apple](https://www.patentlyapple.com/patents-applications/) - 探索苹果公司的专利申请。
- [USPTO Report](https://uspto.report/) - 提供美国专利商标局专利相关信息和报告的平台。

### Marketing

- [Telega](https://telega.io/) - 在Telegram中寻找目标受众并启动有效的广告活动。
- [IGMassDMS](https://igmassdms.com/) ($) - 在不使用您账户的情况下向目标受众发送Instagram直接消息。
- [Product Management Tools](https://rohitverma.gumroad.com/l/PM-tools) ($) - 产品管理工具集合，售价5美元。
- [Yes Promo](https://yespromo.me/) - 100多个成功的Reddit自我推广帖子的免费数据库。
- [Marketing for Startups E-Book](https://www.welovenocode.com/marketingforstartups) - 免费电子书，包含47种获得客户和实现指数级增长的实用策略。
- [ArrayList](https://arraylist.org/) - 云端列表数据库，用于存储表单提交、电子邮件订阅表单或进行AJAX/REST调用来存储数值。
- [SuperMeme AI](https://www.supermeme.ai/) ($) - 使用AI生成110多种语言的原创表情包。
- [Apollo](https://www.apollo.io/) - 销售智能和参与平台，搜索、接触并转化超过6000万家公司的2.5亿多个联系人。
- [Vocus.io](https://vocus.io/) - 个性化电子邮件活动，跟踪和自动化电子邮件跟进，安排约会，与您的CRM同步，并与您的团队协作。

#### Social Media

- [Instagram Caption Maker](https://apps4lifehost.com/Instagram/CaptionMaker.html) - IG标题的简洁美观换行符。
- [Social Sizes](https://socialsizes.io/) - 社交媒体的图片和视频尺寸。
- [Ocoya](https://www.ocoya.net/) ($) - 创建、自动生成和安排内容的平台。
- [Imgupscaler](https://imgupscaler.com/?ref=producthunt) - 基于AI的PNG/JPG图像放大器。
- [Namechk](https://namechk.com/) - 在几秒钟内检查用户名或域名的可用性。30个域名和90多个社交媒体账户。
- [Inflact](https://inflact.com/downloader/instagram/video/) - 将Instagram上的原始高质量视频保存到您的设备。
- [TikTok Video Downloader](https://ttvdl.com/) - 下载TikTok视频。
- [SWAPD](https://swapd.co/) - 提供虚拟物品和服务买卖交易的中介服务，提供安全平台，连接用户与庞大的买家、卖家网络和数字市场中的机会。

### Trends

- [Google Trends](https://trends.google.com/trends/?geo=US) - 基于Google搜索探索热门话题和见解。
- [Google Trends - Visualize](https://trends.google.com/trends/hottrends/visualize) - 可视化和探索Google上的最热门趋势。
- [Statista](https://www.statista.com/) - 统计数据和可视化平台，提供各行业和主题的见解。
- [Google Books Ngram Viewer](https://books.google.com/ngrams) - 图形化显示所选年份书籍语料库中短语出现频率。

### Meetings

- [When2meet](https://www.when2meet.com/) - 安排团体会议的免费服务。允许用户创建和参与可用性调查，找到团体聚会的最佳时间。
- [Sessions](https://sessions.us/) - 旨在增强会议体验的基于Web的会议工具。旨在改善整体会议体验，免费提供。
- [Form to Chatbot](https://formtochatbot.com/) - 将Google表单转换为聊天机器人形式的互动对话。此工具有助于基于表单回复创建引人入胜的动态交互。

## Jobs

- [Y Combinator Jobs](https://www.ycombinator.com/jobs) - 发现Y Combinator策划的最佳初创公司工作机会。
- [Coroflot](https://www.coroflot.com/discover) - 专门为设计师量身定制的求职平台。
- [Cool Startup Jobs](https://www.coolstartupjobs.com/) - 探索成长型初创公司的工作机会，给您的股票期权一个机会。
- [Anon Friendly](https://anonfriendly.com/) - 寻找尊重您匿名愿望的工作。
- [Prompt Engineering Jobs](https://prompt-engineering-jobs.com) - 探索Prompt的工程工作机会。
- [KeyValues](https://www.keyvalues.com/) - 寻找与您价值观一致的工程团队。
- [About.me](https://about.me/) - 自由职业者和企业家扩大受众和吸引客户的平台。
- [Rejected.us](https://rejected.us/) - 阅读和分享工作被拒的故事。
- [Tech Interview Handbook](https://www.techinterviewhandbook.org/) - 免费策划的面试准备材料。

### Remote Jobs

- [Remote OK](https://remoteok.com/) - 远程职位招聘板。
- [Remotive](https://remotive.com/) - 连接远程公司与优秀专业人士的平台。
- [Remote.co](https://remote.co/) - 寻找远程工作机会的资源。
- [Remote Leaf](https://remoteleaf.com/) - 专注于科技行业远程机会的招聘板。
- [Remote Leads](https://remoteleads.io/) - 在软件开发领域连接公司与远程人才的平台。
- [RemoteBear](https://remotebear.io/) - 科技和设计远程职位招聘板。
- [RemoteBase](https://remotebase.com/) - 连接远程工作者与提供远程职位公司的平台。
- [JustRemote](https://justremote.co/) - 远程工作机会招聘板。
- [JS Remotely](https://jsremotely.com/) - 专门针对远程JavaScript职位的招聘板。
- [Jobspresso](https://jobspresso.co/) - 科技、营销等领域远程工作的策划招聘板。
- [Just Join](https://justjoin.it/) - IT远程职位招聘板。
- [FlexJobs](https://flexjobs.com/) - 灵活和远程工作机会招聘板。
- [We Work Remotely](https://weworkremotely.com/) - 各行业远程职位招聘板。
- [Daily Remote](https://dailyremote.com/) - 每日更新的远程工作机会招聘板。
- [AngelList Candidates](https://angel.co/candidates/overview) - 连接初创公司与潜在候选人的平台，包括远程职位。
- [Hired](https://hired.com/) - 连接技术人才与创新公司的平台。
- [PowerToFly](https://powertofly.com/) - 专注于科技行业女性远程机会的招聘板。
- [SkipTheDrive](https://skipthedrive.com/) - 各行业远程职位招聘板。
- [Authentic Jobs](https://authenticjobs.com/) - 创意和技术专业人士的招聘板，包括远程职位。
- [Working Nomads](https://workingnomads.co/) - 各行业远程职位招聘板。
- [Europe Remotely](https://europeremotely.com/) - 专门针对欧洲远程职位的招聘板。
- [Virtual Vocations](https://virtualvocations.com/) - 远程办公和远程职位招聘板。

### Freelancing

- [Remote Starter Kit](https://www.remotestarterkit.com/) - 远程团队工具和流程的终极清单。
- [Fiverr](https://www.fiverr.com/) - 为您的业务寻找完美的自由职业服务。
- [Upwork](https://www.upwork.com/) - 雇佣自由职业者并在线获得自由职业工作。

### Portfolio / CV / Resume

- [University of Nebraska Omaha - Job & Internship Resources](https://www.unomaha.edu/student-life/achievement/academic-and-career-development-center/career-development/jobs-and-internships/job-internship-resources.php) - 内布拉斯加大学奥马哈分校学术和职业发展中心提供的简历和求职信资源。
- [VisualCV](https://www.visualcv.com/resume-samples/) - 500多个专业简历样本集合。
- [SuperPortfolio](https://superportfolio.co/) - 在线作品集制作器。
- [Referd.ai](https://www.referd.ai/resume-scanner) - 免费简历扫描器。
- [RxResu.me](https://rxresu.me/) - 免费开源简历构建器。
- [GoodCV](https://www.goodcv.com/) - 无需Photoshop或AI技术，几分钟内创建专业简历/CV。
- [JSON Resume](https://jsonresume.io/) - 根据规范上传您的JSON简历并进行精美渲染。
- [CVmkr](https://cvmkr.com/) - 免费创建、维护、发布和分享您的简历。
- [Novoresume](https://novoresume.com/) - 在线简历构建器。
- [HelloTechRecruiters](https://hellotechrecruiters.com/) - 为技术招聘人员量身定制。
- [FlowCV](https://flowcv.com/) - AI增强的简历构建器、求职信、工作跟踪器、电子邮件签名、个人网站。
- [Signature Maker](https://signature-maker.net/) - 创建手写数字签名。

### Careers

- [Roadmap.sh](https://roadmap.sh/) - 提供各种开发领域的路线图。
- [WTF Should I Do With My Life](https://www.wtfshouldidowithmylife.com/) - 探索和了解不同职业的资源。
- [path-to-polymath.notion](https://path-to-polymathy.notion.site/path-to-polymathy/Path-To-Polymathy-d7586429b7ce4db1889fc539822b9670) - 整理了所有学科和领域的安排，以及构成它们的具体主题。

## Startups

- [Startup Growth Calculator](https://growth.tlb.org/) - 计算您的初创公司增长所需的资金。
- [Startup Equity Calculator](https://capbase.com/startup-equity-calculator/) - 基于不同变量为您的初创公司分配股权。
- [Fifty Years Progress Map](https://progress.fiftyyears.com/) - 突出显示低初创投资的大型市场。提供按竞争力排名的大规模市场列表，包括市场规模和过去10年A轮初创公司的总投资。为每个市场计算竞争比率。
- [Museum of Websites](https://www.kapwing.com/museum-of-websites) - 展示著名互联网公司如何随时间变化的画廊。
- [Goal Examples](https://hypercontext.com/goal-examples) - 为技术领域每个角色精心策划的目标示例列表。
- [Startup Resources](https://www.feedough.com/startup-resources/) - 为初创公司分类的资源集合。
- [500+ Free Tools For Startups](https://docs.google.com/spreadsheets/d/1s6-hGBh0_tqa-jd23fsdYuwbmS8UPmElPqaH-Rnoa_A/htmlview) - 初创公司免费工具的综合列表。
- [Founder Resources](https://www.founderresources.io/) - 为初创公司提供的免费策划资源、模板和工具。
- [100+ Resources on GPT-3](https://harishgarg.gumroad.com/l/wiSvc?ref=producthunt) - 100多个GPT-3资源的策划列表。
- [100+ Resources for Building a Successful Startup](https://cerdeira.notion.site/b3b5f44d37cf4843b3fcd2f300354467?v=8f3458522f4542d8896ebb3720c14b2d) - 构建成功初创公司的资源汇编。
- [2,500 Accelerators Incubators](https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fattachments.convertkitcdnn2.com%2F587796%2F16ab0442-1232-4a49-956c-acafd6df4189%2F2%2C500%2520Accelerators%2520Incubators.xlsx&wdOrigin=BROWSELINK) - 2,500个加速器和孵化器的列表。
- [The Complete Unicorn List](https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fattachments.convertkitcdnn2.com%2F587796%2F476863f4-bda0-4132-8bd0-d25728513cfd%2FThe%2520Complete%2520Unicorn%2520List.xlsx&wdOrigin=BROWSELINK) - 1,016个独角兽公司（估值超过10亿美元的私人公司）的综合列表。
- [GitHub Email Hunter](https://chrome.google.com/webstore/detail/github-email-hunter/ppcegaekdbgcgbapfdcjbhednhmgcjnk) - 一键查找GitHub用户和仓库的电子邮件地址。
- [The Angel Philosopher](https://theangelphilosopher.com/) - Naval智慧、知识和思想的汇编。
- [Under Glass](https://underglass.io/) - 对世界最佳数字产品的分析。
- [The Perfect Pitch Deck](https://attachments.convertkitcdnn2.com/587796/1e723803-ab50-4a61-9b3a-f347aa436408/The%20Perfect%20Pitch%20Deck.pdf) - 从分析350多个初创公司路演稿中得出的经验教训。
- [Old Computers Museum](https://oldcomputers.net/) - 探索拥有150件展品的老式和复古计算机博物馆。
- [Startups List](https://www.startups-list.com/) - 不同地方最佳初创公司的集合。
- [Pessimists Archive](https://pessimistsarchive.org/) - 唤醒我们对新技术、想法和趋势常常引起的歇斯底里、技术恐惧症和道德恐慌的集体记忆的项目。

### Failures

- [Failory - Google Failures](https://www.failory.com/google/) - 从Google的100多个失败案例中学习，以构建盈利业务和扩展被收购的公司。
- [Killed by Google](https://killedbygoogle.com/) - Google推出并终止的项目。

### Finding Ideas

- [Random Startup Website Generator](https://tiffzhang.com/startup/) - 随机初创公司网站生成器。
- [AnswerSocrates](https://answersocrates.com/) - 免费发现人们在Google上询问的几乎任何主题的问题。
- [IdeasAI](https://ideasai.com/) - 由OpenAI的GPT-3生成的想法。
- [AnswerThePublic](https://answerthepublic.com/) - 发现人们在热门搜索引擎中询问的问题。
- [List of Emerging Technologies](https://en.wikipedia.org/wiki/List_of_emerging_technologies) - 维基百科新兴技术列表。
- [UniCorner](https://unicorner.news/) - 每周一早晨为您的收件箱提供新兴初创公司2分钟概览的通讯。
- [DemandHunt](https://demandhunt.com/) ($) - 发现和投票支持新初创公司的平台。

### Connectivity

- [Integromat](https://www.integromat.com/en?pc=referralbonus) - 几次点击即可连接应用程序并自动化工作流程。
- [IFTTT](https://ifttt.com/) - 快速轻松地自动化您最喜欢的应用程序和设备，使它们以新的强大方式协同工作。
- [Franz](https://meetfranz.com/) - 在一个平台中管理您的所有消息应用程序，如WhatsApp、Facebook Messenger、Slack、Telegram等。
- [Google Remote Desktop](https://remotedesktop.google.com/) - 远程连接您的家庭或工作电脑，或与他人共享您的屏幕。
- [RecWide](https://www.recwide.com/) - 屏幕和网络摄像头录制器。免费，在线（无需下载）。
- [1,000 True Fans](https://kk.org/thetechnium/1000-true-fans/) - 培养1,000个忠实粉丝以实现可持续创意成功的概念。
- [Alias](https://alias.co/) - 与最好的保持同步。
- [LittleSis](https://littlesis.org/) - 商业和政府高层人物关系的免费数据库。

### Design

- [Social Image Maker](https://socialimagemaker.io/) - 轻松创建社交媒体图片的工具。
- [Open Source Design Resources](https://opensourcedesign.net/resources/) - 展示提供开放许可图标、字体、图片、工具和其他设计资源的网站和平台。
- [Transparent Textures](https://transparenttextures.com/) - 为设计项目提供透明纹理的平台。
- [Collection of $0 Design Tools](https://www.producthunt.com/e/0-design-tools) - 帮助项目创建的免费设计工具集合。
- [Easel.ly](https://www.easel.ly/) - 可视化各种类型信息的设计工具。
- [Material Design](https://m3.material.io/) - Google为构建Android、iOS、Flutter和网页高质量数字体验而设计的设计系统。
- [Rasterizer.io](https://rasterizer.io/) - 创建动态图像的工具。
- [Jitter.Video](https://jitter.video/) ($) - 网页上的简单动画工具。
- [Polotno Studio](https://studio.polotno.com/) - 无需注册和广告即可创建图形设计的网页应用程序，Canva的免费替代品。
- [BitBof](https://bitbof.com/) - 免费绘画和素描应用程序。
- [Sumo](https://sumo.app/) - 绘图工具和图像编辑器。
- [Random Design Stuff](https://randomdesignstuff.com/) - 浏览为设计师精心挑选的网站，既有用的也有无用的。
- [Discover NFT Club](https://www.discovernft.club/) - 发现最新的NFT项目。
- [Haikei](https://app.haikei.app/) - 基于网络的设计工具，为各种目的生成独特的SVG设计资产。
- [Spline](https://spline.design/) - 创建3D场景、编辑材质和建模3D对象，控制设计工作的结果。
- [Visiwig](https://www.visiwig.com/) - 仅通过点击和粘贴创建图形。
- [FreeType](https://freetype.org/) - 用C语言编写的字体渲染软件库，能够为大多数矢量和位图字体格式产生高质量输出，设计为小巧、高效、高度可定制和便携。

#### Colors

- [HexColor](https://hexcolor.co/) - 提供各种免费颜色工具。
- [Adobe Color Wheel](https://color.adobe.com/create/color-wheel) - 可用于生成调色板的色轮。
- [SchemeColor](https://www.schemecolor.com/) - 允许您下载配色方案。
- [Mobile Palette Generator](https://mobilepalette.colorion.co/) - 生成移动端调色板的工具。
- [DeGraeve Color Palette Generator](https://www.degraeve.com/color-palette/) - 生成调色板。
- [0to255](https://0to255.com/) - 基于任何颜色帮助查找更亮和更暗颜色的颜色工具。
- [ColorHexa](https://www.colorhexa.com/) - 提供任何颜色的信息并生成匹配的调色板。
- [Color Hunt](https://colorhunt.co/) - 发现精心挑选的调色板。
- [Coolors](https://coolors.co/) - 配色方案生成器和调色板创建工具，允许用户为设计项目探索、创建和分享颜色组合。
- [Colors.lol](https://colors.lol/) - 提供简单界面生成调色板的网站，具有保存和导出选项，用于数字设计。

#### Fonts

- [Font Squirrel](https://www.fontsquirrel.com/) - 免费字体乌托邦。
- [Font Discovery](https://fontdiscovery.typogram.co/) - 为创作者、创始人、制作者提供的每周设计、字体和颜色创意新闻通讯。
- [MyFonts](https://www.myfonts.com/) - 超过130,000种可用字体，且数量还在增长。
- [Google Fonts](https://fonts.google.com/) - Google的免费开源字体集合。
- [DaFont](https://www.dafont.com/) - 提供免费字体下载的热门网站，具有各种风格和用途的分类，包括装饰性、手写和无衬线字体。
- [UCL Fonts Project](http://vecg.cs.ucl.ac.uk/Projects/projects_fonts/projects_fonts.html) - 专注于字体流形的研究项目，提供其工作成果和交互式2D字体流形演示。

#### Icons / Icon Packs

- [The Noun Project](https://thenounproject.com/) - 提供大量免费图标和库存照片的平台，可用于各种项目和设计。
- [IconPacks](https://www.iconpacks.net/) - 提供各种图标包供个人和商业使用的平台。
- [Doodlicons on Notion](https://www.notion.so/Doodlicons-519314a92ed3474093a10e44946bbb72) - Notion上项目线框图的涂鸦图标。
- [Illustration Kit](https://illustrationkit.com/) - 个人和商业项目免费矢量插图集合。
- [FontAwesome](https://fontawesome.com/) - 互联网图标库和工具包，被设计师、开发者和内容创作者广泛使用。
- [Iconshock](https://www.iconshock.com/freeicons/) - 提供来自各种开源集合图标的平台，有10万个图标可免费下载。
- [3DIcons](https://3dicons.co/) - 在CC0许可下可免费商业和个人使用的3D图标集合。
- [Fontello](https://fontello.com/) - 创建自定义图标字体的图标字体生成器。
- [HealthIcons](https://healthicons.org/) - 各种用例的免费开源健康图标。
- [TablerIcons](https://tablericons.com/) - 开源免费SVG图标，高度可定制，商业使用无需署名。
- [David Li](https://david.li/) - 基于粒子的3D模拟和渲染平台。
- [IcoMoon](https://icomoon.io/) - 创建和管理图标字体的平台。
- [UTF8Icons](https://www.utf8icons.com/) - UTF-8标准中Unicode符号的集合。
- [Free Isometric Illustrations](https://passionhacks.com/free-isometric-illustrations/) - 各种项目的免费等距插图集合。
- [Illlustrations](https://illlustrations.co/) - 创意项目的开源插图套件。
- [PixelBazaar](https://www.pixelbazaar.com) - 为有特色的品牌提供有态度的图标。
- [Iconfinder](https://www.iconfinder.com/) ($) - 提供图标、插图、3D插图、设计师和免费图标的平台。
- [Iconz Design](https://iconz.design/) ($) - 223个图标的高级3D库。
- [HugeIcons.pro](https://hugeicons.pro/) ($) - 提供超过25,000个图标的平台，有5种独特风格，分布在57个热门类别中。

#### Stock Images

- [The Stocks](https://thestocks.im/) - 聚合器，提供来自各种来源的免费库存照片、视频和音乐。
- [Pexels](https://www.pexels.com/) - 提供高质量库存照片和视频免费下载和使用的平台。
- [Unsplash](https://unsplash.com/) - 为创意项目提供大量高分辨率免版税图片的网站。
- [FreeImages](https://www.freeimages.com/) - 提供多样化免费库存照片供个人或商业使用的平台。
- [Pixabay](https://pixabay.com/) - 社区驱动的平台，贡献者分享高质量库存图片、视频和音乐。
- [PNG Guru](https://www.pngguru.in/) - 免费PNG图片、背景和模板的来源。
- [Pond5](https://www.pond5.com/free) - 提供免费库存视频、照片和音乐资源的平台。
- [Critter Pics](https://www.critter.pics/) - 为创意项目提供小动物图片的集合。
- [Stock Up](https://stockup.sitebuilderreport.com/) - 索引来自31个不同免费库存照片网站的35,356张照片，便于访问。
- [Shutterstock](https://www.shutterstock.com/) - 提供大量免版税图片、视频、矢量、插图和音乐库的平台。
- [Zoom.nl](https://zoom.nl/) - 荷兰最大的摄影社区，为摄影爱好者提供资源和平台。
- [Depositphotos](https://depositphotos.com/) - 拥有2.32亿文件的平台，包括免版税图片、视频、矢量、插图和音乐。
- [Skuawk](https://skuawk.com/) - 各种创意项目的公共领域照片来源。
- [All-Free-Download](https://all-free-download.com/) - 提供可在个人或商业项目中免费使用的图形艺术的平台。

#### Wallpapers

- [Wallpapers.com](https://wallpapers.com/) - 提供不同主题和风格壁纸的网站。
- [WallpaperCave](https://wallpapercave.com/) - 提供各种类别高质量壁纸集合的平台。
- [Wallhaven](https://wallhaven.cc/) - 拥有大量高分辨率壁纸集合的壁纸社区。
- [MovieMania](https://www.moviemania.io/phone) - 手机无文字高分辨率电影壁纸数据库。
- [WallpaperTip](https://www.wallpapertip.com/) - 上传和发现免费高清壁纸的平台。
- [SimpleDesktops](https://simpledesktops.com/browse/) - 桌面背景的极简壁纸集合。
- [WallpaperFlare](https://www.wallpaperflare.com/search?wallpaper=vertical) - 提供高分辨率垂直壁纸的网站。
- [Positron Dream](https://www.positrondream.com/wallpapers-all) - 可下载的抽象壁纸集合。

## Art

- [WeavesSilk](https://weavesilk.com/) - 使用Silk创建美丽流动的艺术作品。
- [Google Arts & Culture](https://artsandculture.google.com/) - 将世界艺术和文化在线呈现给每个人的平台。
- [ArtGraphica](https://www.artgraphica.net/) - 免费绘画、素描和绘画技巧的资源。
- [Tattoos Wizard](https://tattooswizard.com/) - 寻找您附近的纹身艺术家和工作室。
- [The Art Institute of Chicago Collection](https://www.artic.edu/collection) - 探索博物馆收藏的数千件艺术品。
- [ZoomQuilt](https://zoomquilt.org/) - 协作无限缩放绘画。
- [Invaluable](https://www.invaluable.com/) - 世界顶级在线拍卖平台，每日添加数千件拍品。Invaluable为随时随地发现和获得卓越艺术品和物品提供便利。
- [50 Watts](https://50watts.com/) - 来自世界各地奇异精彩视觉短篇作品的档案

### Photography

- [Cambridge in Colour](https://www.cambridgeincolour.com/) - 摄影师学习社区
- [Exposure Guide](https://www.exposureguide.com/) - 摄影技巧、技术和教程

### Art Communities

- [Ello](https://ello.co/discover)
- [Behance](https://www.behance.net/)
- [ArtStation](https://www.artstation.com/)

## Academia

- [TLDR This](https://tldrthis.com/) - 将任何文本总结为简洁易懂内容的平台，帮助用户克服信息过载。
- [Archive.org General Index](https://archive.org/details/GeneralIndex) - 提供访问超过1.07亿篇期刊文章的综合索引。
- [Homework Help Global](https://www.homeworkhelpglobal.com/) - 提供专业和定制论文写作服务的在线平台。
- [Dartmouth Academic Careers](https://sites.dartmouth.edu/nyhan/academic-careers/) - 提供学术职业见解的资源。
- [CNX](https://cnx.org/) - 查看和分享免费教育材料的平台。
- [Reach Out Michigan Tutorials](https://www.reachoutmichigan.org/learn/tutorials.html#math) - 涵盖各种主题的在线教程和参考资料集合。
- [Open Text BC](https://opentextbc.ca/) - 简单的图书制作软件，允许用户发布教科书、学术专著、教学大纲、小说和非小说书籍、白皮书等多种格式。
- [Modern for Wikipedia](https://chrome.google.com/webstore/detail/modern-for-wikipedia/emdkdnnopdnajipoapepbeeiemahbjcn) - 通过现代化可定制设计增强Wikipedia体验的Chrome扩展。
- [Wikiwand - Wikipedia Modern](https://chrome.google.com/webstore/detail/wikiwand-wikipedia-modern/emffkefkbkpkgpdeeooapgaicgmcbolj) - 优化Wikipedia内容以改善阅读体验的Chrome扩展。
- [List of Academic Databases and Search Engines](https://en.wikipedia.org/wiki/List_of_academic_databases_and_search_engines) - 列出学术数据库和搜索引擎的Wikipedia页面。
- [Scribbr APA Citation Generator](https://www.scribbr.com/citation/generator/apa/) - 提供准确APA引文的平台，经专家验证，受数百万人信任。
- [Bridges: About Institutions, Histories, and Artifacts](https://temple.manifoldapp.org/projects/bridges) - 关于美国学院和大学生活的机构、历史和文物的资源。
- [Tropy](https://tropy.org/) - 通过将照片转化为物品来组织研究的平台。
- [Linda Hall Library Catalog](https://catalog.lindahall.org/discovery/search?vid=01LINDAHALL_INST:LHL) - Linda Hall图书馆目录，允许您搜索书籍、期刊、会议论文集、技术报告和标准以及其他材料，专注于科学、工程和技术。
- [Project Abstracts](https://projectabstracts.com/) - 各个领域学术小项目和毕业项目的项目摘要和下载集合。
- [SCIRP Open Access Journal](https://www.scirp.org/journal/OpenAccess) - 提供各种科学学科开放获取学术期刊的平台，促进免费获取研究。
- [DOI.org](https://www.doi.org/) - 数字对象标识符(DOI)的官方网站，提供数字资源（包括学术论文和数据集）的持久标识和访问系统。

### Studying

- [Bartleby](https://www.bartleby.com/) - 搜索教科书、作业问题的逐步解释等内容的平台。
- [Chegg](https://www.chegg.com/) ($) - 提供24/7课程帮助的服务，包括教科书解答和专家问答。
- [Chegg Flashcards](https://www.chegg.com/flashcards) (@) - 使用学生和专家为各种课程创建的闪卡学习。

### Calculators

- [Calc Resource](https://calcresource.com/index.html) - 提供各种计算器和数学计算资源的平台。
- [eFunda](https://www.efunda.com/home.cfm) - 提供计算器、公式以及材料和工艺信息的网站。
- [LCM Calculator](https://www.calculator.net/lcm-calculator.html) - 查找最小公倍数的计算器。
- [GCF Calculator](https://www.calculator.net/gcf-calculator.html?numberinputs=9%2C+57%2C+72&x=75&y=22) - 查找最大公因数的计算器。
- [CalculatorSoup](https://www.calculatorsoup.com/) - 为不同数学目的提供各种计算器的在线平台。
- [RapidTables](https://www.rapidtables.com/) - 提供计算器和表格集合供快速参考的网站。
- [Linear Algebra Calculator](https://www.emathhelp.net/en/linear-algebra-calculator/?u=3%2C1%2C4&v=-2%2C0%2C5&action=cross+product) - 线性代数计算器，如叉积。
- [Wikipedia: List of Physical Quantities](https://en.wikipedia.org/wiki/List_of_physical_quantities) - 列出各种物理量的Wikipedia页面。
- [eMathHelp Linear Algebra Calculator](https://www.emathhelp.net/en/calculators/linear-algebra/) - 线性代数计算的在线计算器。
- [Online Math School](https://onlinemschool.com/math/assistance/) - 提供数学帮助以及各种计算器和资源的平台。

### MOOC (Massive Open Online Courses)

- [InfoCobuild - Audio Video Courses](http://www.infocobuild.com/education/audio-video-courses/) - 来自世界各地学院和大学免费音频/视频学术课程讲座的集合。按学术科目进行良好分类，包括生物学、化学、计算机科学、经济学、电子和电气工程、历史、文学、材料科学、数学、物理学和心理学。
- [MIT OpenCourseWare](https://ocw.mit.edu/) - MIT的倡议，提供对广泛课程材料的免费开放访问。
- [Coursera](https://www.coursera.org/) - 在线学习平台，提供来自世界各地大学和组织的课程、证书和学位项目。
- [Wikiversity](https://en.wikiversity.org/wiki/Wikiversity:Main_Page) - 维基媒体基金会项目，提供免费的教育内容和资源。
- [Udemy](https://www.udemy.com/) - 平台提供由专家教授的各种主题的大量在线课程。
- [Open Culture](https://www.openculture.com/) - 提供免费文化和教育媒体的网站，包括课程、教科书和有声读物。
- [edX](https://www.edx.org/) - 在线学习平台，提供来自世界各地大学和机构的课程、证书和学位。
- [Udacity](https://www.udacity.com/) - 专注于技术相关课程和与行业领导者合作设计的纳米学位项目的平台。
- [Stanford Online](https://online.stanford.edu/) - 斯坦福大学的在线学习平台，提供各种课程和项目。
- [OpenLearn](https://www.open.edu/openlearn/) - 开放大学的倡议，提供对课程材料和教育资源的免费访问。
- [Open Learning Initiative (OLI)](https://oli.cmu.edu/) - 卡内基梅隆大学的平台，提供公开可用的课程和资源。
- [MITx](https://www.mitx.org/) - MIT的平台，提供专注于前沿研究的在线课程和项目。
- [Open Yale Courses](https://oyc.yale.edu/) - 耶鲁大学的倡议，提供由杰出教师教授的入门课程的免费访问。
- [Alison](https://alison.com/) - 平台提供各种主题的免费在线课程和文凭。
- [Academic Earth](https://academicearth.org/) - 聚合来自世界顶级大学在线课程的网站。
- [NPTEL](https://nptel.ac.in/) - 国家技术增强学习计划，提供工程和科学在线课程。
- [University of the People](https://www.uopeople.edu/) - 在线大学，提供免学费的认证学位项目。
- [Canvas Network](https://www.canvas.net/) - 使用Canvas学习管理系统提供来自各种机构的在线课程和项目的平台。
- [Isaac Newton Institute for Mathematical Sciences](https://www.newton.ac.uk/) - 研究所网站，提供数学科学资源和项目的访问。
- [Saylor Academy](https://www.saylor.org/) - 非营利倡议，提供免费的自定进度在线课程。
- [Connexions](https://cnx.org/) - 提供各种学科领域开放教育资源和教科书的平台。
- [Directory of Open Access Journals (DOAJ)](https://doaj.org/) - 在线目录，提供对高质量开放获取科学和学术期刊的访问。
- [Learning on the Internet](https://www.learningontheinternet.com/?ref=producthunt) - 展示来自各种来源的精选教育内容和资源的平台。
- [Class Central](https://www.classcentral.com/) - 在线课程的搜索引擎和评论平台，聚合来自各种提供商的课程。
- [OCW SNU](https://ocw.snu.ac.kr/) - 首尔国立大学的开放课件。

## Science

- [Reproducibility Institute](https://reproducibilityinstitute.org/w/) - 提供学术论文、速查表和付费课程种子的项目。
- [EBSCO](https://www.ebsco.com/) - 研究数据库、电子期刊、杂志订阅、电子书和发现服务的提供商。
- [Zooniverse](https://www.zooniverse.org/) - 参与真实研究，拥有超过50个活跃的在线公民科学项目。
- [Experiment](https://experiment.com/) - 帮助资助下一波科学研究。
- [Closer to Truth](https://www.closertotruth.com/) - Robert Lawrence Kuhn探索宇宙的基本问题。
- [Nature Scitable](https://www.nature.com/scitable/) - 科学概述图书馆。定制您自己的电子书，创建在线课堂，贡献和分享内容，并与同事网络连接。
- [Vinaire](https://vinaire.me/) - 物理和数学课程。
- [VisualPDE](https://visualpde.com/) - 探索科学和数学的交互式平台，提供波浪、病毒和反应扩散模式等主题的模拟。
- [Whole Earth](https://wholeearth.info/) - Whole Earth出版物的近乎完整档案，这是Stewart Brand和POINT基金会从1968年到2002年出版的一系列期刊和杂志，为学术、教育和研究目的提供。
- [Sketchplanations](https://sketchplanations.com/categories/science) - 科学概念的简单草图和视觉解释集合，旨在使复杂主题更易理解。

### Biographies

- [Mathematics History](https://mathshistory.st-andrews.ac.uk/) - 包含3000多位数学家传记和2000多页论文及支持材料的免费在线资源。
- [Web of Stories](https://www.webofstories.com/) - 聆听我们时代一些伟大人物的生活故事。
- [Letters of Note](https://lettersofnote.com/) - 历史上最迷人的信件。
- [Organism Earth Library](https://www.organism.earth/library/) - 生物地球图书馆。
- [Darwin Project](https://www.darwinproject.ac.uk/) - 进化科学家查尔斯·达尔文（1809-1882）写的信件。
- [Darwin Online](https://darwin-online.org.uk/) - 世界上最大最广泛使用的查尔斯·达尔文资源。
- [Newton Project](https://www.newtonproject.ox.ac.uk/) - 艾萨克·牛顿爵士（1642-1727）所有著作的在线版本。
- [Bethe](https://bethe.cornell.edu/index.html) - 汉斯·贝特的个人和历史观点。
- [Open Source Shakespeare](https://www.opensourceshakespeare.org/) - 开源莎士比亚。
- [Leonardo da Vinci](https://www.leonardodavinci.net/) - 列奥纳多·达·芬奇，他的生活和艺术作品。
- [Our Karl Popper](https://ourkarlpopper.net/) - 卡尔·波普尔如何改变了我们的生活（来自五大洲的证词）。
- [Varlam Shalamov](https://shalamov.ru/en/) - 瓦尔拉姆·沙拉莫夫的著作和历史背景，这位俄国作家以其关于苏联劳改营监禁的短篇小说系列而闻名。
- [Samuel Beckett On-Line Resources](https://www.samuel-beckett.net/) - 塞缪尔·贝克特在线资源和链接页面。（塞缪尔·贝克特的《[等待戈多](https://www.samuel-beckett.net/Waiting_for_Godot_Part1.html)》）
- [Philip K. Dick](https://philipdick.com/) - 致力于科幻作家菲利普·K·迪克（1928-82）的生活和作品。
- [Alan Turing Digital Archive](https://turingarchive.kings.cam.ac.uk/) - 这个数字档案包含图灵的许多信件、谈话记录、照片和未发表的论文。
- [Turing.org.uk](https://www.turing.org.uk/) - 致力于数学家和计算机科学家艾伦·图灵的生活和工作的平台。
- [Sherlock Holmes Series](https://sherlock-holm.es/) - 阿瑟·柯南·道尔爵士的夏洛克·福尔摩斯系列（公有领域，免版权）。
- [Feynman Lectures Online](https://www.feynmanlectures.caltech.edu/) - 理查德·费曼的在线讲座。
- [Leibniz Translations](https://www.leibniz-translations.com/index2.php) - 提供哲学家和数学家戈特弗里德·威廉·莱布尼茨作品翻译的资源。
- [David Hume](https://davidhume.org/) - 致力于苏格兰哲学家大卫·休谟的平台。准确、有用地呈现休谟几乎所有著作。
- [Fooled by Randomness](https://fooledbyrandomness.com/) - 纳西姆·尼古拉斯·塔勒布的主页。
- [Prabook](https://prabook.com/web/home.html/) - 提供著名人物传记信息的平台。
- [Famous Mathematicians](https://famous-mathematicians.org/) - 展示著名数学家信息和传记的网站。

### Books, Articles, Texts

- [Archive.org/texts](https://archive.org/details/texts) - 提供对大量数字内容集合免费通用访问的数字图书馆。
- [Open Library](https://openlibrary.org/) - 包含图书元数据的通用目录，可访问广泛的数字图书。
- [OpenStax](https://openstax.org/) - 为教育目的提供免费灵活的教科书和资源。
- [Project Gutenberg](https://www.gutenberg.org/) - 提供超过60,000本免费电子书（包括许多经典作品）的数字图书馆。
- [Wikibooks](https://en.wikibooks.org/wiki/Main_Page) - 任何人都可以编辑的开放内容教科书集合。
- [Wikisource](https://wikisource.org/wiki/Main_Page) - 允许协作改进其内容的免费图书馆。
- [MIT Classics](https://classics.mit.edu/) - 从59位不同作者的441部经典文学作品列表中选择。
- [Goodreads Free eBooks](https://www.goodreads.com/ebooks?sort=readable) - Goodreads上提供的免费图书。
- [Lit2Go](https://etc.usf.edu/lit2go/) - 免费在线故事和诗歌有声读物集合（Mp3格式）。
- [Booksc](https://booksc.org/) - 世界最大的科学文章存储库，包含7000万+免费文章。
- [IntechOpen](https://www.intechopen.com/books) - 阅读、分享和下载超过5,800本同行评审的开放获取图书。
- [FreeTechBooks](https://www.freetechbooks.com/) - 免费/开放获取在线计算机科学图书、教科书和讲义的数据库。
- [FreeComputerBooks](https://freecomputerbooks.com/) - 提供免费计算机科学和编程图书集合的平台。
- [Manning](https://www.manning.com/) - 编程图书出版商。
- [Holy Books](https://holybooks.com/) - 下载免费PDF格式的精神文本电子书。
- [Librivox](https://librivox.org/) - 提供来自世界各地志愿者朗读的免费公有领域有声读物。
- [Online Books Page](https://onlinebooks.library.upenn.edu/) - 网络上超过300万本免费图书的列表。
- [Audible](https://www.audible.com/) - 提供优质音频故事讲述的平台，拥有广泛的有声读物选择。
- [VCU Transcendentalism](https://archive.vcu.edu/english/engweb/transcendentalism/) - 关于先验主义文本的教育超文本空间，链接到其他互联网空间。
- [Library of Short Stories](https://www.libraryofshortstories.com/) - 在线图书馆，包含超过1000个经典短篇小说，可供阅读和下载。
- [SlideShare](https://www.slideshare.net/) - 分享演示文稿和文档的在线平台。
- [Free-eBooks.net](https://www.free-ebooks.net/) - 发现数百个虚构和非虚构类别中的数千位新作者。
- [University of Pennsylvania Digital Library](https://digital.library.upenn.edu/books/) - 提供图书集合访问的数字图书馆。
- [Feedbooks Public Domain](https://www.feedbooks.com/catalog/public_domain) - 免费提供公有领域图书目录的平台。
- [Authorama](https://www.authorama.com/) - 提供不同作者公有领域图书集合的平台。
- [Google Play - Top Selling Free Books](https://play.google.com/store/books/collection/topselling_free?clp=ChcKFQoPdG9wc2VsbGluZ19mcmVlEAcYAQ%3D%3D:S:ANO1ljKuey8&gsr=ChkKFwoVCg90b3BzZWxsaW5nX2ZyZWUQBxgB:S:ANO1ljIbX7M) - Google Play上最畅销免费图书集合。
- [Taoism.net](https://taoism.net/) - 探索道教的资源。
- [Early Modern Texts](https://earlymoderntexts.com/) - 提供早期现代哲学文本现代英语译本的平台。
- [Online Library of Liberty](https://oll.libertyfund.org/) - 涉及自由核心问题的学术作品精选集合。
- [Online Books Library](https://onlinebooks.library.upenn.edu/) - 宾夕法尼亚大学的在线图书馆，提供对网络上300万本免费图书的访问。
- [Elegant eBooks](https://www.ibiblio.org/ebooks/) - 以时尚版本查找优秀的虚构和非虚构经典作品。本网站上几乎所有电子书都来自公有领域图书。
- [22 Free Data Science Books](https://www.wzchen.com/data-science-books) - 精选的免费优质数据科学图书汇编，帮助探索数据科学职业道路的人士。每本书的最后更新日期包含在括号中。
- [Chest of Books](https://chestofbooks.com/) - 免费在线图书馆，提供各种主题的大量图书集合，包括科学、技术、艺术和文学。
- [Stephen Wolfram: A New Kind of Science](https://www.wolframscience.com/nks/) - 史蒂芬·沃尔夫拉姆《一种新的科学》图书的在线版本，提供目录和细胞自动机与复杂性相关材料的访问。

#### Book Recommendations and Summaries

- [Read Next](https://read-next.com/) - 来自科学家、投资者、企业家、名人和作者等各个领域名人推荐的3,000+本图书集合。
- [Goodbooks](https://www.goodbooks.io/) - 提供来自世界各地成功有趣人士的8,500+本图书推荐。
- [Most Recommended Books](https://mostrecommendedbooks.com/) - 策展500+专家、600+列表、500+图书系列，提供100%验证的图书推荐。
- [Books Chatter](https://bookschatter.com/) - 从人们的推文中寻找图书推荐，显示相关推文。
- [Leafmarks](https://www.leafmarks.com/) - 探索来自著名作者、顶级CEO、传奇投资者和喜爱名人的图书推荐。
- [Bookstash](https://bookstash.io/) - 展示名人推荐的顶级图书，3分钟或更短时间内总结。
- [Abakcus](https://abakcus.com/books/) - 关于数学和一些科学的图书集合。
- [BookBub](https://www.bookbub.com/welcome) - 根据您的偏好获得个性化图书推荐。
- [Hacker News Books](https://hackernewsbooks.com/) - 每周策展Hacker News上提到的最佳图书。
- [Goodreads](https://www.goodreads.com/) - 世界最大的读者和图书推荐网站。
- [What Should I Read Next?](https://www.whatshouldireadnext.com/) - 输入您喜欢的图书，网站将分析我们庞大的真实读者最爱图书数据库，为您提供图书推荐和下一步阅读建议。
- [Blas](https://blas.com/) - Blas Moros总结的超过400本图书。
- [Google Books - Talk to Books](https://books.google.com/talktobooks/) - 允许用户使用自然语言与图书对话的平台。

### Maps and Data

- [Our World in Data](https://ourworldindata.org/covid-vaccinations) - 按国家分类的COVID-19疫苗接种数据。
- [WebSDR](https://websdr.org/) - 连接到互联网的软件定义无线电接收器，允许众多听众同时收听和调谐，捕获来自地球的当前信号。
- [GSM Security Map](https://gsmmap.org/) - 比较移动网络在GSM安全方面保护能力的地图。
- [International Campuses](https://cbert.org/resources-data/intl-campus/) - 国际校园列表。
- [United States International College Campuses on Google Maps](https://www.google.com/maps/d/u/0/viewer?mid=1ckm_TSM8mbCrnhA8COpBNG-qJqTR2IW3&ll=43.664774893391936%2C24.56718262638249&z=5) - 展示美国国际大学校园的地图。
- [Submarine Cable Map](https://www.submarinecablemap.com/) - 展示世界海底电缆系统的交互式地图。
- [Observable](https://observablehq.com/) - 协作探索、分析和解释数据的平台。
- [FixPhrase](https://fixphrase.com/) - 仅用四个词定位地球上任何地方，适用于处理几个词比一长串数字更方便的情况。
- [Common Crawl](https://commoncrawl.org/) - 任何人都可以访问和分析的开放网络爬取数据存储库。
- [Mount Everest 3D Map](https://mount-everest3d.com/3d-map/) - 珠穆朗玛峰的交互式3D地图。
- [Visualize Value Archive](https://archivve.visualizevalue.com/) - Visualize Value的视觉内容档案。
- [Fliist](https://fliist.com/en) - 创建和分享您最喜爱列表的平台。
- [Search Engine Map](https://www.searchenginemap.com/) - 流行搜索引擎的视觉表示。
- [Friendly Dubinsky](https://friendly-dubinsky-cb22fe.netlify.app/) - 不同地方的地图。
- [Real-Time SpaceX Starlink Satellite Tracker](https://www.starlinkmap.org/) - 提供SpaceX星链卫星实时跟踪器的网站。
- [Album of Computational Fluid Motion](https://album-of-cfm.com/) - 展示计算流体运动图像的集合。
- [Visualization of Every Job Title in the World](https://duarteocarmo.com/blog/every-job-world) - 将世界上每个职位名称分类为10个类别的交互式可视化。
- [ObservableHQ](https://observablehq.com/@tophtucker/examples-of-bitemporal-charts) - 双时态图表示例
- [The Uncensored Library](https://uncensoredlibrary.com/en) - 由无国界记者发布、BlockWorks、DDB Berlin和MediaMonks创建的Minecraft服务器和地图，旨在规避新闻自由受限国家的审查制度
- [Lighthouse Map](https://geodienst.github.io/lighthousemap/) - 显示世界各地灯塔位置的交互式地图，为每个地点提供地理空间信息和历史。

#### Arxiv

- [ArxivXplorer](https://arxivxplorer.com/) - 专为探索arXiv科学论文而设计的搜索工具，允许用户查找和筛选各学科的研究文章。
- [Interactive Data Map of ArXiv Machine Learning Papers](https://datamapplot.readthedocs.io/en/latest/auto_examples/plot_interactive_arxiv_ml.html) - 显示ArXiv机器学习部分论文的交互式数据地图。
- [ArXiv Machine Learning Landscape](https://lmcinnes.github.io/datamapplot_examples/ArXiv_data_map_example.html) - 展示ArXiv机器学习研究景观的可视化。
- [DataMapPlot Examples](https://lmcinnes.github.io/datamapplot_examples/arXiv/) - 使用DataMapPlot库在流形上可视化数据的示例和演示集合，专注于arXiv数据集。

### Infographic

- [Information is Beautiful](https://informationisbeautiful.net/) - 专注于基于事实和数据决策的平台，通过视觉吸引的图形呈现信息。
- [Visual Capitalist](https://www.visualcapitalist.com/) - 数据驱动的视觉内容来源，涵盖市场、技术、能源和全球经济等各种主题。
- [Infographic Journal](https://infographicjournal.com/) - 涵盖广泛主题的信息图表档案。
- [D3.js](https://d3js.org/) - 基于数据操作文档的JavaScript库，能够创建动态和交互式数据可视化。
- [Deniz Cem On Duygu Portfolio](https://www.denizcemonduygu.com/) - Deniz Cem On Duygu的信息图表个人作品集。
- [Data Visualization Catalogue](https://datavizcatalogue.com/index.html) - 提供各种类型数据可视化信息的目录。
- [Tableau Public](https://public.tableau.com/app/discover) - 免费平台，可在线探索、创建和公开分享数据可视化。
- [Will Robots Take My Job?](https://willrobotstakemyjob.com/) - 估算各种职业的自动化风险、增长、工资等。
- [Preceden](https://www.preceden.com/) - 在线时间线和路线图制作工具。
- [Jason Davies](https://www.jasondavies.com/) - Jason Davies的个人网站，展示他的项目和可视化作品。

### Philosophy

- [Desolhar Philo](https://www.desolhar-philo.com/) - 关于哲学的笔记。
- [VisualizingSEP](https://www.visualizingsep.com/) - 探索斯坦福哲学百科全书的交互式可视化和搜索引擎。
- [Hakob's Sandbox](https://hakobsandbox.openetext.utoronto.ca/) - 关于科学史和科学哲学入门的在线图书。
- [Philosophy A Level](https://philosophyalevel.com/) - A-Level哲学学习资源。
- [Deniz Cemon Duygu - History of Philosophy](https://www.denizcemonduygu.com/philo/browse/) - 总结和可视化的哲学史。
- [Interactive timeline of philosophical ideas](https://www.denizcemonduygu.com/portfolio/the-history-of-philosophy/). - 重要哲学家及其思想的视觉表示和探索。
- [Beyng](https://www.beyng.com/) - 致力于马丁·海德格尔哲学的资源，提供他的作品和思想的英文译本和讨论。
- [Greg Egan's Official Website](https://www.gregegan.net/) - 科幻作家和计算机程序员Greg Egan的作品、哲学和项目信息。
- [Ayn Rand Institute Courses](https://courses.aynrand.org/) - 提供安·兰德哲学免费课程的教育平台，包括客观主义及其在生活和社会中的应用。

### Social Sciences

- [Temple Manifold - All Projects](https://temple.manifoldapp.org/projects/all) - 各种社会科学的优秀资源集合。

### History

- [Histography](https://histography.io/) - 跨越140亿年历史（从大爆炸到2015年）的交互式时间线。
- [Unenumerated Blog](https://unenumerated.blogspot.com/) - 涵盖各种历史主题的全面详细博客。
- [Human Origins - Human Family Tree](https://humanorigins.si.edu/evidence/human-family-tree) - 探索人类进化史的交互式人类家谱。
- [OldEra Timeline](https://timeline.oldera.org/) - 允许您在可缩放界面上查看整个历史的交互式时间线。
- [Nuclear Secrecy Blog](https://blog.nuclearsecrecy.com/) - 关于核机密的历史信息，深入探讨核武器的发展和影响。
- [The Ascent of Humanity](https://ascentofhumanity.com/) - 探索分离时代、重聚时代的概念，以及塑造人类历史转型的危机汇聚。
- [Today in Science History](https://todayinsci.com/) - 提供特定日期历史事件的平台。
- [Khipu Field Guide](https://khipufieldguide.com/guidebook/Introduction.html) - 提供奇普（古印加结绳记录系统）信息的指南手册。
- [Hellenistic History](https://www.hellenistichistory.com/) - 希腊化历史研究资源，涵盖从亚历山大大帝时代到罗马帝国的政治、文化和社会发展。
- [Perseus Digital Library](https://www.perseus.tufts.edu/hopper/) - 提供大量古典文本、图像和资源，用于研究古希腊和古罗马文化。
- [Futility Closet](https://www.futilitycloset.com/) - 历史、文学、语言、艺术、哲学和数学中有趣奇事的集合，旨在帮助您尽可能愉快地消磨时间。

### Geoscience

- [River Runner Global](https://river-runner-global.samlearner.com/) - 提供从世界任何地点的雨滴到其终点路径的可视化。
- [Celestrak Satellite Visualization](https://celestrak.com/cesium/orbit-viz.php?tle=/pub/TLE/catalog.txt&satcat=/pub/satcat.txt&referenceFrame=1) - 提供卫星可视化工具。
- [Mars Now](https://mars.nasa.gov/explore/mars-now/) - 显示火星卫星的当前位置，为火星探索提供见解。
- [Physical Geology](https://temple.manifoldapp.org/projects/physical-geology) - 涵盖物理地质学的基本主题。
- [Flood Site](https://floodsite.net/juniorfloodsite/) - 关于洪水的教育资源。
- [NEMO - Nucleus for European Modeling of the Ocean](https://www.nemo-ocean.eu/) - 由欧洲联盟开发的平台，提供与多种空间和时间尺度海洋建模相关的信息和资源。
- [USGS Earthquake Map](https://earthquake.usgs.gov/earthquakes/map/?extent=25.56227,4.08691&extent=45.08904,70.00488&list=false) - 美国地质调查局显示最近地震活动的地图。
- [LatLong.net](https://www.latlong.net/) - 在地图上查找任何位置纬度和经度坐标的工具，提供地理坐标和相关数据。
- [Latitude.to](https://latitude.to/) - 允许用户查找世界任何地址或位置的GPS坐标，具有详细的地图功能。

### Biology

- [Sectional Anatomy](https://www.sectional-anatomy.org/) - 放射学横断面解剖学的免费在线工具。
- [Deep Sea](https://neal.fun/deep-sea/) - 探索海洋深处的平台。
- [Ask Nature](https://asknature.org/) - 生物学启发的策略、创新或教育资源。
- [We Are Hosts for Memes](https://wearehostsformemes.com/) - 您已接触到元迷因。
- [Birdwatching Zone](https://birdwatching.zone/) - 专门用于观鸟的网站，记录了超过300种鸟类，为爱好者提供识别技巧和资源。
- [OneZoom](https://www.onezoom.org/) - 生命之树，展示地球上所有生命如何连接。探索220万个物种之间的关系，查看超过10万张图像，了解物种如何从共同祖先进化而来。通过树状结构缩放，发现地球上生命的多样性。

## Physics

- [The Theoretical Minimum](https://theoreticalminimum.com/home) - 由世界知名物理学家伦纳德·苏斯金德教授的斯坦福大学继续教育课程系列。
- [Theoretical Physics - Cambridge](https://www.damtp.cam.ac.uk/user/tong/index.html) - 来自剑桥大学的理论物理资源和信息。
- [University of Virginia Classical and Modern Physics II](https://galileo.phys.virginia.edu/classes/632.ral5q.summer06/lectures.html) - 弗吉尼亚大学经典与现代物理学II课程的讲义和材料。
- [Mueller Group - Cornell](https://muellergroup.lassp.cornell.edu/index.html) - 康奈尔大学原子和固体物理实验室。
- [Ptable](https://ptable.com/?lang=en) - 提供元素及其性质信息的交互式可定制周期表。
- [National Institute of Standards and Technology – Fundamental Physical Constants](https://www.nist.gov/pml/fundamental-physical-constants) - 提供权威的物理常数数据，为研究和技术应用提供基本测量数据。
- [Mechanics Map](https://mechanicsmap.psu.edu/index.html) - 来自宾夕法尼亚州立大学的交互式资源，用于探索机械系统和理解基本力学原理。

### Quantum

- <https://vsg.quasihome.com/interfer.htm> - Applet of Young's Double Slit Interference
- [Webb Telescope](https://webbtelescope.org/) - 詹姆斯·韦伯太空望远镜的官方网站。

#### Quantum Games

- [Virtual Lab by Quantum Flytrap](https://quantumflytrap.com/lab) - 模拟多粒子量子系统的光学实验，支持量子密钥分发和纠缠探索。
- [Hello Quantum](https://quantum-computing.ibm.com/lab) - 通过交互式益智游戏介绍量子电路和门，具有简化量子概念的图形界面。
- [Particle in a Box](https://learnqm.gatech.edu) - 通过对比经典物理和量子物理的2D单人平台游戏演示量子叠加和能级。
- [Psi and Delta](https://learnqm.gatech.edu) - 通过专注于叠加和量子概率的合作游戏鼓励量子力学的协作学习。
- [QPlayLearn](https://qplaylearn.com) - 提供交互式工具、视频和游戏进行多层次量子物理教育，满足不同学习者的需求。
- [Quantum Odyssey](https://quarksinteractive.com) - 通过游戏化界面可视化量子算法开发，辅助量子计算逻辑和状态演化教育。
- [Quantum Moves 2](https://www.scienceathome.org/games/quantum-moves-2) - 让公民科学家参与优化量子实验，解决量子优化中的现实挑战。
- [The Virtual Quantum Optics Laboratory](https://www.vqol.org/) - 支持量子光学实验的设计和仿真，为教育目的连接经典力学和量子力学。
- [Arxiv Paper - Quantum Games and Interactive Tools for Quantum Technologies Outreach and Education](https://arxiv.org/abs/2202.07756) - 关于使用游戏和交互工具让公众理解量子技术的全面讨论。

### Astronomy

- [Fear of Physics](https://www.fearofphysics.com/) - 提供免费的"天文学101"课程。
- [Galileo's Applets](https://galileo.phys.virginia.edu/classes/109N/more_stuff/Applets/home.html) - 提供关于早期月球观测和各种运动相关主题的小程序。
- [100,000 Stars](https://stars.chromeexperiments.com/) - 展示十万颗附近恒星的可视化，提供沉浸式体验。
- [The Million Earth Solar System](https://planetplanet.net/2018/06/01/the-million-earth-solar-system/) - 探索拥有一百万颗类地行星的太阳系概念。
- [Space Telescope Live](https://spacetelescopelive.org/webb) - 实时访问詹姆斯·韦伯太空望远镜的数据和观测结果，让用户探索太空研究和发现。
- [Telescope Optics](https://www.telescope-optics.net/) - 为业余望远镜制造者提供的资源，详细介绍望远镜光学系统的设计和构造。
- [Orbital Basics](https://t-neumann.github.io/space/OrbitalBasics/) - 解释轨道力学基本概念的教育资源，专为太空科学初学者设计。

## Mathematics

- [Open Logic Text](https://openlogicproject.org/about/) - 开源、模块化、协作编写的形式（元）逻辑和形式方法教学材料集合。
- [OEIS - The On-Line Encyclopedia of Integer Sequences](https://oeis.org/) - 提供综合整数序列数据库的平台。
- [First Principles of Mathematics](https://pdodds.w3.uvm.edu/) - 涵盖各种数学主题第一原理的资源。
- [38 Best Math Websites for Students](https://blog.symbaloo.com/webmixes/11/best-math-websites) - 为学生推荐的38个数学网站汇编。
- [Math Hints](https://mathhints.com/) - 为各种数学主题提供简单解释和示例的免费网站。
- [Better Explained](https://betterexplained.com/) - 专注于理解数学概念而非死记硬背的平台，提供虚数和指数等主题的清晰课程。
- [Mathway](https://www.mathway.com/) - 免费数学问题求解器。
- [ChiliMath](https://www.chilimath.com/) - 数学主题的在线资源。
- [Visual and Interactive Introduction to Complex Analysis](https://complex-analysis.com/) - 提供复分析可视化和交互式介绍的资源。
- [Matrices as Tensor Network Diagrams](https://www.math3ma.com/blog/matrices-as-tensor-network-diagrams) - 讨论矩阵表示为张量网络图的文章。
- [What is Category Theory Anyway?](https://www.math3ma.com/blog/what-is-category-theory-anyway) - 用范畴论探索数学事物的宏观体系。
- [Algebra Practice Problems](https://www.algebrapracticeproblems.com/) - 提供代数练习题和清晰解释的平台。
- [Matrixology](https://pdodds.w3.uvm.edu/teaching/courses/2016-08UVM-122/) - 矩阵学课程资源。
- [Math Courses at NITK](https://sam.nitk.ac.in/courses-taught.html) - 卡纳塔克邦国家理工学院（NITK）数学课程列表。
- [Easy Mathematical Tricks from Counting Through Calculus](https://mathhints.com/) - 从计数到微积分的简易数学技巧集合。
- [Math Salamanders](https://www.math-salamanders.com/) - 为儿童或学生提供有用计算器（如反向百分比计算器）和数学工作表的平台。
- [3Blue1Brown Non-Videos](https://some.3b1b.co/non-videos) - 3Blue1Brown的SoME非视频内容获奖作品。
- [Mathigon – The Mathematical Playground](https://mathigon.org/) - 提供教育内容和资源的交互式数学平台。
- [Math Warehouse](https://www.mathwarehouse.com/) - 提供交互式数学活动、演示、定义和示例课程、工作表和其他资源。
- [Cut the Knot](https://www.cut-the-knot.org/) - 提供交互式数学谜题、问题和可视化的资源，通过解决问题来探索数学概念。
- [Trigonography](https://trigonography.com/?page_id=230) - 探索三角学及其应用的网站，具有视觉辅助工具、公式和解题技巧。
- [Lamar Math Tutorials](https://tutorial.math.lamar.edu/) - 综合的数学教程和练习题集合，涵盖从代数到微积分和微分方程的主题。
- [Art of Problem Solving](https://artofproblemsolving.com/company) - 专注于培养各学科问题解决技能，包括数学、物理、编程和语言艺术。提供严格的在线课程、实体学院和旨在鼓励学生批判性思维、实验和坚持的引人入胜的课程。
- [Mathematics Genealogy Project](https://genealogy.math.ndsu.nodak.edu/index.php) - 旨在汇编和分享全球数学家的综合信息，从学术机构和个人贡献者收集数据。
- [Enjeck Complicated Math Equation Generator](https://enjeck.com/num2math/?input=4&submit=Generate) - 根据用户输入生成复杂数学方程，提供练习或可视化数学问题的工具。
- [Erdos Problems](https://www.erdosproblems.com/) - 专门探索与保罗·埃尔德什相关的开放数学问题的网站，包括数论和组合学中的持续挑战。
- [The Electronic Journal of Combinatorics](https://www.combinatorics.org/ojs/index.php/eljc) - 发表组合学研究论文和文章的开放获取期刊，组合学是处理计数、排列和结构的数学领域。
- [Zeta by Amir Hirsch](https://amirhirsch.com/zeta/index.html) - 致力于探索黎曼假设的网站，为对数论感兴趣的人提供交互式工具和资源。
- [Tungsteno](https://www.tungsteno.io/) - 提供教学工具的平台，让每个人都能接触数学，完全免费，基于开放协作。
- [PlanetMath](https://planetmath.org/) - 在线数学资源和协作平台，提供开放获取的数学内容，包括定义、定理和证明。
- [Geomstats Tutorials](https://geomstats.github.io/tutorials/index.html) - 几何统计学教程和资源，提供在流形上实现统计技术的实际示例和代码。
- [Ximera - MOOCulus](https://ximera.osu.edu/mooculus) - 为在线课程设计的交互式数学模块和学习资源集合，专注于微积分和相关学科。

### Math + Art

- [Paul Nylander's website](https://bugman123.com/) - 保罗·尼兰德最喜欢的爱好和兴趣，特别是科学和艺术
- [NYC DOE CS4All](https://nycdoe-cs4all.github.io/) - 使用p5.js进行计算媒体介绍
- [Texample](https://texample.net/) - LaTeX示例和社区
- [Snowflakes Project](https://www.dynamicmath.xyz/collective-math-art/) - 集体数学艺术的雪花项目

## Engineering

- [Animagraffs](https://animagraffs.com/) - 以详细动画信息图表（animagraffs）为特色的教育网站，直观地解释各学科的复杂主题和过程。
- [Technology Student](https://technologystudent.com/index.htm) - 包含大量信息表、练习和动画的资源，涵盖各种技术相关主题。
- [Nuclear Power](https://www.nuclear-power.com/) - 旨在学习核能和反应堆物理基础的非营利项目，涵盖核电站、反应堆物理、热工程、材料和辐射防护等主题。
- [Knovel](https://app.knovel.com/kn) - 为各种工程和科学目的提供工具和计算器的平台。
- [Engineering Library](https://engineeringlibrary.org/reference/) - 工程主题参考图书馆。
- [Engineering Toolbox](https://www.engineeringtoolbox.com/index.html) - 提供工程工具和信息的在线资源。
- [Text to CAD](https://text-to-cad.zoo.dev/) - 将文字描述转换为CAD模型的工具，帮助用户从书面输入生成技术图纸。
- [McMaster-Carr](https://www.mcmaster.com/) - 硬件、工具、原材料、工业材料和维护设备供应商，通过广泛的产品目录为各行业提供服务。
- [Engineer on a Disk: Modeling](https://engineeronadisk.com/book_modeling/modelTOC.html) - 为工程师和开发人员提供系统建模和仿真实践指南的书籍，包含实用示例和解释。
- [Bartosz Ciechanowski Archives](https://ciechanow.ski/archives/) - 巴托什·齐哈诺夫斯基关于设计和技术的文章集合。

### Civil Engineering

- [Floor Plan Lab](https://floorplanlab.com/) - 创建和可视化平面图的平台。
- [Engineers Edge](https://www.engineersedge.com/) - 提供结构和机械工程主题文章和资源的平台。
- [3D House Planner](https://3dhouseplanner.com/) - 网络上的免费3D平面图规划应用程序。

### Mechanical Engineering

- [507 Movements](https://507movements.com/toc.html) - 以动画机械运动为特色的网站，提供对各种机械系统的视觉理解。
- [MadeHow](https://www.madehow.com/) - 解释和详述各种产品制造过程的资源。
- [Comprehensive Structural Analysis Book](https://temple.manifoldapp.org/projects/structural-analysis) - 提供结构分析综合书籍的在线资源。
- [Awesome MechEng](https://github.com/m2n037/awesome-mecheng#heat-transfer) - 优秀的机械工程资源。
- [Animated Dynamics](https://dynref.engr.illinois.edu/ref.html) - 可视化动力学仿真的交互式参考资料，旨在帮助用户更好地理解复杂的机械系统。
- [Wikimedia Commons - Engine Animations](https://commons.wikimedia.org/wiki/Category:Animations_of_engines) - 演示各种类型发动机（从燃烧到电动）功能的动画图像和视频集合。
- [Mechanisms/menu-gear](https://www.mekanizmalar.com/menu-gear.html) - 齿轮机构动画。
- [Mechanism Animations](https://people.ohio.edu/williams/html/MechanismAnimations.html) - 提供演示机械系统和机构的交互式动画，帮助用户可视化和理解其运动和功能。
- [CalcResource](https://calcresource.com/resources.html) - 定期更新的专注于力学和静力学的资源列表，有助于学习和理解这些学科。
- [Engineering Toolbox](https://www.engineeringtoolbox.com/) - 涵盖广泛工程主题的综合资源。
- [StructX](https://structx.com/) - 为专业人士和学生提供结构工程资源的网站。
- [Amesweb](https://www.amesweb.info/) - 包含计算器和涵盖各种工程主题文章的平台。
- [RoyMech](https://www.roymech.co.uk/) - 提供结构和机械工程主题深度文章的参考网站。
- [Arcelor-Mittal Design Software](https://sections.arcelormittal.com/design_aid/design_software/EN) - 阿赛洛-米塔尔提供的钢结构免费设计软件。
- [BU Moss](https://www.bu.edu/moss/) - 专注于理解细长结构的课程和资源。
- [Homestyler](https://www.homestyler.com/) - 面向专业人士和业余爱好者的简单省时的在线室内设计工具。
- [How a Car Works](https://www.howacarworks.com/) - 解释汽车工作原理的完整免费指南。
- [IIHS Ratings](https://www.iihs.org/ratings) - 来自公路安全保险协会的碰撞测试评级和安全信息。
- [Euro NCAP](https://www.euroncap.com/en/) - 为车辆提供安全评级的欧洲新车评估计划。
- [Toaster Museum](http://toastermuseum.com/) - 专门收藏古董烤面包机的展示，为烤面包机爱好者和收藏家展示复古型号，同时提供历史信息和保存细节。

#### Materials / Nanotechnology

- [DoITPoMS](https://www.doitpoms.ac.uk/index.php) - 来自剑桥大学材料科学与冶金系的DoITPoMS
- [Nanoscience Resources](https://ssd.phys.strath.ac.uk/) - 斯特拉斯克莱德大学物理系的纳米科学资源
- [StatNano](https://product.statnano.com/) - 关于纳米技术产品信息的可靠来源，目前广泛应用于各种工业领域
- [MyMiniFactory](https://www.myminifactory.com/) - 世界上最大的免费下载具有文化意义的3D可打印物体生态系统
- [Roy Mech](https://roymech.org/) - 提供与机械工程和工程材料相关的有用信息、表格、时间表和公式的网站。

### Electronics Engineering

- [Electrical4U](https://www.electrical4u.com/) - 学习电气工程的平台。
- [Electronics Tutorials](https://www.electronics-tutorials.ws/) - 提供电子学各个方面教程的资源。
- [Octopart](https://octopart.com/) - 电子元件平台，帮助用户查找和比较组件。
- [TechSpot - How CPUs Are Designed and Built](https://www.techspot.com/article/1821-how-cpus-are-designed-and-built/) - 涵盖计算机架构、处理器电路设计、超大规模集成电路、芯片制造和计算未来趋势的系列文章。
- [Open Circuits Book](https://www.opencircuitsbook.com/) - "开放电路"是对日常电子产品内部精美设计的摄影探索。
- [Digi-Key Electronics](https://www.digikey.com/) - 电子元件在线市场，为工程师和制造商提供大量零件、工具和资源选择。
- [Telematic Connections Timeline](http://telematic.walkerart.org/timeline/index.html) - 探索远程信息处理和网络通信历史与影响的交互式时间轴，专注于艺术和技术。
- [Cybergraph](https://cybergraph.dubberly.com/#) - 控制论的视觉探索，提供网络的交互式表示。

## Computer Science

- [Webopedia](https://www.webopedia.com/) - 计算机和IT术语的在线技术词典、学习指南和评论。
- [Teach Yourself Computer Science](https://teachyourselfcs.com/) - 计算机科学自学的综合指南。
- [Open Source Society University - Computer Science](https://github.com/open-source-society/computer-science) - 开源社会大学提供的计算机科学学习课程。
- [Functional Computer Science Curriculum](https://functionalcs.github.io/curriculum/) - 专注于计算机科学中函数式编程概念的课程。
- [Menimagerie](https://www.menimagerie.com/) - 探索理论计算机科学概念，从数系到康托无穷、哥德尔定理和自动机。
- [Computer Science Library](https://www.compscilib.com/) - 通过自动化分步解决方案和练习题帮助掌握计算机科学和数学课程概念的平台。
- [Computer Jargon](https://www.computerhope.com/jargon.htm) - 计算机相关术语和技术术语词汇表。
- [Talks by Alan Kay](https://tinlizzie.org/IA/index.php/Talks_by_Alan_Kay) - 计算机科学家艾伦·凯演讲集。
- [Everything Computer Science](https://everythingcomputerscience.com/) - 涵盖计算机科学主题的广泛文章和教程，包括编程、算法、数据结构和软件开发实践。
- [Richard Sutton Video Lectures](https://videolectures.net/search/?query=Richard%20sutton) - 理查德·萨顿视频讲座和演讲集，他是强化学习和人工智能领域的著名研究者。

### Data Structures and Algorithms (DS&A)

- [Dictionary of Algorithms and Data Structures](https://xlinux.nist.gov/dads/) - NIST算法和数据结构词典。
- [Visualgo](https://visualgo.net/en) - 通过动画可视化数据结构和算法的平台。
- [Adventures In Coding & Algorithms](https://entcheva.github.io/) - 探索编程和算法的博客。
- [Damn Cool Algorithms](https://blog.notdot.net/tag/damn-cool-algorithms) - 专门介绍特别有趣算法的博客。
- [Skiena's Algorithms Lectures](https://www3.cs.stonybrook.edu/~algorith/video-lectures/) - 史蒂文·斯基纳的算法视频讲座。
- [Visual Algorithms](https://thimbleby.gitlab.io/algorithm-wiki-site/) - 呈现算法视觉表示的平台。
- [Sinon - Algorithms](https://sinon.org/algorithms/) - 涵盖计算机科学重要成果的总结页面。

### Big-O notation

- [Algorithms and Data Structures Big-O Notation](https://cooervo.github.io/Algorithms-DataStructures-BigONotation/) - 涵盖各种算法和数据结构大O表示法的综合资源。
- [Big-O Cheat Sheet](https://www.bigocheatsheet.com/) - 展示计算机科学中常用算法空间和时间复杂度（大O）的网页。
- [Comp160 Data Cheat](https://www.clear.rice.edu/comp160/data_cheat.html) - 提供计算机科学中数据结构和相关概念信息的资源。

## AI/ML

- [Doublespeak Chat](https://doublespeak.chat/#/handbook) - 关于大语言模型黑客攻击的经验性、非学术性和实用指南。
- [Fast.ai Course](https://course.fast.ai/) - 深度学习和机器学习在线课程。
- [Papers with Code](https://paperswithcode.com/sota) - 机器学习任务最先进结果及相关代码的集合。
- [Delta Academy Maps](https://maps.joindeltaacademy.com/) - 机器学习数学地图。
- [Inside the Matrix by PyTorch](https://pytorch.org/blog/inside-the-matrix/) - PyTorch的博客文章，可视化矩阵乘法、注意力机制等内容。
- [Directory of AI Models](https://docs.google.com/spreadsheets/d/1gc6yse74XCwBx028HV_cvdxwXkmXejVjkO-Mz2uwE0k/edit?pli=1#gid=0) - 提供各种AI模型目录的Google表格文档，包含名称、日期、参数、组织等信息。
- [Papers with Code](https://paperswithcode.com/) - 提供最新机器学习研究论文及代码实现和基准测试的平台。
- [Globe Engineer Explorer](https://explorer.globe.engineer/) - 应用AI公司，制作为人类理解优化信息表示的产品。
- [Colah's Blog](https://colah.github.io/) - 克里斯托弗·奥拉关于深度学习和人工智能的博客，包含详细解释、教程和研究见解。
- [TensorFlow Playground](https://playground.tensorflow.org) - 用于实验神经网络的交互式工具，让用户可视化不同配置对模型训练和分类任务的影响。
- [Ben's Bites](https://bensbites.com/catalog) - 提供各种一口大小AI资源的目录。

### Robotics

- [Robots Guide](https://robotsguide.com/) - 为机器人学初学者和爱好者提供指南、评论和见解的综合资源。
- [Control Challenges](https://janismac.github.io/ControlChallenges/) - 机器人学的LeetCode。

### LLMs

- [Hacker Llama](https://osanseviero.github.io/hackerllama/blog/posts/hitchhiker_guide/) - 关于加入本地大语言模型社区时需要了解的有用术语的博客文章。
- [Moebio Mind](https://moebio.com/mind/) - 探索语言模型内部工作原理，可视化GPT-3 API生成的语义空间和词汇补全轨迹。
- [LLM Visualization](https://bbycroft.net/llm) - 用于可视化大语言模型并探索其结构、行为和输出的交互式工具。
- [RR LM Game](https://rr-lm-game.herokuapp.com/) - 基于浏览器的语言建模游戏。

#### Prompt Engineering

- [PromptPerfect](https://promptperfect.jina.ai/) - 为大语言模型、大型模型和LMOps设计的尖端提示优化器。
- [Jailbreak Chat](https://www.jailbreakchat.com/) - 大语言模型越狱提示集合。
- [MidJourney Styles and Keywords Reference](https://github.com/willwulfken/MidJourney-Styles-and-Keywords-Reference) - AI图像生成的样式和关键词参考。
- [QuickRef ChatGPT](https://quickref.me/chatgpt) - ChatGPT速查表。
- [OpenAI Cookbook](https://cookbook.openai.com/) - OpenAI提供的AI工作实用指南手册。
- [Prompting Guide](https://www.promptingguide.ai/) - 为AI模型创建有效提示的资源。
- [Instaprompt](https://www.instaprompt.ai/?ref=producthunt) - 提供即时写作提示的平台。
- [Midjourney Prompt Helper](https://promptfolder.com/midjourney-prompt-helper/) - 帮助用户为Midjourney生成详细有效提示的工具。
- [Free Midjourney Prompt](https://www.freemidjourneyprompt.com/) - 提供大量免费Midjourney提示，让用户轻松通过优化的自然语言提示生成图像。
- [Prompt Engineering Guide](https://learnprompting.org/docs/introduction) - 提示工程综合指南，提供为AI模型和创意任务制作有效提示的基本原则和技术。

### AI tools

- [Future Tools](https://www.futuretools.io/) - 寻找符合您需求的精确AI工具的平台。
- [WarpSound AI](https://www.warpsound.ai/) - 仅用提示在几秒钟内创建高质量生成式AI音乐。
- [AI Tools Arena](https://aitoolsarena.com/) - 展示各种AI工具和资源的平台。
- [Klavier AI](https://klavier.ai/) - 允许您在选择的网页和文档上与ChatGPT进行问答。
- [Aiva Valley](https://aivalley.ai/) - 最新的AI工具和提示来源。
- [Bardeen AI](https://www.bardeen.ai/) - 几分钟内无需代码即可自动化手动工作和任务的AI工具。
- [Speechify](https://speechify.com/) - 通过领先的AI文本转语音阅读器聆听文档、文章、PDF、邮件等任何内容。
- [Humata AI](https://www.humata.ai/) - 上传任何PDF或文档，几秒钟内获得答案。
- [Syntelly](https://app.syntelly.com/search) - 用于有机化学和医学化学的人工智能。
- [Warp.dev - Warp AI](https://www.warp.dev/warp-ai?source=producthunt&ref=producthunt) - 无需编写代码即可构建API的AI驱动工具。
- [Feedback by AI](https://feedbackbyai.com/?ref=producthunt) - 利用AI为写作生成可操作反馈的平台。
- [Seeing the World through Your Eyes](https://world-from-eyes.github.io/) - 重建通过人类眼睛反映的世界。
- [Auxiliary Tools](https://www.auxiliary.tools/) - 为人类提供AI实验和工具的平台。
- [Hemingway Editor](https://hemingwayapp.com/) - 针对文体风格的拼写检查器。
- [FuturePedia](https://www.futurepedia.io/) - 领先的AI资源平台，致力于帮助各行业专业人士利用AI技术进行创新和发展。
- [ExplainPaper](https://www.explainpaper.com/) - 用户可以上传研究论文、高亮困惑文本并获得解释的平台，使研究论文更易理解。
- [Summarize Tech](https://www.summarize.tech/) - AI驱动的工具，可获取任何长YouTube视频的摘要，如讲座、直播活动或政府会议。
- [YouTubeTranscript](https://youtubetranscript.com/) - 获取YouTube视频转录和摘要。

### Data Science

- [DataTau](https://www.datatau.com/news) - 专注于数据的Hackernews
- [DatasetList](https://www.datasetlist.com/) - 来自网络各处的机器学习数据集列表
- [Stat Learning](https://www.statlearning.com/) - 学习统计学和相关主题的平台。

### Databases

- [ImageNet](https://www.image-net.org/) - 根据WordNet层次结构组织的图像数据库。
- [DataSheets.com](https://www.datasheets.com/) - 可搜索的电子元件数据表和采购信息数据库，专为设计工程师和电子采购代理设计。
- [Academic Torrents](https://academictorrents.com/) - 社区驱动的大型数据集共享平台，为学术研究提供种子下载，包括科学论文、数据集和多媒体。
- [DBLP](https://dblp.org/) - 提供主要计算机科学期刊、会议和论文集书目信息的综合开放式数据库。

## Web Development

- [Mozilla Developer Network](https://developer.mozilla.org/en-US/) - Web技术文档，包括CSS、HTML和JavaScript。
- [W3C](https://www.w3.org/) - 万维网联盟官方网站，使Web技术能够与不同语言、脚本和文化一起使用。
- [UX Core](https://keepsimple.io/uxcore) - UX Core允许您在创建软件时探索许多认知偏见。
- [Coggle](https://coggle.it/diagram/Vz9LvW8byvN0I38x/t/web-development) - 用于可视化和组织Web开发相关想法的协作思维导图工具。
- [Web.dev Learn](https://web.dev/learn) - Google提供的Web开发资源和教程教育平台。
- [WebDevHome](https://webdevhome.github.io/) - Web开发资源和教程集合。
- [Web Dev Resources](https://web-dev-resources.com/#/) - 为开发者精选的出色Web开发资源列表。
- [BBC Microbit Editor](https://bbcmic.ro/) - BBC提供的Microbit编程基础编辑器。
- [DemoFox](https://blog.demofox.org/) - 包含与编程和开发相关的各种链接的博客。
- [Timeline of Web Browsers](https://super-static-assets.s3.amazonaws.com/bc2689f0-a124-4777-93dc-416ee1aa4858/images/7db6532c-14f1-4c51-a337-b3021a7293bf.svg) - 2019年前Web浏览器时间轴的可视化。
- [Is Houdini Ready Yet?](https://ishoudinireadyyet.com/) - 检查Web浏览器中各种Houdini规范就绪状态的工具。
- [Artillery](https://www.artillery.io/) - 用于SRE（站点可靠性工程）和DevOps的现代负载测试和冒烟测试工具。
- [Rentry](https://rentry.org/) - 具有预览、自定义URL和编辑功能的Markdown粘贴板服务，提供快速、简单和免费使用，条目永久保存。
- [Intab Resources](https://intab.io/resources/) - 精选的2021年Web开发工具。
- [Chrome Extension Kit](https://chromeextensionkit.com/) ($) - 包含17个经过实战检验的Chrome扩展构建入门模板的工具包，节省设置时间并专注于交付。
- [Esoteric Codes](https://esoteric.codes/) - 探索深奥编程语言、基于约束的编码、代码艺术、代码诗歌等的平台。
- [FreeFormatter](https://freeformatter.com/) - 为开发者提供的免费在线工具，包括格式化器、验证器、代码压缩器、字符串转义器、编码器/解码器、消息摘要器等。
- [dbdesigner.net](https://www.dbdesigner.net/) - 免费的在线数据库模式设计和建模工具。
- [Jvns blog](https://jvns.ca/blog/2022/04/12/a-list-of-new-ish--command-line-tools/>) - 新命令行工具列表
- [VisBug](https://visbug.web.app/) - 用于Web开发的开源浏览器设计工具。
- [Web Design Repo](https://webdesignrepo.com/) - 为设计师和开发者提供的免费Web设计资源库。
- [Devopedia](https://devopedia.org/) - 涵盖技术和软件开发各种主题的知识中心。
- [MadeWithBubble](https://www.madewithbubble.xyz/) - 发现用Bubble（可视化Web开发平台）创建的有趣应用程序和网站的平台。
- [Interneting is Hard](https://www.internetingishard.com/) - 为完全初学者设计的友好Web开发教程。
- [Airtable Toolkit](https://www.airtable.com/home/<USER>
- [Grey Software Resources](https://resources.grey.software/) - 由全球专业人士和学者策划和众包的最新软件资源。
- [Free-for.dev](https://free-for.dev/#/) - 为开发者提供免费套餐的软件（SaaS、PaaS、IaaS等）和其他服务列表。
- [Free Privacy Policy Generator](https://www.freeprivacypolicy.com/) - 生成免费隐私政策的工具，确保符合CCPA、GDPR、CalOPPA、Google Analytics和AdSense等要求。
- [Mailinator](https://www.mailinator.com/) ($) - 允许开发者和QA测试团队测试电子邮件和短信工作流程的平台，包括2FA验证、注册和密码重置。
- [Atlaq](https://atlaq.com/) - 域名生成器。
- [Addy's Toolkit](https://toolkit.addy.codes/) - 为Web设计师和开发者精选的806个工具和资源集合。
- [BrowserBench - Speedometer 3.0](https://www.browserbench.org/Speedometer3.0/) - 测量Web浏览器性能的基准测试工具，专门测试现代Web应用程序的响应性和速度。
- [UserAgents.me](https://www.useragents.me/) - 提供在Web上所有设备类型、操作系统和浏览器中最新和最常见用户代理的自更新列表。数据始终新鲜，每周更新。此用户代理列表非常适合希望融入的网络爬虫、开发者、网站管理员和研究人员。

### Front-end

- [SafeRules](https://anthonyhobday.com/sideprojects/saferules/) - 每次都可以安全遵循的视觉设计规则。
- [Can I Email](https://www.caniemail.com/) - 检查HTML和CSS功能的电子邮件客户端兼容性。
- [Egg Gradients](https://www.eggradients.com/) - 渐变背景颜色集合。
- [Frontend Checklist](https://frontendchecklist.io/) - 在将网站/HTML页面发布到生产环境之前需要拥有/测试的所有元素的详尽列表。
- [Frontend Mentor](https://www.frontendmentor.io/) - 在处理专业设计时解决现实世界HTML、CSS和JavaScript挑战的平台。
- [CodePen](https://codepen.io/) - 构建、测试和发现前端代码的平台。探索[主题](https://codepen.io/topics/)。
- [Electron](https://www.electronjs.org/) - 使用JavaScript、HTML和CSS构建跨平台桌面应用程序的框架。
- [Homepage Gallery](https://homepage.gallery/) - 展示500多个网站以获得Web设计灵感的画廊。
- [HTML Dog](https://htmldog.com/) - HTML、CSS和JavaScript的综合资源。
- [Learn.shayhowe](https://learn.shayhowe.com/) - 学习编写HTML和CSS代码。
- [This vs That](https://thisthat.dev/) - 探索前端开发概念之间的差异。
- [Know-It-All](https://know-it-all.io/) - 列出您在Web开发方面知道和不知道的内容。
- [You Might Not Need jQuery](https://youmightnotneedjquery.com/) - jQuery替代方案。

#### HTML

- [Hyperscript](https://hyperscript.org/) - 使用普通标记编写的网站变得令人愉快。
- [htmx](https://htmx.org/) - htmx通过属性直接在HTML中为您提供AJAX、CSS过渡、WebSockets和服务器发送事件的访问权限，因此您可以利用超文本的简洁性和强大功能构建现代用户界面。

#### CSS

- [CSS Solid](https://www.csssolid.com/) - CSS参考、教程和文章。
- [CSS-Tricks](https://css-tricks.com/) - 由Digital Ocean支持的Web设计社区。
- [CSS Box Shadow Code Snippet](https://onaircode.com/css-box-shadow-code-snippet/) - 创建CSS盒子阴影的代码片段。

#### JavaScript

- [The Coding Cards](https://thecodingcards.com/) ($) - JavaScript和数据结构抽认卡。包含语法和示例的基本编程概念。
- [StandardJS](https://standardjs.com/) - JavaScript风格指南、代码检查器和格式化程序。
- [Modern JavaScript Cheatsheet](https://mbeaudru.github.io/modern-js-cheatsheet/) - 现代项目中经常遇到的JavaScript知识速查表。
- [JSONPlaceholder](https://jsonplaceholder.typicode.com/) - 用于测试和原型设计的免费虚假API。
- [Vue.js Cheat Sheet](https://marozed.com/vue-cheatsheet/) - Vue.js速查表。
- [JS Bin](https://jsbin.com/) - 开源协作Web开发调试工具。
- [JavaScript Event Keycode Info](https://www.toptal.com/developers/keycode) - JavaScript事件键码信息。
- [jsDelivr](https://www.jsdelivr.com/) - 开源免费CDN。快速、可靠且自动化。

### Back-End

- [RunSidekick](https://www.runsidekick.com/) - 无需停止和重新部署应用程序即可按需收集跟踪和生成日志。
- [Vantage Instances](https://instances.vantage.sh/) - 比较亚马逊的实例类型、定价和其他页面。

#### APIs

- [Public APIs Directory](https://publicapis.dev/) - 发现公共API
- [Public APIs on GitHub](https://github.com/public-apis/public-apis) - 供软件和Web开发使用的免费API集合列表
- [REST API Tutorial](https://www.restapitutorial.com/) - 学习REST
- [Spotify API Documentation](https://developer.spotify.com/documentation/web-api) - Spotify的API文档
- [Stripe API Documentation](https://stripe.com/docs/api) - Stripe的API文档

#### SQL

- [SQLZoo](https://sqlzoo.net/wiki/SQL_Tutorial) - 分阶段学习SQL
- [Bipp SQL Tutorial](https://bipp.io/sql-tutorial) - 免费SQL教程
- [Select Star SQL](https://selectstarsql.com/) - 学习SQL的交互式书籍
- [Medium-Hard Data Analyst SQL Interview Questions](https://quip.com/2gwZArKuWk7W) - SQL面试题集合
- [SQL Translate](https://www.sqltranslate.app/) - SQL到自然语言和自然语言到SQL的翻译器。100%免费和开源
- [Servers for Hackers](https://serversforhackers.com/) - 程序员的服务器管理。学习开发和生产的服务器技术。

### Web Analytics

- [Pirsch](https://pirsch.io/) - 开源、无Cookie的网络分析平台。
- [Websites Milonic](https://websites.milonic.com/) - 为网站提供整洁报告的重要数据。
- [WooRank](https://www.woorank.com/) ($) - 网站评估和SEO检查器。
- [Semji](https://semji.com/) - 通过创建高性能SEO内容来提高内容投资回报率的平台。
- [Web.dev](https://web.dev/) - 允许您测量网站性能并提供可操作的见解。
- [Download Time Calculator](https://downloadtimecalculator.com/) - 根据您的传输速度估算下载任何文件所需的时间，而无需实际下载文件。
- [W3C Link Checker](https://validator.w3.org/checklink) - 检查网页或完整网站中的链接和锚点。
- [URLVoid](https://www.urlvoid.com/) - 网站声誉/安全检查器，帮助检测潜在恶意网站。
- [SimilarSites](https://www.similarsites.com/) - 帮助找到相似的网站并提供有关其统计信息的信息。
- [LocaBrowser](https://www.locabrowser.com/) - 实时测试您的网站在不同国家的外观。
- [StatusVista](https://statusvista.com/) - 您的产品所依赖系统的一体化状态页面。
- [BuiltWith](https://builtwith.com/) - 发现网站使用的技术，使用包含59,905+种网络技术和超过6.73亿个网站的数据库。
- [CamelCamelCamel](https://camelcamelcamel.com/) - 免费的亚马逊价格追踪器，监控数百万种产品并在价格下降时提醒您。
- [CloudPing](https://cloudping.bastionhost.org/en/) - 执行HTTP ping以测量从您的浏览器到世界各地各种云数据中心的网络延迟。
- [DownForEveryoneOrJustMe](https://downforeveryoneorjustme.com/) - 检查网站是否宕机。
- [DownDetector](https://downdetector.in/) - 实时问题和中断监控。
- [WebhookWizard](https://webhookwizard.com/) - 使用webhooks解锁数据。
- [Lighthouse](https://chrome.google.com/webstore/detail/lighthouse/blipmdconlkpinefehnmjammfjpmpbjk?hl=en) - 用于提高Web应用程序性能、质量和正确性的开源自动化工具。
- [Archive.md](https://archive.md/) - 网页的时间胶囊。
- [Hypothesis](https://web.hypothes.is/) - 覆盖整个网络的对话层，在任何地方都能工作，无需任何底层站点实施（开放社区项目）。

#### Testing

- [Fast.com](https://fast.com/) - 测量您的互联网速度、延迟和上传速度。
- [Pingdom Tools](https://tools.pingdom.com/) ($) - 允许您测试页面加载时间、分析并找到瓶颈。
- [GTmetrix](https://gtmetrix.com/) - 分析您的网站性能，识别缓慢的原因，并发现优化机会。
- [Loader.io](https://loader.io/) - 免费负载测试服务，用数千个并发连接对Web应用程序和API进行压力测试。
- [WebPageTest](https://www.webpagetest.org/) - 测量您网站的碳足迹并运行无代码实验以找到改进方法。
- [Azure Speed Test](https://www.azurespeed.com/Azure/Latency) - 测试Azure服务速度和延迟的工具。

### Web 3.0 Dev and Cryptocurrencies

- [Web3 is Going Great](https://web3isgoinggreat.com/) - 追踪区块链/加密货币/web3技术领域挑战的例子。时间轴涵盖了自2021年初以来加密货币和基于区块链技术的事件。
- [Blockchain Demo](https://andersbrownworth.com/blockchain/coinbase) - 区块链工作原理的演示。
- [DappRadar](https://dappradar.com/) - 在DeFi、NFT和游戏世界中发现、追踪和交易。
- [DeFi Pulse](https://www.defipulse.com/) - 去中心化金融（DeFi）项目及其统计数据的列表。
- [GlassChain](https://glasschain.org/home) - 总部位于瑞士的非营利组织，检查地址、交易、钱包或提供商，以追回因犯罪行为而被盗或丢失的比特币（或其中一部分）。比特币、莱特币、比特币现金和狗狗币地址被聚类到基于UTXO的区块链钱包中。提供实时、可靠且100%正确的数据。

## Software Engineering

- [NoviceDock](https://novicedock.com/) - 提供各种软件工程领域资源和解释的平台。
- [Morioh](https://morioh.com/) - 开发者社交网络，讨论bug和问题，分享知识并连接全球数百万程序员和开发者的才能。
- [Algorithmica - High-Performance Computing](https://en.algorithmica.org/hpc/) - Sergey Slotin的《现代硬件算法》。
- [ExBook](https://markm208.github.io/exbook/) - Elixir的动画介绍。
- [Ben Grosser Projects](https://bengrosser.com/projects/) - Ben Grosser的项目作品集。
- [Cybercademy Project Ideas](https://cybercademy.org/project-ideas/) - 探索网络安全项目想法的资源，为教育或实际网络安全倡议提供灵感。

### Android Development

- [Fossdroid](https://fossdroid.com/) - 发现和分享开源Android应用的平台。
- [Android Weekly](https://androidweekly.net/) - 与Android开发相关的精选新闻、文章和资源的每周通讯。
- [F-Droid Repository Search](https://apt.izzysoft.de/fdroid/index.php) - 允许您搜索F-Droid存储库中可用的应用程序，这是一个免费开源Android应用程序的集合。
- [Exodus Privacy](https://reports.exodus-privacy.eu.org/en/) - 帮助检查Android应用的权限和跟踪器，提供有关不同应用程序隐私方面的见解。
- [Don't Kill My App](https://dontkillmyapp.com/) - 倡导反对Android上激进的应用后台进程限制的网站，这可能会负面影响应用性能。
- [Mobile X Files](https://mobilexfiles.com/) - 智能手机的秘密代码和其他隐藏功能。
- [MobileRead Wiki](https://wiki.mobileread.com/wiki/Main_Page) - 电子阅读器、电子书和相关技术的综合资源，为各种移动阅读设备的用户提供指南、常见问题解答和教程。

### Game Development

- [itch.io](https://itch.io/) - 提供免费在线查找和分享独立游戏的简单方式的平台。
- [System Requirements Lab](https://www.systemrequirementslab.com/cyri) - 在几秒钟内分析您的计算机规格，提供您的系统是否满足各种游戏要求的信息。
- [Kenney](https://kenney.nl/) - 提供免费游戏资产、图形和游戏开发资源的平台。
- [Flash Games Archive](https://flasharch.com/en) - Flash游戏档案，保存这些经典游戏供未来享受。
- [OpenGameArt](https://opengameart.org/) - 为开发者和游戏创作者提供免费使用游戏资产的平台，包括2D和3D艺术、音效和音乐。
- [Nexus Mods](https://www.nexusmods.com/) - 最大的游戏修改在线社区之一，为流行视频游戏提供大量mod集合，以增强游戏体验、添加内容或个性化体验。

#### Game Theory

- [Ncase](https://ncase.me/) - 通过简单游戏解释博弈论。
- [Alberta Games Research](https://webdocs.cs.ualberta.ca/~games/) - 阿尔伯塔大学的网站，提供与博弈论、计算模型和作为计算研究的游戏相关的资源和研究。
- [Combinatorics.org](https://www.combinatorics.org/) - A. S. Fraenkel的组合博弈论文献目录。
- [Geometry and Graph Theory](https://ics.uci.edu/~eppstein/cgt/) - 组合博弈论资源，包含解释、示例和组合问题算法研究。

#### Pokemon

- [Pokemon Database](https://pokemondb.net/) - 包含新闻和更新的综合宝可梦数据库。
- [Serebii](https://serebii.net/) - 提供有关宝可梦的各种新闻、功能、档案和解释。

#### Chess

- [The Chess Website](https://www.thechesswebsite.com/) - 学习、练习和下国际象棋。
- [Lichess](https://en.lichess.org/) - 由志愿者和捐赠支持的免费/自由开源国际象棋服务器。
- [Chess.com](https://www.chess.com/) - 在线国际象棋平台。

### Embeddings

- [FPGA4Fun](https://www.fpga4fun.com/) - 专注于FPGA（现场可编程门阵列）技术的教育资源和教程，为初学者和高级用户提供实用指南。
- [Wokwi](https://wokwi.com/) - 在线ESP32、STM32、Arduino模拟器。
- [x86 Instruction Set Reference](https://c9x.me/x86/) - x86指令集"Into the Void"参考的镜像，为x86架构提供汇编语言和处理器指令的详细指南。
- [Analog Devices Wiki](https://wiki.analog.com/) - 包含模拟和混合信号设备技术文档、教程和资源的知识库，面向工程师和开发人员。
- [IEEE-754 Floating Point Converter](https://www.h-schmidt.net/FloatConverter/IEEE754.html) - 基于IEEE-754标准，在数字的十进制表示和现代CPU使用的二进制格式之间转换的工具。
- [Guide to x86 Assembly](https://www.cs.virginia.edu/~evans/cs216/guides/x86.html) - 弗吉尼亚大学计算机科学的x86汇编指南

### Linux

- [Linux Journey](https://linuxjourney.com/) - 学习Linux的平台。
- [Run Linux in Your Browser](https://bellard.org/jslinux/) - 在浏览器中运行Linux或其他操作系统。
- [OS Directory](https://os.directory/) - 在Web浏览器中模拟Linux发行版的平台。
- [DistroWatch](https://distrowatch.com/) - Linux和BSD发行版资源，提供新闻、评论和比较，帮助用户找到并安装合适的操作系统。
- [SS64](https://ss64.com/) - 包含最常见计算命令语法和示例的参考指南，涵盖数据库和操作系统。
- [SS64 Bash Keyboard Shortcuts](https://ss64.com/bash/syntax-keyboard.html) - bash键盘快捷键参考指南。
- [Command Line for Beginners](https://ubuntu.com/tutorials/command-line-for-beginners#1-overview) - 面向初学者的Linux命令行概述。
- [Linux Journey](https://linuxjourney.com/) - 提供免费、交互式Linux课程的教育网站，涵盖从基本命令到高级系统管理的所有内容。
- [ExplainShell](https://explainshell.com/) - 解释Linux shell命令的工具，提供命令语法和功能的详细分解。
- [LibreHunt](https://librehunt.org/) - 在您的Linux发行版（以及潜在的自由软件）搜索中为您提供帮助。回答简单问题，根据这些回答获得满足您需求的Linux发行版推荐。

### Vim

- [Vim Cheat Sheet](https://vim.rtorr.com/) - Vim速查表。
- [Learn Vim](https://learnvim.irian.to/) - 智能学习Vim的资源。
- [Vim Awesome](https://vimawesome.com/) - 搜索列出的Vim插件
- [Vim Adventures](https://vim-adventures.com/) - 一个交互式游戏，旨在通过引人入胜的谜题和挑战教用户Vim文本编辑器的基础知识。
- [Vimified](https://www.vimified.com/) - 学习和练习Vim的平台，一个免费开源的基于屏幕的文本编辑器程序。

### Git

- [Git - The Simple Guide](https://rogerdudler.github.io/git-guide/) - Git入门的直接指南。
- [Git Sheet](https://gitsheet.wtf) - 常见Git命令的快速参考指南。
- [MiXLab on Google Colab](https://colab.research.google.com/github/shirooo39/MiXLab/blob/master/MiXLab.ipynb#scrollTo=e-0yDs4C0HkB) - 从GitHub编译的Google Colab笔记本的混合。
- [Learn Git Branching](https://learngitbranching.js.org/) - 通过模拟git存储库学习Git分支的交互式工具。

### GitHub

- [GitStalk](https://gitstalk.netlify.app/) - 发现GitHub社区中个人正在做什么的平台。
- [GitExplorer](https://gitexplorer.com/) - 无需在网络中搜索即可找到正确Git命令的工具。
- [GitStats](https://gitstats.me/) - 开源GitHub贡献分析器，提供对您的GitHub活动的见解。
- [Gitcoin](https://gitcoin.co/) - 个人可以通过为各种编程语言和领域的开源软件做贡献而获得报酬的平台。
- [Oh Shit, Git!](https://ohshitgit.com/) - 为常见Git错误提供解决方案以及如何从中恢复的资源。
- [GitHub Trending Archive](https://github.motakasoft.com/trending/) - GitHub上趋势存储库的档案，允许用户随时间探索流行项目和编程趋势。
- [Map of GitHub](https://anvaka.github.io/map-of-github/) - GitHub存储库网络的可视化表示。

### IDEs

- [OneLang IDE](https://ide.onelang.io/) - 将代码从一种编程语言转换为另一种的在线工具。
- [Theia IDE](https://theia-ide.org/) - 灵活可扩展的云和桌面IDE平台，使用现代Web技术实现IDE和工具的高效开发和交付。
- [VSCode Themes](https://vscodethemes.com/) - 为Visual Studio Code提供各种主题的平台，允许用户通过不同的配色方案和样式个性化其编码环境的外观和感觉。

## Privacy

- [ToS;DR](https://tosdr.org/) - 服务条款；没有阅读（简称：ToS;DR）。
- [Nothing Private](https://www.nothingprivate.ml/) - 检查为什么使用隐私浏览模式或无痕模式时您并不匿名。您也可以在[这里](https://github.com/gautamkrishnar/nothing-private)阅读。
- [TrustPage](https://trustpage.com/directory) - 查找和比较数千家公司的安全政策，根据从网络上获取的安全政策选择合适的软件和服务。
- [Arkenfox User.js](https://github.com/arkenfox/user.js/wiki/4.1-Extensions) - 隐私和安全相关的浏览器扩展。
- [Cookiepedia](https://cookiepedia.co.uk/) - 最大的预分类Cookie和在线跟踪技术数据库。
- [Security Planner](https://securityplanner.consumerreports.org/) - 获得定制建议以减少数据收集并防止黑客攻击。
- [MyWhisper](https://mywhisper.net/) - 基于AES密码学的文本加密工具。
- [BeEncrypted](https://beencrypted.com/) - 变得更加加密的资源。
- [Prism Break](https://prism-break.org/en/all/) - 最常用应用程序和服务的私密安全替代方案。
- [Reddit Piracy Megathread](https://www.reddit.com/r/piracy/wiki/megathread/tools) - 应用程序、工具和Web服务列表。
- [Ressources](https://gitlab.com/tzkuat/Ressources) - 不同领域网站列表，如安全、OSINT等。
- [Privacy Tools List by CHEF-KOCH](https://chef-koch.bearblog.dev/privacy-tools-list-by-chef-koch/) - CHEF-KOCH的综合隐私工具列表。
- [Privacy Analyzer](https://privacy.net/analyzer/#pre-load) - 分析网站的隐私设置。
- [Shut Up Trackers](https://shutuptrackers.com/) - 提供保护数据安全和隐私的信息。
- [EFF Surveillance Self-Defense](https://ssd.eff.org/) - 更安全在线通信的提示、工具和操作指南。
- [Router Passwords](https://www.routerpasswords.com/) - 互联网上最新的默认路由器密码库。
- [BugMeNot](https://bugmenot.com/) - 查找和分享各种网站的登录信息。
- [Security.org](https://www.security.org/security-score/) - 了解您的安全评分。
- [LibreProjects](https://libreprojects.net/#favs=wikipedia,joindiaspora-com,nextcloud,openstreetmap,jamendo,plos) - 118个开源托管Web服务列表。
- [Privnote](https://privnote.com/) - 发送阅读后会自毁的笔记。
- [Dangerzone](https://dangerzone.rocks/) - 将潜在不安全的PDF、Office文档或图像转换为安全可查看格式的平台。
- [ExifTool](https://exiftool.org/) - 读取、写入和编辑图像、音频和视频文件元数据的软件。
- [HideMyWeb](https://hidemyweb.wordpress.com/) - 隐藏、模糊和突出显示网页内容的工具。
- [Browser.lol](https://browser.lol/) - 在浏览器内使用免费虚拟环境匿名浏览。
- [OneTimeSecret](https://onetimesecret.com/) - 使用一次性链接安全分享密码、秘密消息或私人链接的平台。
- [MetaDefender](https://metadefender.opswat.com/) - 扫描和分析文件安全威胁的在线工具，提供潜在风险的详细报告。
- [FotoForensics](https://fotoforensics.com/) - 分析和验证数字图像的在线工具，提供检测照片修改和编辑的取证工具。

### Cryptography

- [Cryptologie](https://www.cryptologie.net/) - 涵盖密码学各种主题的网站。
- [Invisible](https://mikebradley.me/invisible/index.html) - 在图像中隐藏文本并允许您查找隐藏文本的工具。

### GAFA Alternatives

- [DeGoogle](https://degoogle.jmoore.dev/#mobile-applications-mobile-apps-installable-from-stores) - Google产品替代方案的庞大列表。隐私提示、技巧和链接。
- [Degooglisons Internet](https://degooglisons-internet.org/en/) - FAANG的替代方案。
- [AccountKiller](https://www.accountkiller.com/en/home) - AccountKiller收集直接链接和删除说明，使账户终止变得容易。
- [JustDeleteMe](https://backgroundchecks.org/justdeleteme/) - 从Web服务删除您的账户的直接链接目录。
- [SmartTubeNext](https://github.com/yuliskov/SmartTubeNext) - 在Android TV盒子上观看YouTube视频的无广告应用。
- [Libredirect](https://libredirect.github.io/) - 将YouTube、Twitter、Instagram请求重定向到隐私友好的替代前端和后端的Web扩展。

### Ad Blocker

- [Adblock Tester](https://adblock-tester.com/) - 检查AdBlock扩展有效性的工具。
- [12ft](https://12ft.io/) - 移除付费墙并获得文章访问权限。
- [Incoggo](https://incoggo.com/) - 针对付费墙的广告拦截器。

### Emails

- [BurnerMail](https://burnermail.io/) - 保护您的个人电子邮件地址，控制谁可以向您发送电子邮件，一键生成新的临时地址。
- [Kill the Newsletter](https://kill-the-newsletter.com/) - 将电子邮件通讯转换为Atom提要以便于消费。
- [Dead Man's Switch](https://www.deadmansswitch.net/) - 如果您在指定时间内不与其交互，则将您的电子邮件发送给指定收件人的服务。
- [YAMM](https://yamm.com/) ($) - Gmail的邮件合并，允许您使用Gmail发送大量邮件，确保投递到主收件箱。直接从Google Sheets实时跟踪结果。

#### Disposable Email

- [Erine Email](https://erine.email/) - 为您现有电子邮件地址提供的反垃圾邮件服务。
- [Maildrop](https://maildrop.cc/) - 当您不想透露真实电子邮件地址时使用Maildrop。
- [Mailsac](https://mailsac.com/) - 为测试和开发目的提供临时电子邮件服务。
- [InboxKitten](https://inboxkitten.com/) - 开源的临时电子邮件服务。
- [Guerrilla Mail](https://www.guerrillamail.com/inbox) - 允许您撰写电子邮件并确定临时电子邮件的域名、主密码短语和密码。
- [EmailDrop](https://www.emaildrop.io/) - 以高度简约的设计创建具有自定义或随机域名的电子邮件。
- [GetNada](https://getnada.com/) - 提供临时电子邮件地址。
- [GetNotify](https://www.getnotify.com/) - 免费的电子邮件跟踪服务，当您发送的电子邮件被阅读时通知您。
- [MXToolbox](https://mxtoolbox.com/) - 在一个地方提供免费的DNS和电子邮件工具，简化故障排除。
- [Postale](https://postale.io/) ($) - 允许您在几分钟内轻松创建域名电子邮件地址。

### Data Breach

- [Have I Been Pwned](https://haveibeenpwned.com/) - 检查您的电子邮件或手机是否在数据泄露中。
- [TinEye](https://tineye.com/) - 查找图像在线出现的位置。
- [IPLeak](https://ipleak.net/) - 查看您访问的所有网站可以看到和收集的关于您的信息类型。
- [Cover Your Tracks](https://coveryourtracks.eff.org/) - 测试您的浏览器，查看您在跟踪和指纹识别方面的保护程度。
- [Am I FLoCed](https://amifloced.org/) - 检查Google是否在您的Chrome浏览器上测试FLoC。

### Search

- [Startpage](https://www.startpage.com/) - 在整个网络上获得隐私保护。
- [Neeva](https://neeva.com/) - 无广告的私人搜索引擎，采用订阅模式。
- [AndiSearch](https://andisearch.com/) - 无广告和匿名搜索引擎。
- [Plex Page](https://plex.page/) - 搜索摘要工具。
- [Marginalia Search](https://search.marginalia.nu/) - 专注于非商业内容的独立DIY搜索引擎。
- [Unlisted Videos](https://unlistedvideos.com/) - 用于提交、搜索和观看未列出的YouTube视频。无需注册。
- [Filmot](https://filmot.com/) - 在YouTube字幕中搜索。
- [XN--1-ZFA](https://xn--1-zfa.com/) - Google、DuckDuckGo、Twitter、YouTube、Reddit的高级搜索。
- [Seekr](https://www.seekr.com/) - 利用AI分析和评分内容质量的搜索引擎，从新闻文章开始。
- [Million Short](https://millionshort.com/) - 发现前百万搜索结果之外的内容。

### Internet

- [Internet Live Stats](https://www.internetlivestats.com/) - 提供有关互联网的实时统计信息，包括网站数量、电子邮件等。
- [Internet Map](https://internet-map.net/) - 可视化表示互联网的交互式地图，显示各种网站之间的关系。
- [Test IPv6](https://test-ipv6.com/) - 允许您测试IPv6连接性并提供有关网络设置的信息。
- [TLS 1.2 Explained](https://tls12.xargs.org/) - 提供TLS连接中每个字节的详细解释。
- [CIDR.xyz](https://cidr.xyz/) - 交互式IP地址和CIDR范围可视化工具。
- [I Can Haz IP](https://icanhazip.com/) - 在页面顶部仅显示您的IP地址。
- [IP Location](https://iplocation.io/) - 提供输入IP地址的免费位置跟踪，包括城市、国家、纬度和经度。
- [Ifconfig.co](https://ifconfig.co/) - 帮助您找到自己的IP地址并提供相关信息。
- [IPinfo.io](https://ipinfo.io/) - 为各种用例提供准确的IP地址数据。
- [Visual Subnet Calculator](https://www.davidc.net/sites/default/subnets/subnets.html) - 帮助进行IP地址和子网计算的可视化子网计算器。
- [Ping Test](https://www.meter.net/ping-test/) - 通过向指定服务器发送测试数据包（ping）来测量网络延迟的在线工具，提供连接稳定性和速度的见解。
- [LibreSpeed](https://librespeed.org/) - 开源互联网速度测试工具，提供下载、上传和延迟的准确测量，无需安装额外软件。

#### DNS

- [How DNS Works](https://howdns.works/) - 通过漫画的帮助提供DNS工作原理的生动解释。
- [DNS Checker](https://dnschecker.org/) - 提供免费的DNS查找服务，检查域名系统记录对全球选定DNS服务器列表。
- [AdGuard DNS Providers](https://kb.adguard.com/en/general/dns-providers) - DNS提供商列表，您可以配置使用以替代路由器或ISP提供的默认设置。
- [DNS Speed Test](https://dnsspeedtest.online/) - 快速DNS服务器速度测试工具，允许用户找到最佳DNS服务器以实现更快的互联网浏览，无需安装。

### URL

- [URL Tools](https://shrtco.de/tools/) - 各种URL工具。
- [OneLink](https://www.onelink.to/) - 为您的应用创建链接和二维码的平台。
- [QR Code Generator](https://freecodetools.org/qr-code-generator/) - 生成二维码的工具。
- [AppOpener](https://appopener.com/) - 创建智能链接以从URL打开所需应用，无需登录。
- [Who Is Hosting Any Domain](https://digital.com/best-web-hosting/who-is/) - 查找任何域名的托管商，包括网络主机、IP地址、名称服务器等。
- [Webhook.Site](https://webhook.site/) - 检查、测试和自动化任何传入HTTP请求或电子邮件的平台。
- [GoQR.Me](https://goqr.me/) - 生成适合打印的高分辨率二维码的生成器。
- [Open Graph Generator](https://freecodetools.org/ogp/) - 生成Open Graph图像的工具。

#### URL Shortener

- [Rebrandly](https://www.rebrandly.com/) - 使用自定义域名品牌化、跟踪和分享短URL的链接管理平台。
- [Bit.do](https://bit.do/) - 免费为您的链接提供实时流量统计。
- [s.id](https://home.s.id/) - 链接缩短器和微网站构建器。
- [TinyURL](https://tinyurl.com/app) - 提供可选的短链接结尾。
- [Tiny.cc](https://tiny.cc/) - 提供可选的短链接结尾。
- [Bitly](https://bitly.com/) - Web/移动链接管理和活动管理分析以及品牌链接。
- [iplogger.org](https://iplogger.org/) - 具有高级分析功能的URL缩短器，分析通过您的链接的流量、在线商店、博客或网站的访客。
- [cutr.ml](https://cutr.ml/) - URL缩短器。
- [is.gd](https://is.gd/) - URL缩短器。
- [gg.gg](https://gg.gg/) - URL缩短器。
- [shrunken.com](https://www.shrunken.com/) - URL缩短器。

### VPN

- [Top10VPN](https://www.top10vpn.com/) - 提供各种VPN服务信息和评论的平台。
- [What's My Browser](https://www.whatsmybrowser.org/) - 检查您的浏览器详细信息，包括版本和插件。
- [VPN Comparison Spreadsheet](https://docs.google.com/spreadsheets/d/1ijfqfLrJWLUVBfJZ_YalVpstWsjw-JGzkvMd6u2jqEk/edit#gid=231869418) - 包含分析和比较的所有VPN详细列表。
- [Njalla](https://njal.la/) ($) - 提供从您的计算机到互联网的加密隧道，将您的真实IP地址隐藏在他们的IP地址后面。

### Fake Information Generation

- [Fake Name Generator](https://www.fakenamegenerator.com/) - 生成虚假个人资料信息，包括37种语言和31个国家的姓名、地址和电话号码。
- [Burner](https://www.burnerapp.com/) - 创建临时第二个电话号码用于通话和短信的应用，对隐私保护和避免垃圾信息有用。
- [Random.org](https://www.random.org/) - 生成真随机数、列表、字符串和映射的服务。

### Password Generation

- [Everything About Passwords and Internet Security](https://www.healthypasswords.com/index-2.html) - 关于密码和互联网安全的综合信息。
- [Random Password Generator](https://random-password-gen.web.app/) - 生成随机和安全的密码。
- [How Secure Is My Password?](https://www.security.org/how-secure-is-my-password/) - 检查密码的安全强度。输入是安全的，不会被存储或分享。
- [Password Generator](https://freecodetools.org/password-generator/) - 轻松生成安全密码。

## Softwares

- [OlderGeeks](https://oldergeeks.com/) - 由捐赠支持的无广告软件下载网站，提供无忧体验。
- [AlternativeTo](https://alternativeto.net/) - 基于用户推荐发现流行软件的最佳替代方案。
- [Open Source Alternative](https://www.opensourcealternative.to/) - 提供专有软件开源替代方案的平台。
- [Dark Mode List](https://darkmodelist.com/) - 支持暗黑模式的300个应用列表。
- [LocalStack](https://localstack.cloud/) - 离线开发和测试云和无服务器应用。
- [Markwhen](https://markwhen.com/) - 将类markdown文本转换为级联时间线的文本到时间线工具。
- [Asciinema](https://asciinema.org/) - 以纯文本方式记录和分享您的终端会话。
- [Apache Guacamole](https://guacamole.apache.org/) - 支持VNC、RDP和SSH等协议的无客户端远程桌面网关。
- [DockerSwarm.rocks](https://dockerswarm.rocks/) - 使用Docker Compose文件将应用程序堆栈部署到分布式集群的生产环境。
- [EasyCron](https://www.easycron.com/) - 在线定时任务服务（付费）。
- [CDecl](https://cdecl.org/) - 将C语言难懂的声明翻译为英语，帮助您理解复杂的C声明。
- [NirSoft](https://www.nirsoft.net/) - Windows小工具集合，包括系统工具、密码恢复工具等。
- [Ninite](https://ninite.com/) - 一次安装和更新多个程序，无工具栏或不必要的点击。
- [ReadWok](https://app.readwok.com/lib) - 具有渐进式查看模式的在线文本阅读器，允许逐段阅读和编辑。
- [BitwiseCMD](https://bitwisecmd.com/) - 在线位运算和转换。
- [Commands.dev](https://www.commands.dev/?ref=producthunt) - 可搜索的模板化流行终端命令目录，从互联网各处精选。
- [SourceForge](https://sourceforge.net/) - 基于Web的服务，允许您比较、下载和开发开源和商业软件。

### Snippets

- [CodeClippet](https://codeclippet.com/) - 在以社区为中心的环境中分享代码片段的平台。
- [Kod.so](https://kod.so/) - 创建可视化代码片段的平台，具有下载和分享功能。
- [CodePNG](https://www.codepng.app/) - 通过将代码转换为图像来从源代码创建图片的工具。
- [CodeMyUI](https://codemyui.com/) - 提供网页设计和UI灵感以及精选代码片段的网站。
- [30 Seconds of Code](https://www.30secondsofcode.org/list/p/1) - 为各种开发需求精选的短代码片段集合。
- [W3Schools HowTo](https://www.w3schools.com/howto/default.asp) - W3Schools提供HTML、CSS和JavaScript代码片段的部分。
- [Snipplr](https://snipplr.com/) - 将常用代码片段保存在一个可访问位置的平台，允许用户分享和发现代码片段。
- [LittleSnippets](https://littlesnippets.net/) - 分享和发现小代码片段的平台。
- [CodeToGo](https://codetogo.io/) - 查找JavaScript和React用例的最新代码片段的资源。
- [TweetSnippet](https://tweetsnippet.com/) - 来自Twitter的技巧和窍门精选列表，以代码片段形式呈现。
- [CSS-Tricks Snippets](https://css-tricks.com/snippets/) - 涵盖各种设计和布局技术的CSS代码片段集合。
- [Crontab Guru](https://crontab.guru/) - 用于cron调度表达式的快速简单编辑器，提供人类可读的解释。
- [Crontab Generator](https://crontab-generator.org/) - 生成cron调度表达式的在线工具，具有用户友好的界面。
- [Carbon](https://carbon.now.sh/) - 创建和分享具有语法高亮的源代码片段美丽图像的基于Web的工具。

### Linters

- [FromLatest.io](https://www.fromlatest.io/#/) - 用于检查Dockerfile语法和最佳实践的Dockerfile代码检查器。
- [YAMLlint](https://www.yamllint.com/) - 检查YAML代码有效性并提供为Ruby优化的干净UTF-8版本的在线工具。
- [K8sYaml](https://k8syaml.com/) - 用于创建Kubernetes配置文件的Kubernetes YAML生成器。
- [Puppet Validator](https://validate.puppet.com/) - 检查Puppet代码语法有效性的工具，无需编译或强制执行目录。
- [ShellCheck](https://www.shellcheck.net/) - 通过分析并提供改进建议来查找shell脚本中bug的在线平台。
- [Beautifier.io](https://beautifier.io/) - 美化、解压或反混淆JavaScript和HTML，并使JSON/JSONP可读的在线工具。
- [JSONLint](https://jsonlint.com/) - JSON验证器和重新格式化工具，为混乱的JSON代码提供整理和验证。
- [Algorithm Visualizer](https://algorithm-visualizer.org/) - 从代码可视化算法以帮助理解其执行的交互式平台。
- [CodeBeautify](https://codebeautify.org/) - 提供代码格式化工具的在线平台，包括JSON美化器、XML查看器、十六进制转换器等。
- [ExplainShell](https://explainshell.com/) - 通过显示每个参数的帮助文本来解释命令行命令的工具。
- [ShowTheDocs](https://showthedocs.com/) - 为您的代码查找相关文档的文档浏览器，使探索和理解变得更容易。
- [Mango - Specification Interpretation](https://mango-slra1-ckwssph7iq-ue.a.run.app/readme) - 解释规格说明并自动识别逻辑矛盾、过度复杂性和实体名称不一致等缺陷的技术。
- [Code Minifier](https://freecodetools.org/minifier/) - 用于压缩代码以减少文件大小和提高加载时间的在线工具。
- [Markdown Preview](https://freecodetools.org/markdown-preview/) - 预览和格式化Markdown文本的在线工具。
- [Code Beautifier](https://freecodetools.org/beautifier/) - 美化和格式化代码以提高可读性的在线工具。
- [CodePad](https://codepad.org/) - 在线编译器/解释器和协作工具，允许用户使用短URL运行和分享代码片段。
- [JSON Formatter](https://jsonformatter.org/json-parser) - 在线JSON解析器和格式化工具。

### Testing

- [OWASP Fuzzing Project](https://owasp.org/www-community/Fuzzing) - OWASP模糊测试项目的官方页面，旨在改善模糊测试工具、技术和流程的整体状态。
- [Awesome Fuzzing](https://github.com/secfigo/Awesome-Fuzzing) - 模糊测试资源的精选列表，包括工具、教程、研究论文等。

### Regex

- [Regex Cheat Sheet](https://dev.to/emmabostian/regex-cheat-sheet-2j2a) - 正则表达式的有用指南。
- [RegExr](https://regexr.com/) - 学习、构建和测试正则表达式（RegEx/RegExp）的在线工具。
- [Regulex](https://jex.im/regulex/) - 用于理解和可视化正则表达式的JavaScript正则表达式可视化工具。
- [Regex101](https://regex101.com/) - 在线正则表达式测试器和调试器。
- [Debuggex](https://www.debuggex.com/) - 可视化正则表达式测试器，让您了解正则表达式的工作原理。
- [UI Bakery Regex Library](https://uibakery.io/regex-library/) - 用于UI设计的正则表达式集合。

### No-Code

- [No Code List](https://nocodelist.co/) - 浏览类别以发现超过300个无代码工具。
- [No-Code Things](https://www.spacebarcounter.net/no-code-tools) - 无代码工具集合。

#### Licensing

- [ChooseALicense - Appendix](https://choosealicense.com/appendix/) - 与开源许可证相关的附加信息和资源。
- [GitHub Docs - Licensing a Repository](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/licensing-a-repository) - GitHub关于存储库许可的文档。
- [Public License Selector](https://ufal.github.io/public-license-selector/) - 帮助您根据偏好选择开源许可证。
- [Creative Commons License Chooser](https://creativecommons.org/choose/) - 选择适合您内容分享偏好的知识共享许可证。
- [License Buttons](https://licensebuttons.net/) - 获取指示您内容许可条款的网站按钮。
- [Shields.io](https://shields.io/) - 为您的GitHub存储库创建自定义徽章（盾牌），提供许可证、版本等信息。

## Programming Languages

- [EbookFoundation-Free-Programming-Books](https://github.com/EbookFoundation/free-programming-books/blob/main/books/free-programming-books-langs.md) - 免费和开源编程书籍
- [Codecademy Catalog](https://www.codecademy.com/catalog) - 提供学习各种编程语言和技术课程目录的平台。
- [Learn X in Y Minutes](https://learnxinyminutes.com/) - 提供各种编程语言快速概览的资源。
- [eComputerNotes](https://ecomputernotes.com/) - 在线教育学习资源，涵盖广泛的计算机科学和编程主题。
- [Libraries.io](https://libraries.io/) - 发现新的开源包、模块和框架，并跟踪依赖关系的平台。
- [LearnByExample](https://www.learnbyexample.org/) - 通过示例和实际解释学习Python、SQL和R语言的平台。
- [PythonTutor](https://pythontutor.com/) - 通过逐步可视化代码执行帮助用户学习Python、JavaScript、C、C++和Java编程的工具。
- [Classic Papers in Programming Languages and Logic](https://www.cs.cmu.edu/~crary/819-f09/) - 卡内基梅隆大学精选的编程语言和逻辑开创性学术论文集合。
- [RubyGems Guides](https://guides.rubygems.org/) - 学习RubyGems的工作原理以及如何制作自己的gem

### Haskell

- [Learn You a Haskell for Great Good!](https://learnyouahaskell.github.io/chapters.html) - Miran Lipovača著的"Learn You a Haskell"在线图书版本。
- [Real World Haskell](https://book.realworldhaskell.org/read/) - Bryan O'Sullivan、Don Stewart和John Goerzen著的"Real World Haskell"在线图书版本。
- [CIS 194 - Introduction to Haskell (Spring 2013)](https://www.seas.upenn.edu/~cis1940/spring13/lectures.html) - Haskell编程的讲座材料和资源。
- [GitHub - Programming in Haskell](https://github.com/topics/programming-in-haskell) - GitHub上Haskell相关存储库和项目的集合。

### Python

- [Python Documentation](https://docs.python.org/3/) - Python编程语言的官方文档。
- [Python Wiki](https://wiki.python.org/moin/FrontPage) - 提供Python相关信息和资源的协作平台。
- [Awesome Python](https://awesome-python.com/) - 精选的优秀Python框架、库、软件和资源列表。
- [Project Python](https://projectpython.net/) - 提供Python教程和学习开发资源的平台。
- [Learn Python](https://www.learnpython.org/) - 提供教程和练习来学习Python编程的交互式平台。
- [CSCircles](https://cscircles.cemc.uwaterloo.ca/) - 滑铁卢大学开发的提供交互式Python练习和资源的在线平台。
- [PythonBasics](https://pythonbasics.org/) - 提供学习Python基础资源和教程的网站。
- [Majyori](https://www.majyori.com/) - 为所有级别学习者提供Python教程和资源的平台。
- [PyFormat](https://pyformat.info/) - 比较不同版本Python格式化样式并提供示例的网站。
- [Learn by Example - Python Resources](https://learnbyexample.github.io/py_resources/) - 各种主题的Python资源和教程集合。
- [Data to Fish](https://datatofish.com/python-tutorials/) - 提供清晰Python教程的平台，涵盖机器学习、数据库、pandas、GUI等主题。
- [Notion Programming Course](https://www.notion.so/Programming-Course-4d4331de1a0c4ae894133cb1ca1e9315) - 在Notion上托管的学习用Django构建基本Web应用程序的自学课程。

### C++

- [LearnCpp.com](https://www.learncpp.com/) - 学习C++编程的综合资源。

## Coding Practice / Competitive Programming

- [LeetCode](https://leetcode.com/) - 专注于算法挑战的编程问题练习平台。
- [NeetCode](https://neetcode.io/) - 通过现实项目和协作编程提升技能的编程练习网站。
- [HackerRank](https://www.hackerrank.com/dashboard) - 提供从算法到人工智能各领域挑战的编程平台。
- [CodeWars](https://www.codewars.com/) - 社区驱动的平台，通过同行评审解决方案提供编程挑战以改进技能。
- [Project Euler](https://projecteuler.net/about) - 以数学为导向的编程平台，通过具有挑战性的问题鼓励通过编程解决问题。
- [Kattis Guide](https://unh-cpc.github.io/kattisguide.html) - 使用Kattis平台解决竞技编程问题的指南，为参与者提供技巧、解题策略和资源。
- [Kaggle](https://www.kaggle.com/) - 举办竞赛、数据集和笔记本的数据科学平台，促进该领域的合作和创新。
- [Replit](https://replit.com/) - 促进协作编程的在线编程环境，具有实时分享和广泛语言支持等功能。
- [AlgoLeague](https://www.algoleague.com/) - 练习算法问题解决的竞技编程平台。
- [Codeforces](https://codeforces.com/) - 提供竞赛和大量题目集的在线竞技编程平台，吸引多样化的国际用户群体。
- [AtCoder](https://atcoder.jp/) - 举办竞赛和练习以增强算法技能的日本竞技编程平台。
- [InterviewBit](https://www.interviewbit.com/coding-interview-questions/) - 提供编程面试问题和挑战以帮助准备技术面试的平台。
- [Advent of Code](https://adventofcode.com/) - 适合各种技能水平的小型编程谜题降临日历，可用任何编程语言解决。
- [CList](https://clist.by/) - 来自各种编程网站的竞赛列表，为即将到来的竞赛提供集中资源。
- [Codeply](https://www.codeply.com/) - 具有数十个框架、启动模板和超过50,000个代码片段的免费在线编辑器。
- [URI Online Judge](https://www.urionlinejudge.com.br/judge/en/login) - 用于训练算法和编程挑战以增强编程技能的平台。
- [Rosalind](https://rosalind.info/problems/locations/) - 通过解决问题学习生物信息学和编程的平台。
- [Kattis](https://open.kattis.com/) - 拥有数百个编程问题的平台，帮助用户练习和提高编程技能。
- [Exercism](https://exercism.org/tracks) - 通过学习、练习和指导的独特结合免费开发67种编程语言流利度的平台。
- [Codility](https://codility.com/programmers/challenges) - 提供编程挑战以评估和改进算法和问题解决技能的平台。
- [r/dailyprogrammer](https://www.reddit.com/r/dailyprogrammer/) - Reddit上为编程爱好者提供每日编程挑战和讨论的子版块。
- [Daily Coding Problem](https://www.dailycodingproblem.com/) - 向订阅者发送每日编程挑战以改进编程和问题解决技能的服务。
- [Coderbyte](https://coderbyte.com/) - 具有编程挑战和面试准备资源以增强编程技能的平台。
- [CodinGame](https://www.codingame.com/start) - 游戏化编程挑战的在线平台，使学习和练习编程更具吸引力。
- [A2OJ](https://a2oj.netlify.app/) - 提供结构化阶梯系统以改进问题解决技能的竞技编程资源，按难度级别和主题分类。
- [CodeChef](https://www.codechef.com/) - 定期举办竞赛并拥有大型题库的竞技编程网站，面向全球编程社区。
- [USACO](http://usaco.org/index.php?page=contests) - 美国计算奥林匹克平台，提供编程竞赛以鼓励和识别美国有才华的年轻程序员。
- [USACO Guide](https://usaco.guide/) - 在竞技编程中指导用户从青铜级到白金级及以上的综合免费资源集合。
- [JoinCPI](https://joincpi.org/) - 致力于通过资源、课程、推广和竞赛在学生中推广竞技编程的平台。
- [CP-Algorithms](https://cp-algorithms.com/) - 提供专门为竞技编程量身定制的算法详细指南的网站，提供深入解释和示例。
- [CSS Battle](https://cssbattle.dev/) - 挑战用户使用CSS技能以最小代码复制目标的平台。
- [JavaScript Quiz](https://javascriptquiz.com/) - 提供专注于测试和增强JavaScript编程语言知识的测验的网站。
- [Elevator Saga](https://play.elevatorsaga.com/) - 学习和练习JavaScript的游戏。
- [Deep ML](https://www.deep-ml.com/) - 提供机器学习代码挑战的网站。
- [Hack The Box](https://www.hackthebox.com/) - 提供虚拟环境和挑战以帮助个人和组织发展网络安全技能的黑客训练在线平台。

### Capture the Flag

- [CTF101](https://ctf101.org/) - 为Capture The Flag竞赛新手提供教育资源的平台。
- [CTFtime](https://ctftime.org/) - Capture The Flag事件、团队和时间线的档案。
- [Google CTF on GitHub](https://github.com/google/google-ctf) - GitHub上可用的Google Capture The Flag竞赛资源。
- [Meusec CTF Resources](https://www.meusec.com/ctf/capture-the-flags-in-cybersecurity/) - Meusec在网络安全领域的Capture The Flag (CTF)资源集合。
- [Trail of Bits - CTF Guide](https://trailofbits.github.io/ctf/) - Trail of Bits提供参与Capture The Flag竞赛的见解和技巧的指南。

### Projects

- [Projects-Solutions on GitHub](https://github.com/karan/Projects-Solutions) - 提供基于项目的编程挑战以通过实际应用学习编程的GitHub存储库。
- [Build Your Own X](https://github.com/codecrafters-io/build-your-own-x#build-your-own-neural-network) - 提供从零开始构建您喜爱技术指南的GitHub存储库。
- [Arduino Project Hub](https://projecthub.arduino.cc/) - 分享和发现Arduino项目的中心。
- [Projects in Networking](https://projectsinnetworking.com/) - 为计算机网络和安全领域的学生、毕业生和专业人士提供网络项目、网络安全项目、网络安全案例研究和源代码的资源。

### Open Source

- [LibHunt](https://www.libhunt.com/) - 发现趋势开源项目及其替代方案的平台。
- [Awesome Open Source](https://awesomeopensource.com/) - 通过在各种类别和项目中搜索、浏览和组合主题来查找开源项目的平台。
- [Open Source Libs](https://opensourcelibs.com/) - 世界最佳开源软件的大量集合。
- [CodeTriage](https://www.codetriage.com/) - 您可以贡献的开源存储库问题列表，如果注册可选择通过电子邮件接收问题。
- [GitLab Explore](https://gitlab.com/explore/projects/starred/) - 在GitLab上探索项目。
- [Open Source Guide](https://opensource.guide/) - 提供启动和发展开源项目资源的指南。
- [The Architecture of Open Source Applications](https://aosabook.org/en/index.html) - 关于各种开源软件项目设计和架构的书籍系列。
- [OSS Gallery](https://oss.gallery/) - 互联网上最佳开源项目的众包集合，提供发现和探索顶级软件存储库和工具的简便方法。

### Hackathons

- [DEVPOST Hackathons](https://devpost.com/hackathons) - 查找在线和线下黑客马拉松。
- [GitHub Education Events](https://education.github.com/events) - 在您附近的黑客马拉松、会议和活动中找到社区成员。
- [Hackathons by Hack Club](https://hackathons.hackclub.com/) - 精选的高中黑客马拉松列表，包含27个州和18个国家的699个活动。
- [Cerebral Valley AI Events](https://events.cerebralvalley.ai/) - 提供即将到来的AI相关活动、黑客马拉松和共同工作机会的信息。

## Cheat Sheets

- [DevHints](https://devhints.io) - 各种编程语言和工具的速查表集合。
- [Cheatography](https://cheatography.com/) - 提供用户生成的各种主题速查表的平台。
- [Sound of Sorting Algorithm Cheat Sheet](https://panthema.net/2013/sound-of-sorting/SoS-CheatSheet.pdf) - 与"排序算法之声"可视化项目相关的速查表。
- [SANS Cheat Sheets](https://www.sans.org/blog/the-ultimate-list-of-sans-cheat-sheets/) - 涵盖一般IT安全主题的SANS速查表终极列表。
- [Codecademy Cheatsheets](https://www.codecademy.com/resources/cheatsheets/all) - 各种编程语言和概念的速查表集合。
- [Awesome Cheatsheets](https://lecoupa.github.io/awesome-cheatsheets/) - 涵盖广泛编程语言、工具和主题的精选优秀速查表列表。
- [OverAPI](https://overapi.com/) - 为各种编程语言和框架提供集中访问速查表和快速参考的平台。
- [Nota Language](https://nota-lang.org/) - 为浏览器设计的文档语言，提供创建基于Web文档的简化语法。
- [Cheat-Sheets.org](https://www.cheat-sheets.org/) - 涵盖各种主题的速查表、汇总、快速参考卡、指南和表格的编译。

### Python Cheat Sheet

- [Speedsheet](https://speedsheet.io/) - 用于更快更好编程的交互式Python速查表。
- [Python Cheatsheet](https://www.pythoncheatsheet.org/) - 基于《用Python自动化繁琐工作》一书和其他各种来源的速查表。
- [Zero to Mastery Python Cheat Sheet](https://zerotomastery.io/cheatsheets/python-cheat-sheet/) - Zero to Mastery提供的Python速查表。

### Front-end Cheat Sheet

- [Can I use](https://caniuse.com/) - 为桌面和移动浏览器提供前端Web技术的最新浏览器支持表。
- [Easings](https://easings.net/en) - 帮助您为动画选择正确的缓动函数。
- [WebCode.tools](https://webcode.tools/) - 协助前端Web项目的代码生成器。
- [MarkSheet](https://marksheet.io/) - 提供免费的HTML和CSS教程。
- [Xul.fr](https://www.xul.fr/en/) - CSS、HTML和JavaScript的教程和索引。
- [Emmet Cheat Sheet](https://docs.emmet.io/cheat-sheet/) - Emmet速查表，Emmet是Web开发人员更快编写HTML和CSS代码的工具包。

#### HTML Cheat Sheet

- [HTML Cheat Sheet](https://htmlcheatsheet.com) - 涵盖各种元素和属性的综合HTML速查表。
- [HTML5 Doctor Element Index](https://html5doctor.com/element-index/) - HTML5中新增或重新定义元素的快速参考。
- [HTML5 Canvas Cheat Sheet](https://simon.html5.org/dump/html5-canvas-cheat-sheet.html) - HTML5 Canvas速查表，提供其属性和方法的快速参考。
- [HTML Vocabulary](https://apps.workflower.fi/vocabs/html/en#children) - 提供HTML词汇表的资源，对元素及其关系进行分类。
- [HTML Reference](https://htmlreference.io/) - 免费HTML指南，包含所有HTML元素和属性的详细解释。

#### CSS Cheat Sheet

- [CSS Reference](https://cssreference.io) - CSS属性和选择器的综合参考指南。
- [Grid Malven](https://grid.malven.co) - CSS Grid布局的交互式指南。
- [Flexbox Malven](https://flexbox.malven.co/) - CSS Flexbox布局的交互式指南。
- [Justin Aguilar Animations](https://www.justinaguilar.com/animations/) - 带实时预览的CSS动画集合。
- [CSS Grid Cheat Sheet](https://alialaa.github.io/css-grid-cheat-sheet/) - CSS Grid布局的可视化速查表。
- [Adam Marsden CSS Cheat Sheet](https://adam-marsden.co.uk/css-cheat-sheet) - 带简洁解释和示例的CSS速查表。
- [Responsive Web Design Cheat Sheet](https://uxpin.s3.amazonaws.com/responsive_web_design_cheatsheet.pdf) - 响应式Web设计原则的PDF速查表。
- [Media Queries Cheat Sheet](https://mac-blog.org.ua/css-3-media-queries-cheat-sheet) - CSS3媒体查询的速查表。
- [Bootsnipp](https://bootsnipp.com/) - 为Bootstrap HTML/CSS/JS框架提供设计元素、游乐场和代码片段的平台。
- [Bootstrap Cheatsheet](https://hackerthemes.com/bootstrap-cheatsheet/) - Bootstrap框架的可视化速查表。
- [HackerThemes](https://hackerthemes.com/) - 基于Bootstrap框架的UI/UX资源。

## Building Computer / PC Build

- [PCPartPicker](https://pcpartpicker.com/list/) - 用于规划和构建定制PC的工具，让用户创建配件列表、比较价格并确保组件之间的兼容性。
- [PCBuilder](https://pcbuilder.net/list/) - 设计定制PC构建的平台，为用户提供组件兼容性检查、价格比较和配置选项。
- [PC Builds](https://pc-builds.com/) - 定制PC构建资源，提供兼容组件的精选列表、指南和各种性能级别及预算的建议。
- [LinearMouse](https://linearmouse.app/) - 为Mac提供高级鼠标和触控板自定义选项的实用程序，允许用户微调手势、按钮映射和滚动行为。

### Keyboard

- [MechanicalKeyboards](https://mechanicalkeyboards.com/index.php) - 全球最大的专用机械键盘目录，提供快速配送和售后支持。
- [Keycaps.info](https://www.keycaps.info/) - 键帽爱好者的资源，提供各种键帽轮廓、设计和机械键盘兼容性信息。
- [Keybr](https://www.keybr.com/) - 提供可定制课程来改善触摸打字技能的平台网站。
- [PairType](https://www.pairtype.com/) - 与伙伴实时练习触摸打字的在线工具。
- [KeyCombiner](https://keycombiner.com/) - 学习和练习键盘快捷键和按键组合的平台。
- [Yip-Yip](https://www.yip-yip.com/) - 提供各种应用程序和程序键盘快捷键的在线工具。
- [Keebmaker](https://keebmaker.com/) - 创建定制机械键盘的资源。
- [Colemak - Learn](https://colemak.com/Learn) - Colemak键盘布局的学习资源。

#### Typing Practice

- [TypeFast](https://typefast.io/) - 旨在通过引人入胜的挑战和练习帮助用户提高打字速度和准确性的打字练习平台。
- [10FastFingers](https://10fastfingers.com/typing-test/english) - 测量英语打字速度和准确性的打字测试平台。
- [TypingClub](https://www.typingclub.com/) - 提供交互式打字课程和游戏以提高打字技能的平台。
- [Typing.com](https://www.typing.com/) - 为所有级别学习者提供打字课程、游戏和测试的在线资源。

#### Keyboard Shortcuts

- [UseTheKeyboard](https://usethekeyboard.com/) - Mac应用程序、Windows程序和网站的键盘快捷键集合，涵盖广泛的常用应用程序。
- [ASCII Tables](https://ascii-tables.com/) - 提供ASCII表、ALT代码、Z分数表、希腊字母表、T分布表和二进制翻译器的在线资源。
- [ShortcutFoo](https://www.shortcutfoo.com/) - 免费练习键盘快捷键的平台，支持Mac、Windows、Linux，并为各种应用程序、语言和终端提供速查表。
- [Microsoft Word Keyboard Shortcuts](https://support.microsoft.com/en-us/office/keyboard-shortcuts-in-word-************************************) - 提供Microsoft Word键盘快捷键综合列表的官方Microsoft Office支持页面。

## Other Websites of Websites

- [Stumbled.to](https://stumbled.to/) - 发现和分享有趣网站和内容的平台。
- [5000Best](https://5000best.com/websites/) - 各种类别中5000个最佳网站的精选集合。
- [Neal.fun](https://neal.fun/) - 具有娱乐性和交互性项目的网站。
- [Tennessine](https://tennessine.co.uk/) - 网络政治、数学和有趣项目的集合。
- [Blakeir](https://blakeir.com/blog/smart-youtube) - 列出一些具有智慧和洞察力内容的YouTube页面的博客文章。
- [AwesomeOSINT on GitHub](https://awesomeopensource.com/project/jivoi/awesome-osint#-pastebins) - 包含开源情报工具和资源精选列表的GitHub存储库。
- [MadeWithBubble](https://www.madewithbubble.xyz/) - 发现使用Bubble开发平台创建的应用程序和网站的平台。
- [NoviceDock](https://novicedock.com/) - 提供学习编程和相关主题精选资源的平台。
- [Hackr.io](https://hackr.io/) - 查找编程社区推荐的编程课程和教程的平台。
- [Limnology](https://limnology.co/en) - 20种不同语言的教育YouTube频道策划，按主题分类，可选择查找类似频道。
- [Tech Blogs](https://tech-blogs.dev/) - 优秀技术博客列表。
- [Track Awesome List](https://www.trackawesomelist.com/) - 跟踪超过500个优秀列表更新，您还可以通过RSS或新闻通讯订阅每日或每周更新。
- [Internet Is Fun](https://projects.kwon.nyc/internet-is-fun/) - 互联网上有趣和有意思网站的集合。
- [Wiby](https://wiby.me/) - 经典网络搜索引擎，旨在重现互联网早期的浏览体验，特别适合老式计算机。

<br>

# Contributing

- If you want to add a new category or website, create a [pull request](https://github.com/atakanaltok/awesome-useful-websites/pulls) (To avoiding duplications, seach whether your website is already included).

- If you are a site owner and believe that your site is not described accurately, please [rise an issue](https://github.com/atakanaltok/awesome-useful-websites/issues).

- If a website is down for some reason, it is generally kept in the list for archiving purposes, and also in case their maintainers might restore them. However, if the destination of a link has changed or a link is now broken, please [rise an issue](https://github.com/atakanaltok/awesome-useful-websites/issues).

<br>

# DISCLAIMER

By accessing and using the Awesome Useful Websites project ("the Project") on GitHub, you agree that the Project is not affiliated with, sponsored by, or endorsed by any of the websites listed. While efforts are made to maintain accurate and up-to-date information, the Project makes no guarantees regarding the accuracy, completeness, or reliability of the content. Changes of the content of linked websites are beyond the Project's control. The Project is provided "as is" without any warranties, express or implied.

<br>

# LICENSE

![Creative Commons License](https://i.creativecommons.org/l/by/4.0/88x31.png)

This work is licensed under a [Creative Commons Attribution 4.0 International](https://creativecommons.org/licenses/by/4.0/).
