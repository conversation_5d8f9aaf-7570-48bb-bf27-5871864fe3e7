import './Footer.css'

function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <h3>Awesome Useful Websites</h3>
            <p>发现互联网上的隐藏宝石，探索各种有用的在线工具和资源。</p>
          </div>
          
          <div className="footer-section">
            <h4>数据来源</h4>
            <ul>
              <li><a href="https://www.producthunt.com/" target="_blank" rel="noopener noreferrer">Product Hunt</a></li>
              <li><a href="https://www.reddit.com/r/InternetIsBeautiful/" target="_blank" rel="noopener noreferrer">Reddit - Internet Is Beautiful</a></li>
              <li><a href="https://news.ycombinator.com/" target="_blank" rel="noopener noreferrer">Hacker News</a></li>
              <li><a href="https://x.com/IndieRandWeb" target="_blank" rel="noopener noreferrer">IndieRandWeb</a></li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>技术栈</h4>
            <ul>
              <li>React 18</li>
              <li>Vite</li>
              <li>CSS3</li>
              <li>响应式设计</li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>特性</h4>
            <ul>
              <li>🔍 智能搜索</li>
              <li>📱 移动友好</li>
              <li>🎨 现代设计</li>
              <li>⚡ 快速加载</li>
            </ul>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p>&copy; {currentYear} Awesome Useful Websites. 基于开源数据构建。</p>
          <div className="footer-links">
            <span>Made with ❤️ for the community</span>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
