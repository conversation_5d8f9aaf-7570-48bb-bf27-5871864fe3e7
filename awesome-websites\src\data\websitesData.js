// 从AD.md解析出的网站数据
export const websitesData = {
  title: "Awesome Useful Websites",
  description: "Explore the internet's hidden gems with this list of awesome useful websites!",
  categories: [
    {
      name: "Tools",
      id: "tools",
      subcategories: [
        {
          name: "White Board",
          id: "white-board",
          websites: [
            {
              name: "TypeHere",
              url: "https://typehere.co/",
              description: "只能输入文字的空白网站。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "PixelPaper",
              url: "https://pixelpaper.io/",
              description: "永久免费的数字白板，无需注册，可嵌入SaaS产品。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Excalidraw",
              url: "https://excalidraw.com/",
              description: "可以绘制手绘风格图表的白板。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Blank Page",
              url: "https://blank.page/",
              description: "显示空白白页的简单网页。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        },
        {
          name: "Mind Map / Note Taking",
          id: "mind-map-note-taking",
          websites: [
            {
              name: "Relanote",
              url: "https://relanote.com/",
              description: "将您的笔记相互链接，形成思维网络。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Bubbl.us",
              url: "https://bubbl.us/",
              description: "在线思维导图。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "MindMup",
              url: "https://www.mindmup.com/",
              description: "免费在线思维导图。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        },
        {
          name: "Converter / Conversion",
          id: "converter-conversion",
          websites: [
            {
              name: "PDF2DOC",
              url: "https://pdf2doc.com/",
              description: "免费在线PDF转DOC转换器。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Online-Convert",
              url: "https://www.online-convert.com/",
              description: "在线转换不同格式的媒体文件。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "SmallPDF",
              url: "https://smallpdf.com/",
              description: "21种免费PDF转换、压缩和编辑工具。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        },
        {
          name: "Visual Tools",
          id: "visual-tools",
          websites: [
            {
              name: "Unscreen",
              url: "https://www.unscreen.com/",
              description: "自动免费移除视频背景。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Remove.bg",
              url: "https://www.remove.bg/",
              description: "自动免费移除图像背景。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Photopea",
              url: "https://www.photopea.com/",
              description: "免费在线图像编辑器，支持PSD、XCF、Sketch等格式。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "5000Best Tools",
          url: "https://5000best.com/tools/",
          description: "5000个工具。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "10015.io",
          url: "https://10015.io/",
          description: "免费的多合一工具箱，用于各种任务。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "UnTools",
          url: "https://untools.co/",
          description: "思维工具和框架集合。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "DIY",
      id: "diy",
      subcategories: [],
      websites: [
        {
          name: "WikiHow",
          url: "https://www.wikihow.com/Main-Page",
          description: "创建和分享操作指南的协作平台。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "ManualsLib",
          url: "https://www.manualslib.com/",
          description: "用户手册和指南的在线存储库。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "HowStuffWorks",
          url: "https://www.howstuffworks.com/",
          description: "通过深入的解释和文章探索事物的工作原理。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Instructables",
          url: "https://www.instructables.com/",
          description: "发现和分享DIY项目的平台。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Language",
      id: "language",
      subcategories: [
        {
          name: "Grammar",
          id: "grammar",
          websites: [
            {
              name: "GrammarBook",
              url: "https://www.grammarbook.com/english_rules.asp",
              description: "英语语法规则的综合指南。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Purdue OWL",
              url: "https://owl.purdue.edu/site_map.html",
              description: "普渡大学在线写作实验室，提供写作、语法和引用格式的资源。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "YouGlish",
          url: "https://youglish.com/",
          description: "使用YouTube提高您的英语发音。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "News in Levels",
          url: "https://www.newsinlevels.com/",
          description: "为英语学习者量身定制的世界新闻。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Travel",
      id: "travel",
      subcategories: [
        {
          name: "Flight",
          id: "flight",
          websites: [
            {
              name: "SeatGuru",
              url: "https://seatguru.com/",
              description: "探索1,278架飞机的座位图找到您的座位。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Flightradar24",
              url: "https://www.flightradar24.com/43,24.35/7",
              description: "全球航班跟踪服务，提供世界各地数千架飞机的实时信息。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "Passport Index",
          url: "https://www.passportindex.org/",
          description: "探索来自世界各地的护照信息，包括排名和详细信息。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "AllTrails",
          url: "https://www.alltrails.com/",
          description: "探索包含30万条步道的数据库，附有户外爱好者的评论和照片。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Music / Audio",
      id: "music-audio",
      subcategories: [
        {
          name: "Find Music",
          id: "find-music",
          websites: [
            {
              name: "Music-Map",
              url: "https://www.music-map.com/",
              description: "根据您的偏好发现相似的音乐。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Commercial Tunage",
              url: "https://www.commercialtunage.com/",
              description: "识别广告中播放的歌曲。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        },
        {
          name: "Free Music",
          id: "free-music",
          websites: [
            {
              name: "Pretzel Rocks",
              url: "https://www.pretzel.rocks/",
              description: "为Twitch和YouTube提供流媒体安全音乐。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Incompetech",
              url: "https://incompetech.com/",
              description: "免版税音乐集合。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "Online Tone Generator",
          url: "https://onlinetonegenerator.com/",
          description: "生成音调，您也可以以WAV格式下载它们。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Every Noise",
          url: "https://everynoise.com/",
          description: "算法生成的音乐流派空间散点图的持续尝试。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Music Lab",
          url: "https://musiclab.chromeexperiments.com/Experiments",
          description: "通过有趣的动手实验使音乐学习更加容易。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Movies and Series",
      id: "movies-series",
      subcategories: [
        {
          name: "Anime",
          id: "anime",
          websites: [
            {
              name: "WCostream",
              url: "https://m.wcostream.com/",
              description: "免费卡通和动漫系列流播。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "Kanopy",
          url: "https://www.kanopy.com/en/",
          description: "在图书馆或大学支持下免费提供数千部电影的流媒体平台。",
          isPaid: false,
          isStudentFriendly: true
        },
        {
          name: "Movie Map",
          url: "https://www.movie-map.com/",
          description: "根据您的偏好找到相似的电影。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "JustWatch",
          url: "https://www.justwatch.com/",
          description: "查找和流播电影与电视剧的指导平台。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Business",
      id: "business",
      subcategories: [
        {
          name: "Finance",
          id: "finance",
          websites: [
            {
              name: "FIGR",
              url: "https://www.figr.app/",
              description: "Google Docs + 计算器 = 个人理财",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        },
        {
          name: "Marketing",
          id: "marketing",
          websites: [
            {
              name: "Apollo",
              url: "https://www.apollo.io/",
              description: "销售智能和参与平台，搜索、接触并转化超过6000万家公司的2.5亿多个联系人。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "Crunchbase",
          url: "https://www.crunchbase.com/",
          description: "发现创新公司、初创企业和商业世界关键人物的平台。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Honey",
          url: "https://www.joinhoney.com/",
          description: "在全球超过30,000个网站上自动搜索优惠券的浏览器扩展。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Jobs",
      id: "jobs",
      subcategories: [
        {
          name: "Remote Jobs",
          id: "remote-jobs",
          websites: [
            {
              name: "Remote OK",
              url: "https://remoteok.com/",
              description: "远程职位招聘板。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "We Work Remotely",
              url: "https://weworkremotely.com/",
              description: "各行业远程职位招聘板。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "Y Combinator Jobs",
          url: "https://www.ycombinator.com/jobs",
          description: "发现Y Combinator策划的最佳初创公司工作机会。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "KeyValues",
          url: "https://www.keyvalues.com/",
          description: "寻找与您价值观一致的工程团队。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Design",
      id: "design",
      subcategories: [
        {
          name: "Colors",
          id: "colors",
          websites: [
            {
              name: "Coolors",
              url: "https://coolors.co/",
              description: "配色方案生成器和调色板创建工具。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Adobe Color Wheel",
              url: "https://color.adobe.com/create/color-wheel",
              description: "可用于生成调色板的色轮。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        },
        {
          name: "Icons",
          id: "icons",
          websites: [
            {
              name: "The Noun Project",
              url: "https://thenounproject.com/",
              description: "提供大量免费图标和库存照片的平台。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "FontAwesome",
              url: "https://fontawesome.com/",
              description: "互联网图标库和工具包。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "Canva",
          url: "https://www.canva.com/",
          description: "在线设计工具，用于创建图形、演示文稿等。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Figma",
          url: "https://www.figma.com/",
          description: "协作界面设计工具。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Programming",
      id: "programming",
      subcategories: [
        {
          name: "Learning",
          id: "learning",
          websites: [
            {
              name: "FreeCodeCamp",
              url: "https://www.freecodecamp.org/",
              description: "免费学习编程的平台。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "Codecademy",
              url: "https://www.codecademy.com/",
              description: "交互式编程学习平台。",
              isPaid: false,
              isStudentFriendly: true
            }
          ]
        },
        {
          name: "Practice",
          id: "practice",
          websites: [
            {
              name: "LeetCode",
              url: "https://leetcode.com/",
              description: "编程练习和面试准备平台。",
              isPaid: false,
              isStudentFriendly: false
            },
            {
              name: "HackerRank",
              url: "https://www.hackerrank.com/",
              description: "编程挑战和技能评估平台。",
              isPaid: false,
              isStudentFriendly: false
            }
          ]
        }
      ],
      websites: [
        {
          name: "GitHub",
          url: "https://github.com/",
          description: "代码托管和版本控制平台。",
          isPaid: false,
          isStudentFriendly: true
        },
        {
          name: "Stack Overflow",
          url: "https://stackoverflow.com/",
          description: "程序员问答社区。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Health",
      id: "health",
      subcategories: [],
      websites: [
        {
          name: "MuscleWiki",
          url: "https://musclewiki.com/",
          description: "了解您的身体和肌肉的平台。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Sleep Calculator",
          url: "https://sleepcalculator.com/",
          description: "帮助用户根据期望的起床时间确定最佳就寝时间的工具。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    },
    {
      name: "Fun & Games",
      id: "fun-games",
      subcategories: [],
      websites: [
        {
          name: "Radio Garden",
          url: "https://radio.garden/",
          description: "探索以绿点形式在Google Earth地图上显示的实时广播电台。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Geoguessr",
          url: "https://www.geoguessr.com/",
          description: "基于地理位置的猜测游戏。",
          isPaid: false,
          isStudentFriendly: false
        },
        {
          name: "Quick, Draw!",
          url: "https://quickdraw.withgoogle.com/",
          description: "Google的AI绘画猜测游戏。",
          isPaid: false,
          isStudentFriendly: false
        }
      ]
    }
  ]
};
