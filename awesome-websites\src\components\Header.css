.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px 0;
  text-align: center;
  color: white;
}

.header-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-description {
  font-size: 1.2rem;
  margin: 0 0 30px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.header-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.stat {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

@media (max-width: 768px) {
  .header {
    padding: 30px 0;
  }
  
  .header-title {
    font-size: 2.2rem;
  }
  
  .header-description {
    font-size: 1rem;
    padding: 0 20px;
  }
  
  .header-stats {
    gap: 15px;
  }
  
  .stat {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
}
