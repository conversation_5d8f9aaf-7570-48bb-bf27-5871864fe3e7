(function(){const G=document.createElement("link").relList;if(G&&G.supports&&G.supports("modulepreload"))return;for(const _ of document.querySelectorAll('link[rel="modulepreload"]'))p(_);new MutationObserver(_=>{for(const Y of _)if(Y.type==="childList")for(const V of Y.addedNodes)V.tagName==="LINK"&&V.rel==="modulepreload"&&p(V)}).observe(document,{childList:!0,subtree:!0});function R(_){const Y={};return _.integrity&&(Y.integrity=_.integrity),_.referrerPolicy&&(Y.referrerPolicy=_.referrerPolicy),_.crossOrigin==="use-credentials"?Y.credentials="include":_.crossOrigin==="anonymous"?Y.credentials="omit":Y.credentials="same-origin",Y}function p(_){if(_.ep)return;_.ep=!0;const Y=R(_);fetch(_.href,Y)}})();var Ju={exports:{}},Sl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jc;function Dp(){if(Jc)return Sl;Jc=1;var T=Symbol.for("react.transitional.element"),G=Symbol.for("react.fragment");function R(p,_,Y){var V=null;if(Y!==void 0&&(V=""+Y),_.key!==void 0&&(V=""+_.key),"key"in _){Y={};for(var B in _)B!=="key"&&(Y[B]=_[B])}else Y=_;return _=Y.ref,{$$typeof:T,type:p,key:V,ref:_!==void 0?_:null,props:Y}}return Sl.Fragment=G,Sl.jsx=R,Sl.jsxs=R,Sl}var Wc;function Op(){return Wc||(Wc=1,Ju.exports=Dp()),Ju.exports}var S=Op(),Wu={exports:{}},q={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ic;function zp(){if(Ic)return q;Ic=1;var T=Symbol.for("react.transitional.element"),G=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),p=Symbol.for("react.strict_mode"),_=Symbol.for("react.profiler"),Y=Symbol.for("react.consumer"),V=Symbol.for("react.context"),B=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),L=Symbol.iterator;function J(d){return d===null||typeof d!="object"?null:(d=L&&d[L]||d["@@iterator"],typeof d=="function"?d:null)}var Ae={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Me=Object.assign,ct={};function je(d,P,E){this.props=d,this.context=P,this.refs=ct,this.updater=E||Ae}je.prototype.isReactComponent={},je.prototype.setState=function(d,P){if(typeof d!="object"&&typeof d!="function"&&d!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,d,P,"setState")},je.prototype.forceUpdate=function(d){this.updater.enqueueForceUpdate(this,d,"forceUpdate")};function mi(){}mi.prototype=je.prototype;function Pt(d,P,E){this.props=d,this.context=P,this.refs=ct,this.updater=E||Ae}var xe=Pt.prototype=new mi;xe.constructor=Pt,Me(xe,je.prototype),xe.isPureReactComponent=!0;var ft=Array.isArray,I={H:null,A:null,T:null,S:null,V:null},qe=Object.prototype.hasOwnProperty;function Ye(d,P,E,M,O,$){return E=$.ref,{$$typeof:T,type:d,key:P,ref:E!==void 0?E:null,props:$}}function Ve(d,P){return Ye(d.type,P,void 0,void 0,void 0,d.props)}function yt(d){return typeof d=="object"&&d!==null&&d.$$typeof===T}function Ri(d){var P={"=":"=0",":":"=2"};return"$"+d.replace(/[=:]/g,function(E){return P[E]})}var Tt=/\/+/g;function De(d,P){return typeof d=="object"&&d!==null&&d.key!=null?Ri(""+d.key):P.toString(36)}function pi(){}function hi(d){switch(d.status){case"fulfilled":return d.value;case"rejected":throw d.reason;default:switch(typeof d.status=="string"?d.then(pi,pi):(d.status="pending",d.then(function(P){d.status==="pending"&&(d.status="fulfilled",d.value=P)},function(P){d.status==="pending"&&(d.status="rejected",d.reason=P)})),d.status){case"fulfilled":return d.value;case"rejected":throw d.reason}}throw d}function Oe(d,P,E,M,O){var $=typeof d;($==="undefined"||$==="boolean")&&(d=null);var U=!1;if(d===null)U=!0;else switch($){case"bigint":case"string":case"number":U=!0;break;case"object":switch(d.$$typeof){case T:case G:U=!0;break;case A:return U=d._init,Oe(U(d._payload),P,E,M,O)}}if(U)return O=O(d),U=M===""?"."+De(d,0):M,ft(O)?(E="",U!=null&&(E=U.replace(Tt,"$&/")+"/"),Oe(O,P,E,"",function(Bt){return Bt})):O!=null&&(yt(O)&&(O=Ve(O,E+(O.key==null||d&&d.key===O.key?"":(""+O.key).replace(Tt,"$&/")+"/")+U)),P.push(O)),1;U=0;var Xe=M===""?".":M+":";if(ft(d))for(var oe=0;oe<d.length;oe++)M=d[oe],$=Xe+De(M,oe),U+=Oe(M,P,E,$,O);else if(oe=J(d),typeof oe=="function")for(d=oe.call(d),oe=0;!(M=d.next()).done;)M=M.value,$=Xe+De(M,oe++),U+=Oe(M,P,E,$,O);else if($==="object"){if(typeof d.then=="function")return Oe(hi(d),P,E,M,O);throw P=String(d),Error("Objects are not valid as a React child (found: "+(P==="[object Object]"?"object with keys {"+Object.keys(d).join(", ")+"}":P)+"). If you meant to render a collection of children, use an array instead.")}return U}function w(d,P,E){if(d==null)return d;var M=[],O=0;return Oe(d,M,"","",function($){return P.call(E,$,O++)}),M}function N(d){if(d._status===-1){var P=d._result;P=P(),P.then(function(E){(d._status===0||d._status===-1)&&(d._status=1,d._result=E)},function(E){(d._status===0||d._status===-1)&&(d._status=2,d._result=E)}),d._status===-1&&(d._status=0,d._result=P)}if(d._status===1)return d._result.default;throw d._result}var H=typeof reportError=="function"?reportError:function(d){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var P=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof d=="object"&&d!==null&&typeof d.message=="string"?String(d.message):String(d),error:d});if(!window.dispatchEvent(P))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",d);return}console.error(d)};function ue(){}return q.Children={map:w,forEach:function(d,P,E){w(d,function(){P.apply(this,arguments)},E)},count:function(d){var P=0;return w(d,function(){P++}),P},toArray:function(d){return w(d,function(P){return P})||[]},only:function(d){if(!yt(d))throw Error("React.Children.only expected to receive a single React element child.");return d}},q.Component=je,q.Fragment=R,q.Profiler=_,q.PureComponent=Pt,q.StrictMode=p,q.Suspense=C,q.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=I,q.__COMPILER_RUNTIME={__proto__:null,c:function(d){return I.H.useMemoCache(d)}},q.cache=function(d){return function(){return d.apply(null,arguments)}},q.cloneElement=function(d,P,E){if(d==null)throw Error("The argument must be a React element, but you passed "+d+".");var M=Me({},d.props),O=d.key,$=void 0;if(P!=null)for(U in P.ref!==void 0&&($=void 0),P.key!==void 0&&(O=""+P.key),P)!qe.call(P,U)||U==="key"||U==="__self"||U==="__source"||U==="ref"&&P.ref===void 0||(M[U]=P[U]);var U=arguments.length-2;if(U===1)M.children=E;else if(1<U){for(var Xe=Array(U),oe=0;oe<U;oe++)Xe[oe]=arguments[oe+2];M.children=Xe}return Ye(d.type,O,void 0,void 0,$,M)},q.createContext=function(d){return d={$$typeof:V,_currentValue:d,_currentValue2:d,_threadCount:0,Provider:null,Consumer:null},d.Provider=d,d.Consumer={$$typeof:Y,_context:d},d},q.createElement=function(d,P,E){var M,O={},$=null;if(P!=null)for(M in P.key!==void 0&&($=""+P.key),P)qe.call(P,M)&&M!=="key"&&M!=="__self"&&M!=="__source"&&(O[M]=P[M]);var U=arguments.length-2;if(U===1)O.children=E;else if(1<U){for(var Xe=Array(U),oe=0;oe<U;oe++)Xe[oe]=arguments[oe+2];O.children=Xe}if(d&&d.defaultProps)for(M in U=d.defaultProps,U)O[M]===void 0&&(O[M]=U[M]);return Ye(d,$,void 0,void 0,null,O)},q.createRef=function(){return{current:null}},q.forwardRef=function(d){return{$$typeof:B,render:d}},q.isValidElement=yt,q.lazy=function(d){return{$$typeof:A,_payload:{_status:-1,_result:d},_init:N}},q.memo=function(d,P){return{$$typeof:v,type:d,compare:P===void 0?null:P}},q.startTransition=function(d){var P=I.T,E={};I.T=E;try{var M=d(),O=I.S;O!==null&&O(E,M),typeof M=="object"&&M!==null&&typeof M.then=="function"&&M.then(ue,H)}catch($){H($)}finally{I.T=P}},q.unstable_useCacheRefresh=function(){return I.H.useCacheRefresh()},q.use=function(d){return I.H.use(d)},q.useActionState=function(d,P,E){return I.H.useActionState(d,P,E)},q.useCallback=function(d,P){return I.H.useCallback(d,P)},q.useContext=function(d){return I.H.useContext(d)},q.useDebugValue=function(){},q.useDeferredValue=function(d,P){return I.H.useDeferredValue(d,P)},q.useEffect=function(d,P,E){var M=I.H;if(typeof E=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return M.useEffect(d,P)},q.useId=function(){return I.H.useId()},q.useImperativeHandle=function(d,P,E){return I.H.useImperativeHandle(d,P,E)},q.useInsertionEffect=function(d,P){return I.H.useInsertionEffect(d,P)},q.useLayoutEffect=function(d,P){return I.H.useLayoutEffect(d,P)},q.useMemo=function(d,P){return I.H.useMemo(d,P)},q.useOptimistic=function(d,P){return I.H.useOptimistic(d,P)},q.useReducer=function(d,P,E){return I.H.useReducer(d,P,E)},q.useRef=function(d){return I.H.useRef(d)},q.useState=function(d){return I.H.useState(d)},q.useSyncExternalStore=function(d,P,E){return I.H.useSyncExternalStore(d,P,E)},q.useTransition=function(){return I.H.useTransition()},q.version="19.1.0",q}var $c;function ir(){return $c||($c=1,Wu.exports=zp()),Wu.exports}var Ut=ir(),Iu={exports:{}},gl={},$u={exports:{}},er={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ef;function Rp(){return ef||(ef=1,function(T){function G(w,N){var H=w.length;w.push(N);e:for(;0<H;){var ue=H-1>>>1,d=w[ue];if(0<_(d,N))w[ue]=N,w[H]=d,H=ue;else break e}}function R(w){return w.length===0?null:w[0]}function p(w){if(w.length===0)return null;var N=w[0],H=w.pop();if(H!==N){w[0]=H;e:for(var ue=0,d=w.length,P=d>>>1;ue<P;){var E=2*(ue+1)-1,M=w[E],O=E+1,$=w[O];if(0>_(M,H))O<d&&0>_($,M)?(w[ue]=$,w[O]=H,ue=O):(w[ue]=M,w[E]=H,ue=E);else if(O<d&&0>_($,H))w[ue]=$,w[O]=H,ue=O;else break e}}return N}function _(w,N){var H=w.sortIndex-N.sortIndex;return H!==0?H:w.id-N.id}if(T.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var Y=performance;T.unstable_now=function(){return Y.now()}}else{var V=Date,B=V.now();T.unstable_now=function(){return V.now()-B}}var C=[],v=[],A=1,L=null,J=3,Ae=!1,Me=!1,ct=!1,je=!1,mi=typeof setTimeout=="function"?setTimeout:null,Pt=typeof clearTimeout=="function"?clearTimeout:null,xe=typeof setImmediate<"u"?setImmediate:null;function ft(w){for(var N=R(v);N!==null;){if(N.callback===null)p(v);else if(N.startTime<=w)p(v),N.sortIndex=N.expirationTime,G(C,N);else break;N=R(v)}}function I(w){if(ct=!1,ft(w),!Me)if(R(C)!==null)Me=!0,qe||(qe=!0,De());else{var N=R(v);N!==null&&Oe(I,N.startTime-w)}}var qe=!1,Ye=-1,Ve=5,yt=-1;function Ri(){return je?!0:!(T.unstable_now()-yt<Ve)}function Tt(){if(je=!1,qe){var w=T.unstable_now();yt=w;var N=!0;try{e:{Me=!1,ct&&(ct=!1,Pt(Ye),Ye=-1),Ae=!0;var H=J;try{t:{for(ft(w),L=R(C);L!==null&&!(L.expirationTime>w&&Ri());){var ue=L.callback;if(typeof ue=="function"){L.callback=null,J=L.priorityLevel;var d=ue(L.expirationTime<=w);if(w=T.unstable_now(),typeof d=="function"){L.callback=d,ft(w),N=!0;break t}L===R(C)&&p(C),ft(w)}else p(C);L=R(C)}if(L!==null)N=!0;else{var P=R(v);P!==null&&Oe(I,P.startTime-w),N=!1}}break e}finally{L=null,J=H,Ae=!1}N=void 0}}finally{N?De():qe=!1}}}var De;if(typeof xe=="function")De=function(){xe(Tt)};else if(typeof MessageChannel<"u"){var pi=new MessageChannel,hi=pi.port2;pi.port1.onmessage=Tt,De=function(){hi.postMessage(null)}}else De=function(){mi(Tt,0)};function Oe(w,N){Ye=mi(function(){w(T.unstable_now())},N)}T.unstable_IdlePriority=5,T.unstable_ImmediatePriority=1,T.unstable_LowPriority=4,T.unstable_NormalPriority=3,T.unstable_Profiling=null,T.unstable_UserBlockingPriority=2,T.unstable_cancelCallback=function(w){w.callback=null},T.unstable_forceFrameRate=function(w){0>w||125<w?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Ve=0<w?Math.floor(1e3/w):5},T.unstable_getCurrentPriorityLevel=function(){return J},T.unstable_next=function(w){switch(J){case 1:case 2:case 3:var N=3;break;default:N=J}var H=J;J=N;try{return w()}finally{J=H}},T.unstable_requestPaint=function(){je=!0},T.unstable_runWithPriority=function(w,N){switch(w){case 1:case 2:case 3:case 4:case 5:break;default:w=3}var H=J;J=w;try{return N()}finally{J=H}},T.unstable_scheduleCallback=function(w,N,H){var ue=T.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?ue+H:ue):H=ue,w){case 1:var d=-1;break;case 2:d=250;break;case 5:d=1073741823;break;case 4:d=1e4;break;default:d=5e3}return d=H+d,w={id:A++,callback:N,priorityLevel:w,startTime:H,expirationTime:d,sortIndex:-1},H>ue?(w.sortIndex=H,G(v,w),R(C)===null&&w===R(v)&&(ct?(Pt(Ye),Ye=-1):ct=!0,Oe(I,H-ue))):(w.sortIndex=d,G(C,w),Me||Ae||(Me=!0,qe||(qe=!0,De()))),w},T.unstable_shouldYield=Ri,T.unstable_wrapCallback=function(w){var N=J;return function(){var H=J;J=N;try{return w.apply(this,arguments)}finally{J=H}}}}(er)),er}var tf;function kp(){return tf||(tf=1,$u.exports=Rp()),$u.exports}var tr={exports:{}},ke={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var af;function Hp(){if(af)return ke;af=1;var T=ir();function G(C){var v="https://react.dev/errors/"+C;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var A=2;A<arguments.length;A++)v+="&args[]="+encodeURIComponent(arguments[A])}return"Minified React error #"+C+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function R(){}var p={d:{f:R,r:function(){throw Error(G(522))},D:R,C:R,L:R,m:R,X:R,S:R,M:R},p:0,findDOMNode:null},_=Symbol.for("react.portal");function Y(C,v,A){var L=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:_,key:L==null?null:""+L,children:C,containerInfo:v,implementation:A}}var V=T.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function B(C,v){if(C==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return ke.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=p,ke.createPortal=function(C,v){var A=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(G(299));return Y(C,v,null,A)},ke.flushSync=function(C){var v=V.T,A=p.p;try{if(V.T=null,p.p=2,C)return C()}finally{V.T=v,p.p=A,p.d.f()}},ke.preconnect=function(C,v){typeof C=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,p.d.C(C,v))},ke.prefetchDNS=function(C){typeof C=="string"&&p.d.D(C)},ke.preinit=function(C,v){if(typeof C=="string"&&v&&typeof v.as=="string"){var A=v.as,L=B(A,v.crossOrigin),J=typeof v.integrity=="string"?v.integrity:void 0,Ae=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;A==="style"?p.d.S(C,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:L,integrity:J,fetchPriority:Ae}):A==="script"&&p.d.X(C,{crossOrigin:L,integrity:J,fetchPriority:Ae,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},ke.preinitModule=function(C,v){if(typeof C=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var A=B(v.as,v.crossOrigin);p.d.M(C,{crossOrigin:A,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&p.d.M(C)},ke.preload=function(C,v){if(typeof C=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var A=v.as,L=B(A,v.crossOrigin);p.d.L(C,A,{crossOrigin:L,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},ke.preloadModule=function(C,v){if(typeof C=="string")if(v){var A=B(v.as,v.crossOrigin);p.d.m(C,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:A,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else p.d.m(C)},ke.requestFormReset=function(C){p.d.r(C)},ke.unstable_batchedUpdates=function(C,v){return C(v)},ke.useFormState=function(C,v,A){return V.H.useFormState(C,v,A)},ke.useFormStatus=function(){return V.H.useHostTransitionStatus()},ke.version="19.1.0",ke}var lf;function jp(){if(lf)return tr.exports;lf=1;function T(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(T)}catch(G){console.error(G)}}return T(),tr.exports=Hp(),tr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sf;function Gp(){if(sf)return gl;sf=1;var T=kp(),G=ir(),R=jp();function p(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)t+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function _(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Y(e){var t=e,i=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(i=t.return),e=t.return;while(e)}return t.tag===3?i:null}function V(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function B(e){if(Y(e)!==e)throw Error(p(188))}function C(e){var t=e.alternate;if(!t){if(t=Y(e),t===null)throw Error(p(188));return t!==e?null:e}for(var i=e,a=t;;){var l=i.return;if(l===null)break;var s=l.alternate;if(s===null){if(a=l.return,a!==null){i=a;continue}break}if(l.child===s.child){for(s=l.child;s;){if(s===i)return B(l),e;if(s===a)return B(l),t;s=s.sibling}throw Error(p(188))}if(i.return!==a.return)i=l,a=s;else{for(var n=!1,u=l.child;u;){if(u===i){n=!0,i=l,a=s;break}if(u===a){n=!0,a=l,i=s;break}u=u.sibling}if(!n){for(u=s.child;u;){if(u===i){n=!0,i=s,a=l;break}if(u===a){n=!0,a=s,i=l;break}u=u.sibling}if(!n)throw Error(p(189))}}if(i.alternate!==a)throw Error(p(190))}if(i.tag!==3)throw Error(p(188));return i.stateNode.current===i?e:t}function v(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=v(e),t!==null)return t;e=e.sibling}return null}var A=Object.assign,L=Symbol.for("react.element"),J=Symbol.for("react.transitional.element"),Ae=Symbol.for("react.portal"),Me=Symbol.for("react.fragment"),ct=Symbol.for("react.strict_mode"),je=Symbol.for("react.profiler"),mi=Symbol.for("react.provider"),Pt=Symbol.for("react.consumer"),xe=Symbol.for("react.context"),ft=Symbol.for("react.forward_ref"),I=Symbol.for("react.suspense"),qe=Symbol.for("react.suspense_list"),Ye=Symbol.for("react.memo"),Ve=Symbol.for("react.lazy"),yt=Symbol.for("react.activity"),Ri=Symbol.for("react.memo_cache_sentinel"),Tt=Symbol.iterator;function De(e){return e===null||typeof e!="object"?null:(e=Tt&&e[Tt]||e["@@iterator"],typeof e=="function"?e:null)}var pi=Symbol.for("react.client.reference");function hi(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===pi?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Me:return"Fragment";case je:return"Profiler";case ct:return"StrictMode";case I:return"Suspense";case qe:return"SuspenseList";case yt:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Ae:return"Portal";case xe:return(e.displayName||"Context")+".Provider";case Pt:return(e._context.displayName||"Context")+".Consumer";case ft:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ye:return t=e.displayName||null,t!==null?t:hi(e.type)||"Memo";case Ve:t=e._payload,e=e._init;try{return hi(e(t))}catch{}}return null}var Oe=Array.isArray,w=G.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,N=R.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H={pending:!1,data:null,method:null,action:null},ue=[],d=-1;function P(e){return{current:e}}function E(e){0>d||(e.current=ue[d],ue[d]=null,d--)}function M(e,t){d++,ue[d]=e.current,e.current=t}var O=P(null),$=P(null),U=P(null),Xe=P(null);function oe(e,t){switch(M(U,t),M($,e),M(O,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Tc(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Tc(t),e=Ac(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}E(O),M(O,e)}function Bt(){E(O),E($),E(U)}function Rs(e){e.memoizedState!==null&&M(Xe,e);var t=O.current,i=Ac(t,e.type);t!==i&&(M($,e),M(O,i))}function wl(e){$.current===e&&(E(O),E($)),Xe.current===e&&(E(Xe),fl._currentValue=H)}var ks=Object.prototype.hasOwnProperty,Hs=T.unstable_scheduleCallback,js=T.unstable_cancelCallback,df=T.unstable_shouldYield,of=T.unstable_requestPaint,St=T.unstable_now,cf=T.unstable_getCurrentPriorityLevel,ar=T.unstable_ImmediatePriority,lr=T.unstable_UserBlockingPriority,bl=T.unstable_NormalPriority,ff=T.unstable_LowPriority,sr=T.unstable_IdlePriority,mf=T.log,pf=T.unstable_setDisableYieldValue,ba=null,Qe=null;function Lt(e){if(typeof mf=="function"&&pf(e),Qe&&typeof Qe.setStrictMode=="function")try{Qe.setStrictMode(ba,e)}catch{}}var Ze=Math.clz32?Math.clz32:Sf,hf=Math.log,yf=Math.LN2;function Sf(e){return e>>>=0,e===0?32:31-(hf(e)/yf|0)|0}var vl=256,Fl=4194304;function yi(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Pl(e,t,i){var a=e.pendingLanes;if(a===0)return 0;var l=0,s=e.suspendedLanes,n=e.pingedLanes;e=e.warmLanes;var u=a&134217727;return u!==0?(a=u&~s,a!==0?l=yi(a):(n&=u,n!==0?l=yi(n):i||(i=u&~e,i!==0&&(l=yi(i))))):(u=a&~s,u!==0?l=yi(u):n!==0?l=yi(n):i||(i=a&~e,i!==0&&(l=yi(i)))),l===0?0:t!==0&&t!==l&&(t&s)===0&&(s=l&-l,i=t&-t,s>=i||s===32&&(i&4194048)!==0)?t:l}function va(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function gf(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function nr(){var e=vl;return vl<<=1,(vl&4194048)===0&&(vl=256),e}function ur(){var e=Fl;return Fl<<=1,(Fl&62914560)===0&&(Fl=4194304),e}function Gs(e){for(var t=[],i=0;31>i;i++)t.push(e);return t}function Fa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function wf(e,t,i,a,l,s){var n=e.pendingLanes;e.pendingLanes=i,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=i,e.entangledLanes&=i,e.errorRecoveryDisabledLanes&=i,e.shellSuspendCounter=0;var u=e.entanglements,r=e.expirationTimes,m=e.hiddenUpdates;for(i=n&~i;0<i;){var g=31-Ze(i),F=1<<g;u[g]=0,r[g]=-1;var h=m[g];if(h!==null)for(m[g]=null,g=0;g<h.length;g++){var y=h[g];y!==null&&(y.lane&=-536870913)}i&=~F}a!==0&&rr(e,a,0),s!==0&&l===0&&e.tag!==0&&(e.suspendedLanes|=s&~(n&~t))}function rr(e,t,i){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-Ze(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|i&4194090}function dr(e,t){var i=e.entangledLanes|=t;for(e=e.entanglements;i;){var a=31-Ze(i),l=1<<a;l&t|e[a]&t&&(e[a]|=t),i&=~l}}function _s(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Us(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function or(){var e=N.p;return e!==0?e:(e=window.event,e===void 0?32:Yc(e.type))}function bf(e,t){var i=N.p;try{return N.p=e,t()}finally{N.p=i}}var qt=Math.random().toString(36).slice(2),ze="__reactFiber$"+qt,Ge="__reactProps$"+qt,ki="__reactContainer$"+qt,Bs="__reactEvents$"+qt,vf="__reactListeners$"+qt,Ff="__reactHandles$"+qt,cr="__reactResources$"+qt,Pa="__reactMarker$"+qt;function Ls(e){delete e[ze],delete e[Ge],delete e[Bs],delete e[vf],delete e[Ff]}function Hi(e){var t=e[ze];if(t)return t;for(var i=e.parentNode;i;){if(t=i[ki]||i[ze]){if(i=t.alternate,t.child!==null||i!==null&&i.child!==null)for(e=Ec(e);e!==null;){if(i=e[ze])return i;e=Ec(e)}return t}e=i,i=e.parentNode}return null}function ji(e){if(e=e[ze]||e[ki]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ta(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(p(33))}function Gi(e){var t=e[cr];return t||(t=e[cr]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function ve(e){e[Pa]=!0}var fr=new Set,mr={};function Si(e,t){_i(e,t),_i(e+"Capture",t)}function _i(e,t){for(mr[e]=t,e=0;e<t.length;e++)fr.add(t[e])}var Pf=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),pr={},hr={};function Tf(e){return ks.call(hr,e)?!0:ks.call(pr,e)?!1:Pf.test(e)?hr[e]=!0:(pr[e]=!0,!1)}function Tl(e,t,i){if(Tf(t))if(i===null)e.removeAttribute(t);else{switch(typeof i){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+i)}}function Al(e,t,i){if(i===null)e.removeAttribute(t);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+i)}}function At(e,t,i,a){if(a===null)e.removeAttribute(i);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(i);return}e.setAttributeNS(t,i,""+a)}}var qs,yr;function Ui(e){if(qs===void 0)try{throw Error()}catch(i){var t=i.stack.trim().match(/\n( *(at )?)/);qs=t&&t[1]||"",yr=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+qs+e+yr}var Ys=!1;function Vs(e,t){if(!e||Ys)return"";Ys=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var F=function(){throw Error()};if(Object.defineProperty(F.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(F,[])}catch(y){var h=y}Reflect.construct(e,[],F)}else{try{F.call()}catch(y){h=y}e.call(F.prototype)}}else{try{throw Error()}catch(y){h=y}(F=e())&&typeof F.catch=="function"&&F.catch(function(){})}}catch(y){if(y&&h&&typeof y.stack=="string")return[y.stack,h.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=a.DetermineComponentFrameRoot(),n=s[0],u=s[1];if(n&&u){var r=n.split(`
`),m=u.split(`
`);for(l=a=0;a<r.length&&!r[a].includes("DetermineComponentFrameRoot");)a++;for(;l<m.length&&!m[l].includes("DetermineComponentFrameRoot");)l++;if(a===r.length||l===m.length)for(a=r.length-1,l=m.length-1;1<=a&&0<=l&&r[a]!==m[l];)l--;for(;1<=a&&0<=l;a--,l--)if(r[a]!==m[l]){if(a!==1||l!==1)do if(a--,l--,0>l||r[a]!==m[l]){var g=`
`+r[a].replace(" at new "," at ");return e.displayName&&g.includes("<anonymous>")&&(g=g.replace("<anonymous>",e.displayName)),g}while(1<=a&&0<=l);break}}}finally{Ys=!1,Error.prepareStackTrace=i}return(i=e?e.displayName||e.name:"")?Ui(i):""}function Af(e){switch(e.tag){case 26:case 27:case 5:return Ui(e.type);case 16:return Ui("Lazy");case 13:return Ui("Suspense");case 19:return Ui("SuspenseList");case 0:case 15:return Vs(e.type,!1);case 11:return Vs(e.type.render,!1);case 1:return Vs(e.type,!0);case 31:return Ui("Activity");default:return""}}function Sr(e){try{var t="";do t+=Af(e),e=e.return;while(e);return t}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function it(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function gr(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Mf(e){var t=gr(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var l=i.get,s=i.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(n){a=""+n,s.call(this,n)}}),Object.defineProperty(e,t,{enumerable:i.enumerable}),{getValue:function(){return a},setValue:function(n){a=""+n},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ml(e){e._valueTracker||(e._valueTracker=Mf(e))}function wr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var i=t.getValue(),a="";return e&&(a=gr(e)?e.checked?"true":"false":e.value),e=a,e!==i?(t.setValue(e),!0):!1}function Cl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Cf=/[\n"\\]/g;function at(e){return e.replace(Cf,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Xs(e,t,i,a,l,s,n,u){e.name="",n!=null&&typeof n!="function"&&typeof n!="symbol"&&typeof n!="boolean"?e.type=n:e.removeAttribute("type"),t!=null?n==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+it(t)):e.value!==""+it(t)&&(e.value=""+it(t)):n!=="submit"&&n!=="reset"||e.removeAttribute("value"),t!=null?Qs(e,n,it(t)):i!=null?Qs(e,n,it(i)):a!=null&&e.removeAttribute("value"),l==null&&s!=null&&(e.defaultChecked=!!s),l!=null&&(e.checked=l&&typeof l!="function"&&typeof l!="symbol"),u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?e.name=""+it(u):e.removeAttribute("name")}function br(e,t,i,a,l,s,n,u){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.type=s),t!=null||i!=null){if(!(s!=="submit"&&s!=="reset"||t!=null))return;i=i!=null?""+it(i):"",t=t!=null?""+it(t):i,u||t===e.value||(e.value=t),e.defaultValue=t}a=a??l,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=u?e.checked:!!a,e.defaultChecked=!!a,n!=null&&typeof n!="function"&&typeof n!="symbol"&&typeof n!="boolean"&&(e.name=n)}function Qs(e,t,i){t==="number"&&Cl(e.ownerDocument)===e||e.defaultValue===""+i||(e.defaultValue=""+i)}function Bi(e,t,i,a){if(e=e.options,t){t={};for(var l=0;l<i.length;l++)t["$"+i[l]]=!0;for(i=0;i<e.length;i++)l=t.hasOwnProperty("$"+e[i].value),e[i].selected!==l&&(e[i].selected=l),l&&a&&(e[i].defaultSelected=!0)}else{for(i=""+it(i),t=null,l=0;l<e.length;l++){if(e[l].value===i){e[l].selected=!0,a&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function vr(e,t,i){if(t!=null&&(t=""+it(t),t!==e.value&&(e.value=t),i==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=i!=null?""+it(i):""}function Fr(e,t,i,a){if(t==null){if(a!=null){if(i!=null)throw Error(p(92));if(Oe(a)){if(1<a.length)throw Error(p(93));a=a[0]}i=a}i==null&&(i=""),t=i}i=it(t),e.defaultValue=i,a=e.textContent,a===i&&a!==""&&a!==null&&(e.value=a)}function Li(e,t){if(t){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=t;return}}e.textContent=t}var Nf=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Pr(e,t,i){var a=t.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,i):typeof i!="number"||i===0||Nf.has(t)?t==="float"?e.cssFloat=i:e[t]=(""+i).trim():e[t]=i+"px"}function Tr(e,t,i){if(t!=null&&typeof t!="object")throw Error(p(62));if(e=e.style,i!=null){for(var a in i)!i.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var l in t)a=t[l],t.hasOwnProperty(l)&&i[l]!==a&&Pr(e,l,a)}else for(var s in t)t.hasOwnProperty(s)&&Pr(e,s,t[s])}function Zs(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ef=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),xf=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Nl(e){return xf.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ks=null;function Js(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var qi=null,Yi=null;function Ar(e){var t=ji(e);if(t&&(e=t.stateNode)){var i=e[Ge]||null;e:switch(e=t.stateNode,t.type){case"input":if(Xs(e,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),t=i.name,i.type==="radio"&&t!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+at(""+t)+'"][type="radio"]'),t=0;t<i.length;t++){var a=i[t];if(a!==e&&a.form===e.form){var l=a[Ge]||null;if(!l)throw Error(p(90));Xs(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<i.length;t++)a=i[t],a.form===e.form&&wr(a)}break e;case"textarea":vr(e,i.value,i.defaultValue);break e;case"select":t=i.value,t!=null&&Bi(e,!!i.multiple,t,!1)}}}var Ws=!1;function Mr(e,t,i){if(Ws)return e(t,i);Ws=!0;try{var a=e(t);return a}finally{if(Ws=!1,(qi!==null||Yi!==null)&&(ms(),qi&&(t=qi,e=Yi,Yi=qi=null,Ar(t),e)))for(t=0;t<e.length;t++)Ar(e[t])}}function Aa(e,t){var i=e.stateNode;if(i===null)return null;var a=i[Ge]||null;if(a===null)return null;i=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(p(231,t,typeof i));return i}var Mt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Is=!1;if(Mt)try{var Ma={};Object.defineProperty(Ma,"passive",{get:function(){Is=!0}}),window.addEventListener("test",Ma,Ma),window.removeEventListener("test",Ma,Ma)}catch{Is=!1}var Yt=null,$s=null,El=null;function Cr(){if(El)return El;var e,t=$s,i=t.length,a,l="value"in Yt?Yt.value:Yt.textContent,s=l.length;for(e=0;e<i&&t[e]===l[e];e++);var n=i-e;for(a=1;a<=n&&t[i-a]===l[s-a];a++);return El=l.slice(e,1<a?1-a:void 0)}function xl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Dl(){return!0}function Nr(){return!1}function _e(e){function t(i,a,l,s,n){this._reactName=i,this._targetInst=l,this.type=a,this.nativeEvent=s,this.target=n,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(i=e[u],this[u]=i?i(s):s[u]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Dl:Nr,this.isPropagationStopped=Nr,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=Dl)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=Dl)},persist:function(){},isPersistent:Dl}),t}var gi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ol=_e(gi),Ca=A({},gi,{view:0,detail:0}),Df=_e(Ca),en,tn,Na,zl=A({},Ca,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ln,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Na&&(Na&&e.type==="mousemove"?(en=e.screenX-Na.screenX,tn=e.screenY-Na.screenY):tn=en=0,Na=e),en)},movementY:function(e){return"movementY"in e?e.movementY:tn}}),Er=_e(zl),Of=A({},zl,{dataTransfer:0}),zf=_e(Of),Rf=A({},Ca,{relatedTarget:0}),an=_e(Rf),kf=A({},gi,{animationName:0,elapsedTime:0,pseudoElement:0}),Hf=_e(kf),jf=A({},gi,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Gf=_e(jf),_f=A({},gi,{data:0}),xr=_e(_f),Uf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Lf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function qf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Lf[e])?!!t[e]:!1}function ln(){return qf}var Yf=A({},Ca,{key:function(e){if(e.key){var t=Uf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=xl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ln,charCode:function(e){return e.type==="keypress"?xl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Vf=_e(Yf),Xf=A({},zl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Dr=_e(Xf),Qf=A({},Ca,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ln}),Zf=_e(Qf),Kf=A({},gi,{propertyName:0,elapsedTime:0,pseudoElement:0}),Jf=_e(Kf),Wf=A({},zl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),If=_e(Wf),$f=A({},gi,{newState:0,oldState:0}),em=_e($f),tm=[9,13,27,32],sn=Mt&&"CompositionEvent"in window,Ea=null;Mt&&"documentMode"in document&&(Ea=document.documentMode);var im=Mt&&"TextEvent"in window&&!Ea,Or=Mt&&(!sn||Ea&&8<Ea&&11>=Ea),zr=" ",Rr=!1;function kr(e,t){switch(e){case"keyup":return tm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hr(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vi=!1;function am(e,t){switch(e){case"compositionend":return Hr(t);case"keypress":return t.which!==32?null:(Rr=!0,zr);case"textInput":return e=t.data,e===zr&&Rr?null:e;default:return null}}function lm(e,t){if(Vi)return e==="compositionend"||!sn&&kr(e,t)?(e=Cr(),El=$s=Yt=null,Vi=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Or&&t.locale!=="ko"?null:t.data;default:return null}}var sm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function jr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!sm[e.type]:t==="textarea"}function Gr(e,t,i,a){qi?Yi?Yi.push(a):Yi=[a]:qi=a,t=ws(t,"onChange"),0<t.length&&(i=new Ol("onChange","change",null,i,a),e.push({event:i,listeners:t}))}var xa=null,Da=null;function nm(e){wc(e,0)}function Rl(e){var t=Ta(e);if(wr(t))return e}function _r(e,t){if(e==="change")return t}var Ur=!1;if(Mt){var nn;if(Mt){var un="oninput"in document;if(!un){var Br=document.createElement("div");Br.setAttribute("oninput","return;"),un=typeof Br.oninput=="function"}nn=un}else nn=!1;Ur=nn&&(!document.documentMode||9<document.documentMode)}function Lr(){xa&&(xa.detachEvent("onpropertychange",qr),Da=xa=null)}function qr(e){if(e.propertyName==="value"&&Rl(Da)){var t=[];Gr(t,Da,e,Js(e)),Mr(nm,t)}}function um(e,t,i){e==="focusin"?(Lr(),xa=t,Da=i,xa.attachEvent("onpropertychange",qr)):e==="focusout"&&Lr()}function rm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Rl(Da)}function dm(e,t){if(e==="click")return Rl(t)}function om(e,t){if(e==="input"||e==="change")return Rl(t)}function cm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ke=typeof Object.is=="function"?Object.is:cm;function Oa(e,t){if(Ke(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var i=Object.keys(e),a=Object.keys(t);if(i.length!==a.length)return!1;for(a=0;a<i.length;a++){var l=i[a];if(!ks.call(t,l)||!Ke(e[l],t[l]))return!1}return!0}function Yr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vr(e,t){var i=Yr(e);e=0;for(var a;i;){if(i.nodeType===3){if(a=e+i.textContent.length,e<=t&&a>=t)return{node:i,offset:t-e};e=a}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Yr(i)}}function Xr(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Xr(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Qr(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Cl(e.document);t instanceof e.HTMLIFrameElement;){try{var i=typeof t.contentWindow.location.href=="string"}catch{i=!1}if(i)e=t.contentWindow;else break;t=Cl(e.document)}return t}function rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var fm=Mt&&"documentMode"in document&&11>=document.documentMode,Xi=null,dn=null,za=null,on=!1;function Zr(e,t,i){var a=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;on||Xi==null||Xi!==Cl(a)||(a=Xi,"selectionStart"in a&&rn(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),za&&Oa(za,a)||(za=a,a=ws(dn,"onSelect"),0<a.length&&(t=new Ol("onSelect","select",null,t,i),e.push({event:t,listeners:a}),t.target=Xi)))}function wi(e,t){var i={};return i[e.toLowerCase()]=t.toLowerCase(),i["Webkit"+e]="webkit"+t,i["Moz"+e]="moz"+t,i}var Qi={animationend:wi("Animation","AnimationEnd"),animationiteration:wi("Animation","AnimationIteration"),animationstart:wi("Animation","AnimationStart"),transitionrun:wi("Transition","TransitionRun"),transitionstart:wi("Transition","TransitionStart"),transitioncancel:wi("Transition","TransitionCancel"),transitionend:wi("Transition","TransitionEnd")},cn={},Kr={};Mt&&(Kr=document.createElement("div").style,"AnimationEvent"in window||(delete Qi.animationend.animation,delete Qi.animationiteration.animation,delete Qi.animationstart.animation),"TransitionEvent"in window||delete Qi.transitionend.transition);function bi(e){if(cn[e])return cn[e];if(!Qi[e])return e;var t=Qi[e],i;for(i in t)if(t.hasOwnProperty(i)&&i in Kr)return cn[e]=t[i];return e}var Jr=bi("animationend"),Wr=bi("animationiteration"),Ir=bi("animationstart"),mm=bi("transitionrun"),pm=bi("transitionstart"),hm=bi("transitioncancel"),$r=bi("transitionend"),ed=new Map,fn="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");fn.push("scrollEnd");function mt(e,t){ed.set(e,t),Si(t,[e])}var td=new WeakMap;function lt(e,t){if(typeof e=="object"&&e!==null){var i=td.get(e);return i!==void 0?i:(t={value:e,source:t,stack:Sr(t)},td.set(e,t),t)}return{value:e,source:t,stack:Sr(t)}}var st=[],Zi=0,mn=0;function kl(){for(var e=Zi,t=mn=Zi=0;t<e;){var i=st[t];st[t++]=null;var a=st[t];st[t++]=null;var l=st[t];st[t++]=null;var s=st[t];if(st[t++]=null,a!==null&&l!==null){var n=a.pending;n===null?l.next=l:(l.next=n.next,n.next=l),a.pending=l}s!==0&&id(i,l,s)}}function Hl(e,t,i,a){st[Zi++]=e,st[Zi++]=t,st[Zi++]=i,st[Zi++]=a,mn|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function pn(e,t,i,a){return Hl(e,t,i,a),jl(e)}function Ki(e,t){return Hl(e,null,null,t),jl(e)}function id(e,t,i){e.lanes|=i;var a=e.alternate;a!==null&&(a.lanes|=i);for(var l=!1,s=e.return;s!==null;)s.childLanes|=i,a=s.alternate,a!==null&&(a.childLanes|=i),s.tag===22&&(e=s.stateNode,e===null||e._visibility&1||(l=!0)),e=s,s=s.return;return e.tag===3?(s=e.stateNode,l&&t!==null&&(l=31-Ze(i),e=s.hiddenUpdates,a=e[l],a===null?e[l]=[t]:a.push(t),t.lane=i|536870912),s):null}function jl(e){if(50<ll)throw ll=0,bu=null,Error(p(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Ji={};function ym(e,t,i,a){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Je(e,t,i,a){return new ym(e,t,i,a)}function hn(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ct(e,t){var i=e.alternate;return i===null?(i=Je(e.tag,t,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=t,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&65011712,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,t=e.dependencies,i.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i.refCleanup=e.refCleanup,i}function ad(e,t){e.flags&=65011714;var i=e.alternate;return i===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=i.childLanes,e.lanes=i.lanes,e.child=i.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=i.memoizedProps,e.memoizedState=i.memoizedState,e.updateQueue=i.updateQueue,e.type=i.type,t=i.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Gl(e,t,i,a,l,s){var n=0;if(a=e,typeof e=="function")hn(e)&&(n=1);else if(typeof e=="string")n=gp(e,i,O.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case yt:return e=Je(31,i,t,l),e.elementType=yt,e.lanes=s,e;case Me:return vi(i.children,l,s,t);case ct:n=8,l|=24;break;case je:return e=Je(12,i,t,l|2),e.elementType=je,e.lanes=s,e;case I:return e=Je(13,i,t,l),e.elementType=I,e.lanes=s,e;case qe:return e=Je(19,i,t,l),e.elementType=qe,e.lanes=s,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case mi:case xe:n=10;break e;case Pt:n=9;break e;case ft:n=11;break e;case Ye:n=14;break e;case Ve:n=16,a=null;break e}n=29,i=Error(p(130,e===null?"null":typeof e,"")),a=null}return t=Je(n,i,t,l),t.elementType=e,t.type=a,t.lanes=s,t}function vi(e,t,i,a){return e=Je(7,e,a,t),e.lanes=i,e}function yn(e,t,i){return e=Je(6,e,null,t),e.lanes=i,e}function Sn(e,t,i){return t=Je(4,e.children!==null?e.children:[],e.key,t),t.lanes=i,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wi=[],Ii=0,_l=null,Ul=0,nt=[],ut=0,Fi=null,Nt=1,Et="";function Pi(e,t){Wi[Ii++]=Ul,Wi[Ii++]=_l,_l=e,Ul=t}function ld(e,t,i){nt[ut++]=Nt,nt[ut++]=Et,nt[ut++]=Fi,Fi=e;var a=Nt;e=Et;var l=32-Ze(a)-1;a&=~(1<<l),i+=1;var s=32-Ze(t)+l;if(30<s){var n=l-l%5;s=(a&(1<<n)-1).toString(32),a>>=n,l-=n,Nt=1<<32-Ze(t)+l|i<<l|a,Et=s+e}else Nt=1<<s|i<<l|a,Et=e}function gn(e){e.return!==null&&(Pi(e,1),ld(e,1,0))}function wn(e){for(;e===_l;)_l=Wi[--Ii],Wi[Ii]=null,Ul=Wi[--Ii],Wi[Ii]=null;for(;e===Fi;)Fi=nt[--ut],nt[ut]=null,Et=nt[--ut],nt[ut]=null,Nt=nt[--ut],nt[ut]=null}var He=null,me=null,te=!1,Ti=null,gt=!1,bn=Error(p(519));function Ai(e){var t=Error(p(418,""));throw Ha(lt(t,e)),bn}function sd(e){var t=e.stateNode,i=e.type,a=e.memoizedProps;switch(t[ze]=e,t[Ge]=a,i){case"dialog":K("cancel",t),K("close",t);break;case"iframe":case"object":case"embed":K("load",t);break;case"video":case"audio":for(i=0;i<nl.length;i++)K(nl[i],t);break;case"source":K("error",t);break;case"img":case"image":case"link":K("error",t),K("load",t);break;case"details":K("toggle",t);break;case"input":K("invalid",t),br(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Ml(t);break;case"select":K("invalid",t);break;case"textarea":K("invalid",t),Fr(t,a.value,a.defaultValue,a.children),Ml(t)}i=a.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||t.textContent===""+i||a.suppressHydrationWarning===!0||Pc(t.textContent,i)?(a.popover!=null&&(K("beforetoggle",t),K("toggle",t)),a.onScroll!=null&&K("scroll",t),a.onScrollEnd!=null&&K("scrollend",t),a.onClick!=null&&(t.onclick=bs),t=!0):t=!1,t||Ai(e)}function nd(e){for(He=e.return;He;)switch(He.tag){case 5:case 13:gt=!1;return;case 27:case 3:gt=!0;return;default:He=He.return}}function Ra(e){if(e!==He)return!1;if(!te)return nd(e),te=!0,!1;var t=e.tag,i;if((i=t!==3&&t!==27)&&((i=t===5)&&(i=e.type,i=!(i!=="form"&&i!=="button")||Hu(e.type,e.memoizedProps)),i=!i),i&&me&&Ai(e),nd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(p(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(i=e.data,i==="/$"){if(t===0){me=ht(e.nextSibling);break e}t--}else i!=="$"&&i!=="$!"&&i!=="$?"||t++;e=e.nextSibling}me=null}}else t===27?(t=me,ni(e.type)?(e=Uu,Uu=null,me=e):me=t):me=He?ht(e.stateNode.nextSibling):null;return!0}function ka(){me=He=null,te=!1}function ud(){var e=Ti;return e!==null&&(Le===null?Le=e:Le.push.apply(Le,e),Ti=null),e}function Ha(e){Ti===null?Ti=[e]:Ti.push(e)}var vn=P(null),Mi=null,xt=null;function Vt(e,t,i){M(vn,t._currentValue),t._currentValue=i}function Dt(e){e._currentValue=vn.current,E(vn)}function Fn(e,t,i){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===i)break;e=e.return}}function Pn(e,t,i,a){var l=e.child;for(l!==null&&(l.return=e);l!==null;){var s=l.dependencies;if(s!==null){var n=l.child;s=s.firstContext;e:for(;s!==null;){var u=s;s=l;for(var r=0;r<t.length;r++)if(u.context===t[r]){s.lanes|=i,u=s.alternate,u!==null&&(u.lanes|=i),Fn(s.return,i,e),a||(n=null);break e}s=u.next}}else if(l.tag===18){if(n=l.return,n===null)throw Error(p(341));n.lanes|=i,s=n.alternate,s!==null&&(s.lanes|=i),Fn(n,i,e),n=null}else n=l.child;if(n!==null)n.return=l;else for(n=l;n!==null;){if(n===e){n=null;break}if(l=n.sibling,l!==null){l.return=n.return,n=l;break}n=n.return}l=n}}function ja(e,t,i,a){e=null;for(var l=t,s=!1;l!==null;){if(!s){if((l.flags&524288)!==0)s=!0;else if((l.flags&262144)!==0)break}if(l.tag===10){var n=l.alternate;if(n===null)throw Error(p(387));if(n=n.memoizedProps,n!==null){var u=l.type;Ke(l.pendingProps.value,n.value)||(e!==null?e.push(u):e=[u])}}else if(l===Xe.current){if(n=l.alternate,n===null)throw Error(p(387));n.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(e!==null?e.push(fl):e=[fl])}l=l.return}e!==null&&Pn(t,e,i,a),t.flags|=262144}function Bl(e){for(e=e.firstContext;e!==null;){if(!Ke(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ci(e){Mi=e,xt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Re(e){return rd(Mi,e)}function Ll(e,t){return Mi===null&&Ci(e),rd(e,t)}function rd(e,t){var i=t._currentValue;if(t={context:t,memoizedValue:i,next:null},xt===null){if(e===null)throw Error(p(308));xt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else xt=xt.next=t;return i}var Sm=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(i,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(i){return i()})}},gm=T.unstable_scheduleCallback,wm=T.unstable_NormalPriority,we={$$typeof:xe,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Tn(){return{controller:new Sm,data:new Map,refCount:0}}function Ga(e){e.refCount--,e.refCount===0&&gm(wm,function(){e.controller.abort()})}var _a=null,An=0,$i=0,ea=null;function bm(e,t){if(_a===null){var i=_a=[];An=0,$i=Cu(),ea={status:"pending",value:void 0,then:function(a){i.push(a)}}}return An++,t.then(dd,dd),t}function dd(){if(--An===0&&_a!==null){ea!==null&&(ea.status="fulfilled");var e=_a;_a=null,$i=0,ea=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function vm(e,t){var i=[],a={status:"pending",value:null,reason:null,then:function(l){i.push(l)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var l=0;l<i.length;l++)(0,i[l])(t)},function(l){for(a.status="rejected",a.reason=l,l=0;l<i.length;l++)(0,i[l])(void 0)}),a}var od=w.S;w.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&bm(e,t),od!==null&&od(e,t)};var Ni=P(null);function Mn(){var e=Ni.current;return e!==null?e:de.pooledCache}function ql(e,t){t===null?M(Ni,Ni.current):M(Ni,t.pool)}function cd(){var e=Mn();return e===null?null:{parent:we._currentValue,pool:e}}var Ua=Error(p(460)),fd=Error(p(474)),Yl=Error(p(542)),Cn={then:function(){}};function md(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Vl(){}function pd(e,t,i){switch(i=e[i],i===void 0?e.push(t):i!==t&&(t.then(Vl,Vl),t=i),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,yd(e),e;default:if(typeof t.status=="string")t.then(Vl,Vl);else{if(e=de,e!==null&&100<e.shellSuspendCounter)throw Error(p(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var l=t;l.status="fulfilled",l.value=a}},function(a){if(t.status==="pending"){var l=t;l.status="rejected",l.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,yd(e),e}throw Ba=t,Ua}}var Ba=null;function hd(){if(Ba===null)throw Error(p(459));var e=Ba;return Ba=null,e}function yd(e){if(e===Ua||e===Yl)throw Error(p(483))}var Xt=!1;function Nn(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function En(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Qt(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Zt(e,t,i){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(ie&2)!==0){var l=a.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),a.pending=t,t=jl(e),id(e,null,i),t}return Hl(e,a,t,i),jl(e)}function La(e,t,i){if(t=t.updateQueue,t!==null&&(t=t.shared,(i&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,i|=a,t.lanes=i,dr(e,i)}}function xn(e,t){var i=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,i===a)){var l=null,s=null;if(i=i.firstBaseUpdate,i!==null){do{var n={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};s===null?l=s=n:s=s.next=n,i=i.next}while(i!==null);s===null?l=s=t:s=s.next=t}else l=s=t;i={baseState:a.baseState,firstBaseUpdate:l,lastBaseUpdate:s,shared:a.shared,callbacks:a.callbacks},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=t:e.next=t,i.lastBaseUpdate=t}var Dn=!1;function qa(){if(Dn){var e=ea;if(e!==null)throw e}}function Ya(e,t,i,a){Dn=!1;var l=e.updateQueue;Xt=!1;var s=l.firstBaseUpdate,n=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var r=u,m=r.next;r.next=null,n===null?s=m:n.next=m,n=r;var g=e.alternate;g!==null&&(g=g.updateQueue,u=g.lastBaseUpdate,u!==n&&(u===null?g.firstBaseUpdate=m:u.next=m,g.lastBaseUpdate=r))}if(s!==null){var F=l.baseState;n=0,g=m=r=null,u=s;do{var h=u.lane&-536870913,y=h!==u.lane;if(y?(W&h)===h:(a&h)===h){h!==0&&h===$i&&(Dn=!0),g!==null&&(g=g.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});e:{var j=e,z=u;h=t;var ne=i;switch(z.tag){case 1:if(j=z.payload,typeof j=="function"){F=j.call(ne,F,h);break e}F=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=z.payload,h=typeof j=="function"?j.call(ne,F,h):j,h==null)break e;F=A({},F,h);break e;case 2:Xt=!0}}h=u.callback,h!==null&&(e.flags|=64,y&&(e.flags|=8192),y=l.callbacks,y===null?l.callbacks=[h]:y.push(h))}else y={lane:h,tag:u.tag,payload:u.payload,callback:u.callback,next:null},g===null?(m=g=y,r=F):g=g.next=y,n|=h;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;y=u,u=y.next,y.next=null,l.lastBaseUpdate=y,l.shared.pending=null}}while(!0);g===null&&(r=F),l.baseState=r,l.firstBaseUpdate=m,l.lastBaseUpdate=g,s===null&&(l.shared.lanes=0),ii|=n,e.lanes=n,e.memoizedState=F}}function Sd(e,t){if(typeof e!="function")throw Error(p(191,e));e.call(t)}function gd(e,t){var i=e.callbacks;if(i!==null)for(e.callbacks=null,e=0;e<i.length;e++)Sd(i[e],t)}var ta=P(null),Xl=P(0);function wd(e,t){e=Gt,M(Xl,e),M(ta,t),Gt=e|t.baseLanes}function On(){M(Xl,Gt),M(ta,ta.current)}function zn(){Gt=Xl.current,E(ta),E(Xl)}var Kt=0,X=null,le=null,Se=null,Ql=!1,ia=!1,Ei=!1,Zl=0,Va=0,aa=null,Fm=0;function he(){throw Error(p(321))}function Rn(e,t){if(t===null)return!1;for(var i=0;i<t.length&&i<e.length;i++)if(!Ke(e[i],t[i]))return!1;return!0}function kn(e,t,i,a,l,s){return Kt=s,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,w.H=e===null||e.memoizedState===null?io:ao,Ei=!1,s=i(a,l),Ei=!1,ia&&(s=vd(t,i,a,l)),bd(e),s}function bd(e){w.H=es;var t=le!==null&&le.next!==null;if(Kt=0,Se=le=X=null,Ql=!1,Va=0,aa=null,t)throw Error(p(300));e===null||Fe||(e=e.dependencies,e!==null&&Bl(e)&&(Fe=!0))}function vd(e,t,i,a){X=e;var l=0;do{if(ia&&(aa=null),Va=0,ia=!1,25<=l)throw Error(p(301));if(l+=1,Se=le=null,e.updateQueue!=null){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}w.H=Em,s=t(i,a)}while(ia);return s}function Pm(){var e=w.H,t=e.useState()[0];return t=typeof t.then=="function"?Xa(t):t,e=e.useState()[0],(le!==null?le.memoizedState:null)!==e&&(X.flags|=1024),t}function Hn(){var e=Zl!==0;return Zl=0,e}function jn(e,t,i){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i}function Gn(e){if(Ql){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ql=!1}Kt=0,Se=le=X=null,ia=!1,Va=Zl=0,aa=null}function Ue(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Se===null?X.memoizedState=Se=e:Se=Se.next=e,Se}function ge(){if(le===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=le.next;var t=Se===null?X.memoizedState:Se.next;if(t!==null)Se=t,le=e;else{if(e===null)throw X.alternate===null?Error(p(467)):Error(p(310));le=e,e={memoizedState:le.memoizedState,baseState:le.baseState,baseQueue:le.baseQueue,queue:le.queue,next:null},Se===null?X.memoizedState=Se=e:Se=Se.next=e}return Se}function _n(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Xa(e){var t=Va;return Va+=1,aa===null&&(aa=[]),e=pd(aa,e,t),t=X,(Se===null?t.memoizedState:Se.next)===null&&(t=t.alternate,w.H=t===null||t.memoizedState===null?io:ao),e}function Kl(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Xa(e);if(e.$$typeof===xe)return Re(e)}throw Error(p(438,String(e)))}function Un(e){var t=null,i=X.updateQueue;if(i!==null&&(t=i.memoCache),t==null){var a=X.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(l){return l.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),i===null&&(i=_n(),X.updateQueue=i),i.memoCache=t,i=t.data[t.index],i===void 0)for(i=t.data[t.index]=Array(e),a=0;a<e;a++)i[a]=Ri;return t.index++,i}function Ot(e,t){return typeof t=="function"?t(e):t}function Jl(e){var t=ge();return Bn(t,le,e)}function Bn(e,t,i){var a=e.queue;if(a===null)throw Error(p(311));a.lastRenderedReducer=i;var l=e.baseQueue,s=a.pending;if(s!==null){if(l!==null){var n=l.next;l.next=s.next,s.next=n}t.baseQueue=l=s,a.pending=null}if(s=e.baseState,l===null)e.memoizedState=s;else{t=l.next;var u=n=null,r=null,m=t,g=!1;do{var F=m.lane&-536870913;if(F!==m.lane?(W&F)===F:(Kt&F)===F){var h=m.revertLane;if(h===0)r!==null&&(r=r.next={lane:0,revertLane:0,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null}),F===$i&&(g=!0);else if((Kt&h)===h){m=m.next,h===$i&&(g=!0);continue}else F={lane:0,revertLane:m.revertLane,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null},r===null?(u=r=F,n=s):r=r.next=F,X.lanes|=h,ii|=h;F=m.action,Ei&&i(s,F),s=m.hasEagerState?m.eagerState:i(s,F)}else h={lane:F,revertLane:m.revertLane,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null},r===null?(u=r=h,n=s):r=r.next=h,X.lanes|=F,ii|=F;m=m.next}while(m!==null&&m!==t);if(r===null?n=s:r.next=u,!Ke(s,e.memoizedState)&&(Fe=!0,g&&(i=ea,i!==null)))throw i;e.memoizedState=s,e.baseState=n,e.baseQueue=r,a.lastRenderedState=s}return l===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Ln(e){var t=ge(),i=t.queue;if(i===null)throw Error(p(311));i.lastRenderedReducer=e;var a=i.dispatch,l=i.pending,s=t.memoizedState;if(l!==null){i.pending=null;var n=l=l.next;do s=e(s,n.action),n=n.next;while(n!==l);Ke(s,t.memoizedState)||(Fe=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),i.lastRenderedState=s}return[s,a]}function Fd(e,t,i){var a=X,l=ge(),s=te;if(s){if(i===void 0)throw Error(p(407));i=i()}else i=t();var n=!Ke((le||l).memoizedState,i);n&&(l.memoizedState=i,Fe=!0),l=l.queue;var u=Ad.bind(null,a,l,e);if(Qa(2048,8,u,[e]),l.getSnapshot!==t||n||Se!==null&&Se.memoizedState.tag&1){if(a.flags|=2048,la(9,Wl(),Td.bind(null,a,l,i,t),null),de===null)throw Error(p(349));s||(Kt&124)!==0||Pd(a,t,i)}return i}function Pd(e,t,i){e.flags|=16384,e={getSnapshot:t,value:i},t=X.updateQueue,t===null?(t=_n(),X.updateQueue=t,t.stores=[e]):(i=t.stores,i===null?t.stores=[e]:i.push(e))}function Td(e,t,i,a){t.value=i,t.getSnapshot=a,Md(t)&&Cd(e)}function Ad(e,t,i){return i(function(){Md(t)&&Cd(e)})}function Md(e){var t=e.getSnapshot;e=e.value;try{var i=t();return!Ke(e,i)}catch{return!0}}function Cd(e){var t=Ki(e,2);t!==null&&tt(t,e,2)}function qn(e){var t=Ue();if(typeof e=="function"){var i=e;if(e=i(),Ei){Lt(!0);try{i()}finally{Lt(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ot,lastRenderedState:e},t}function Nd(e,t,i,a){return e.baseState=i,Bn(e,le,typeof a=="function"?a:Ot)}function Tm(e,t,i,a,l){if($l(e))throw Error(p(485));if(e=t.action,e!==null){var s={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(n){s.listeners.push(n)}};w.T!==null?i(!0):s.isTransition=!1,a(s),i=t.pending,i===null?(s.next=t.pending=s,Ed(t,s)):(s.next=i.next,t.pending=i.next=s)}}function Ed(e,t){var i=t.action,a=t.payload,l=e.state;if(t.isTransition){var s=w.T,n={};w.T=n;try{var u=i(l,a),r=w.S;r!==null&&r(n,u),xd(e,t,u)}catch(m){Yn(e,t,m)}finally{w.T=s}}else try{s=i(l,a),xd(e,t,s)}catch(m){Yn(e,t,m)}}function xd(e,t,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(a){Dd(e,t,a)},function(a){return Yn(e,t,a)}):Dd(e,t,i)}function Dd(e,t,i){t.status="fulfilled",t.value=i,Od(t),e.state=i,t=e.pending,t!==null&&(i=t.next,i===t?e.pending=null:(i=i.next,t.next=i,Ed(e,i)))}function Yn(e,t,i){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=i,Od(t),t=t.next;while(t!==a)}e.action=null}function Od(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function zd(e,t){return t}function Rd(e,t){if(te){var i=de.formState;if(i!==null){e:{var a=X;if(te){if(me){t:{for(var l=me,s=gt;l.nodeType!==8;){if(!s){l=null;break t}if(l=ht(l.nextSibling),l===null){l=null;break t}}s=l.data,l=s==="F!"||s==="F"?l:null}if(l){me=ht(l.nextSibling),a=l.data==="F!";break e}}Ai(a)}a=!1}a&&(t=i[0])}}return i=Ue(),i.memoizedState=i.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:zd,lastRenderedState:t},i.queue=a,i=$d.bind(null,X,a),a.dispatch=i,a=qn(!1),s=Kn.bind(null,X,!1,a.queue),a=Ue(),l={state:t,dispatch:null,action:e,pending:null},a.queue=l,i=Tm.bind(null,X,l,s,i),l.dispatch=i,a.memoizedState=e,[t,i,!1]}function kd(e){var t=ge();return Hd(t,le,e)}function Hd(e,t,i){if(t=Bn(e,t,zd)[0],e=Jl(Ot)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Xa(t)}catch(n){throw n===Ua?Yl:n}else a=t;t=ge();var l=t.queue,s=l.dispatch;return i!==t.memoizedState&&(X.flags|=2048,la(9,Wl(),Am.bind(null,l,i),null)),[a,s,e]}function Am(e,t){e.action=t}function jd(e){var t=ge(),i=le;if(i!==null)return Hd(t,i,e);ge(),t=t.memoizedState,i=ge();var a=i.queue.dispatch;return i.memoizedState=e,[t,a,!1]}function la(e,t,i,a){return e={tag:e,create:i,deps:a,inst:t,next:null},t=X.updateQueue,t===null&&(t=_n(),X.updateQueue=t),i=t.lastEffect,i===null?t.lastEffect=e.next=e:(a=i.next,i.next=e,e.next=a,t.lastEffect=e),e}function Wl(){return{destroy:void 0,resource:void 0}}function Gd(){return ge().memoizedState}function Il(e,t,i,a){var l=Ue();a=a===void 0?null:a,X.flags|=e,l.memoizedState=la(1|t,Wl(),i,a)}function Qa(e,t,i,a){var l=ge();a=a===void 0?null:a;var s=l.memoizedState.inst;le!==null&&a!==null&&Rn(a,le.memoizedState.deps)?l.memoizedState=la(t,s,i,a):(X.flags|=e,l.memoizedState=la(1|t,s,i,a))}function _d(e,t){Il(8390656,8,e,t)}function Ud(e,t){Qa(2048,8,e,t)}function Bd(e,t){return Qa(4,2,e,t)}function Ld(e,t){return Qa(4,4,e,t)}function qd(e,t){if(typeof t=="function"){e=e();var i=t(e);return function(){typeof i=="function"?i():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Yd(e,t,i){i=i!=null?i.concat([e]):null,Qa(4,4,qd.bind(null,t,e),i)}function Vn(){}function Vd(e,t){var i=ge();t=t===void 0?null:t;var a=i.memoizedState;return t!==null&&Rn(t,a[1])?a[0]:(i.memoizedState=[e,t],e)}function Xd(e,t){var i=ge();t=t===void 0?null:t;var a=i.memoizedState;if(t!==null&&Rn(t,a[1]))return a[0];if(a=e(),Ei){Lt(!0);try{e()}finally{Lt(!1)}}return i.memoizedState=[a,t],a}function Xn(e,t,i){return i===void 0||(Kt&1073741824)!==0?e.memoizedState=t:(e.memoizedState=i,e=Jo(),X.lanes|=e,ii|=e,i)}function Qd(e,t,i,a){return Ke(i,t)?i:ta.current!==null?(e=Xn(e,i,a),Ke(e,t)||(Fe=!0),e):(Kt&42)===0?(Fe=!0,e.memoizedState=i):(e=Jo(),X.lanes|=e,ii|=e,t)}function Zd(e,t,i,a,l){var s=N.p;N.p=s!==0&&8>s?s:8;var n=w.T,u={};w.T=u,Kn(e,!1,t,i);try{var r=l(),m=w.S;if(m!==null&&m(u,r),r!==null&&typeof r=="object"&&typeof r.then=="function"){var g=vm(r,a);Za(e,t,g,et(e))}else Za(e,t,a,et(e))}catch(F){Za(e,t,{then:function(){},status:"rejected",reason:F},et())}finally{N.p=s,w.T=n}}function Mm(){}function Qn(e,t,i,a){if(e.tag!==5)throw Error(p(476));var l=Kd(e).queue;Zd(e,l,t,H,i===null?Mm:function(){return Jd(e),i(a)})}function Kd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:H,baseState:H,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ot,lastRenderedState:H},next:null};var i={};return t.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ot,lastRenderedState:i},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Jd(e){var t=Kd(e).next.queue;Za(e,t,{},et())}function Zn(){return Re(fl)}function Wd(){return ge().memoizedState}function Id(){return ge().memoizedState}function Cm(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var i=et();e=Qt(i);var a=Zt(t,e,i);a!==null&&(tt(a,t,i),La(a,t,i)),t={cache:Tn()},e.payload=t;return}t=t.return}}function Nm(e,t,i){var a=et();i={lane:a,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},$l(e)?eo(t,i):(i=pn(e,t,i,a),i!==null&&(tt(i,e,a),to(i,t,a)))}function $d(e,t,i){var a=et();Za(e,t,i,a)}function Za(e,t,i,a){var l={lane:a,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if($l(e))eo(t,l);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var n=t.lastRenderedState,u=s(n,i);if(l.hasEagerState=!0,l.eagerState=u,Ke(u,n))return Hl(e,t,l,0),de===null&&kl(),!1}catch{}finally{}if(i=pn(e,t,l,a),i!==null)return tt(i,e,a),to(i,t,a),!0}return!1}function Kn(e,t,i,a){if(a={lane:2,revertLane:Cu(),action:a,hasEagerState:!1,eagerState:null,next:null},$l(e)){if(t)throw Error(p(479))}else t=pn(e,i,a,2),t!==null&&tt(t,e,2)}function $l(e){var t=e.alternate;return e===X||t!==null&&t===X}function eo(e,t){ia=Ql=!0;var i=e.pending;i===null?t.next=t:(t.next=i.next,i.next=t),e.pending=t}function to(e,t,i){if((i&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,i|=a,t.lanes=i,dr(e,i)}}var es={readContext:Re,use:Kl,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useLayoutEffect:he,useInsertionEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useSyncExternalStore:he,useId:he,useHostTransitionStatus:he,useFormState:he,useActionState:he,useOptimistic:he,useMemoCache:he,useCacheRefresh:he},io={readContext:Re,use:Kl,useCallback:function(e,t){return Ue().memoizedState=[e,t===void 0?null:t],e},useContext:Re,useEffect:_d,useImperativeHandle:function(e,t,i){i=i!=null?i.concat([e]):null,Il(4194308,4,qd.bind(null,t,e),i)},useLayoutEffect:function(e,t){return Il(4194308,4,e,t)},useInsertionEffect:function(e,t){Il(4,2,e,t)},useMemo:function(e,t){var i=Ue();t=t===void 0?null:t;var a=e();if(Ei){Lt(!0);try{e()}finally{Lt(!1)}}return i.memoizedState=[a,t],a},useReducer:function(e,t,i){var a=Ue();if(i!==void 0){var l=i(t);if(Ei){Lt(!0);try{i(t)}finally{Lt(!1)}}}else l=t;return a.memoizedState=a.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},a.queue=e,e=e.dispatch=Nm.bind(null,X,e),[a.memoizedState,e]},useRef:function(e){var t=Ue();return e={current:e},t.memoizedState=e},useState:function(e){e=qn(e);var t=e.queue,i=$d.bind(null,X,t);return t.dispatch=i,[e.memoizedState,i]},useDebugValue:Vn,useDeferredValue:function(e,t){var i=Ue();return Xn(i,e,t)},useTransition:function(){var e=qn(!1);return e=Zd.bind(null,X,e.queue,!0,!1),Ue().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,i){var a=X,l=Ue();if(te){if(i===void 0)throw Error(p(407));i=i()}else{if(i=t(),de===null)throw Error(p(349));(W&124)!==0||Pd(a,t,i)}l.memoizedState=i;var s={value:i,getSnapshot:t};return l.queue=s,_d(Ad.bind(null,a,s,e),[e]),a.flags|=2048,la(9,Wl(),Td.bind(null,a,s,i,t),null),i},useId:function(){var e=Ue(),t=de.identifierPrefix;if(te){var i=Et,a=Nt;i=(a&~(1<<32-Ze(a)-1)).toString(32)+i,t="«"+t+"R"+i,i=Zl++,0<i&&(t+="H"+i.toString(32)),t+="»"}else i=Fm++,t="«"+t+"r"+i.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Zn,useFormState:Rd,useActionState:Rd,useOptimistic:function(e){var t=Ue();t.memoizedState=t.baseState=e;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=i,t=Kn.bind(null,X,!0,i),i.dispatch=t,[e,t]},useMemoCache:Un,useCacheRefresh:function(){return Ue().memoizedState=Cm.bind(null,X)}},ao={readContext:Re,use:Kl,useCallback:Vd,useContext:Re,useEffect:Ud,useImperativeHandle:Yd,useInsertionEffect:Bd,useLayoutEffect:Ld,useMemo:Xd,useReducer:Jl,useRef:Gd,useState:function(){return Jl(Ot)},useDebugValue:Vn,useDeferredValue:function(e,t){var i=ge();return Qd(i,le.memoizedState,e,t)},useTransition:function(){var e=Jl(Ot)[0],t=ge().memoizedState;return[typeof e=="boolean"?e:Xa(e),t]},useSyncExternalStore:Fd,useId:Wd,useHostTransitionStatus:Zn,useFormState:kd,useActionState:kd,useOptimistic:function(e,t){var i=ge();return Nd(i,le,e,t)},useMemoCache:Un,useCacheRefresh:Id},Em={readContext:Re,use:Kl,useCallback:Vd,useContext:Re,useEffect:Ud,useImperativeHandle:Yd,useInsertionEffect:Bd,useLayoutEffect:Ld,useMemo:Xd,useReducer:Ln,useRef:Gd,useState:function(){return Ln(Ot)},useDebugValue:Vn,useDeferredValue:function(e,t){var i=ge();return le===null?Xn(i,e,t):Qd(i,le.memoizedState,e,t)},useTransition:function(){var e=Ln(Ot)[0],t=ge().memoizedState;return[typeof e=="boolean"?e:Xa(e),t]},useSyncExternalStore:Fd,useId:Wd,useHostTransitionStatus:Zn,useFormState:jd,useActionState:jd,useOptimistic:function(e,t){var i=ge();return le!==null?Nd(i,le,e,t):(i.baseState=e,[e,i.queue.dispatch])},useMemoCache:Un,useCacheRefresh:Id},sa=null,Ka=0;function ts(e){var t=Ka;return Ka+=1,sa===null&&(sa=[]),pd(sa,e,t)}function Ja(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function is(e,t){throw t.$$typeof===L?Error(p(525)):(e=Object.prototype.toString.call(t),Error(p(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function lo(e){var t=e._init;return t(e._payload)}function so(e){function t(c,o){if(e){var f=c.deletions;f===null?(c.deletions=[o],c.flags|=16):f.push(o)}}function i(c,o){if(!e)return null;for(;o!==null;)t(c,o),o=o.sibling;return null}function a(c){for(var o=new Map;c!==null;)c.key!==null?o.set(c.key,c):o.set(c.index,c),c=c.sibling;return o}function l(c,o){return c=Ct(c,o),c.index=0,c.sibling=null,c}function s(c,o,f){return c.index=f,e?(f=c.alternate,f!==null?(f=f.index,f<o?(c.flags|=67108866,o):f):(c.flags|=67108866,o)):(c.flags|=1048576,o)}function n(c){return e&&c.alternate===null&&(c.flags|=67108866),c}function u(c,o,f,b){return o===null||o.tag!==6?(o=yn(f,c.mode,b),o.return=c,o):(o=l(o,f),o.return=c,o)}function r(c,o,f,b){var x=f.type;return x===Me?g(c,o,f.props.children,b,f.key):o!==null&&(o.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Ve&&lo(x)===o.type)?(o=l(o,f.props),Ja(o,f),o.return=c,o):(o=Gl(f.type,f.key,f.props,null,c.mode,b),Ja(o,f),o.return=c,o)}function m(c,o,f,b){return o===null||o.tag!==4||o.stateNode.containerInfo!==f.containerInfo||o.stateNode.implementation!==f.implementation?(o=Sn(f,c.mode,b),o.return=c,o):(o=l(o,f.children||[]),o.return=c,o)}function g(c,o,f,b,x){return o===null||o.tag!==7?(o=vi(f,c.mode,b,x),o.return=c,o):(o=l(o,f),o.return=c,o)}function F(c,o,f){if(typeof o=="string"&&o!==""||typeof o=="number"||typeof o=="bigint")return o=yn(""+o,c.mode,f),o.return=c,o;if(typeof o=="object"&&o!==null){switch(o.$$typeof){case J:return f=Gl(o.type,o.key,o.props,null,c.mode,f),Ja(f,o),f.return=c,f;case Ae:return o=Sn(o,c.mode,f),o.return=c,o;case Ve:var b=o._init;return o=b(o._payload),F(c,o,f)}if(Oe(o)||De(o))return o=vi(o,c.mode,f,null),o.return=c,o;if(typeof o.then=="function")return F(c,ts(o),f);if(o.$$typeof===xe)return F(c,Ll(c,o),f);is(c,o)}return null}function h(c,o,f,b){var x=o!==null?o.key:null;if(typeof f=="string"&&f!==""||typeof f=="number"||typeof f=="bigint")return x!==null?null:u(c,o,""+f,b);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case J:return f.key===x?r(c,o,f,b):null;case Ae:return f.key===x?m(c,o,f,b):null;case Ve:return x=f._init,f=x(f._payload),h(c,o,f,b)}if(Oe(f)||De(f))return x!==null?null:g(c,o,f,b,null);if(typeof f.then=="function")return h(c,o,ts(f),b);if(f.$$typeof===xe)return h(c,o,Ll(c,f),b);is(c,f)}return null}function y(c,o,f,b,x){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return c=c.get(f)||null,u(o,c,""+b,x);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case J:return c=c.get(b.key===null?f:b.key)||null,r(o,c,b,x);case Ae:return c=c.get(b.key===null?f:b.key)||null,m(o,c,b,x);case Ve:var Q=b._init;return b=Q(b._payload),y(c,o,f,b,x)}if(Oe(b)||De(b))return c=c.get(f)||null,g(o,c,b,x,null);if(typeof b.then=="function")return y(c,o,f,ts(b),x);if(b.$$typeof===xe)return y(c,o,f,Ll(o,b),x);is(o,b)}return null}function j(c,o,f,b){for(var x=null,Q=null,D=o,k=o=0,Te=null;D!==null&&k<f.length;k++){D.index>k?(Te=D,D=null):Te=D.sibling;var ee=h(c,D,f[k],b);if(ee===null){D===null&&(D=Te);break}e&&D&&ee.alternate===null&&t(c,D),o=s(ee,o,k),Q===null?x=ee:Q.sibling=ee,Q=ee,D=Te}if(k===f.length)return i(c,D),te&&Pi(c,k),x;if(D===null){for(;k<f.length;k++)D=F(c,f[k],b),D!==null&&(o=s(D,o,k),Q===null?x=D:Q.sibling=D,Q=D);return te&&Pi(c,k),x}for(D=a(D);k<f.length;k++)Te=y(D,c,k,f[k],b),Te!==null&&(e&&Te.alternate!==null&&D.delete(Te.key===null?k:Te.key),o=s(Te,o,k),Q===null?x=Te:Q.sibling=Te,Q=Te);return e&&D.forEach(function(ci){return t(c,ci)}),te&&Pi(c,k),x}function z(c,o,f,b){if(f==null)throw Error(p(151));for(var x=null,Q=null,D=o,k=o=0,Te=null,ee=f.next();D!==null&&!ee.done;k++,ee=f.next()){D.index>k?(Te=D,D=null):Te=D.sibling;var ci=h(c,D,ee.value,b);if(ci===null){D===null&&(D=Te);break}e&&D&&ci.alternate===null&&t(c,D),o=s(ci,o,k),Q===null?x=ci:Q.sibling=ci,Q=ci,D=Te}if(ee.done)return i(c,D),te&&Pi(c,k),x;if(D===null){for(;!ee.done;k++,ee=f.next())ee=F(c,ee.value,b),ee!==null&&(o=s(ee,o,k),Q===null?x=ee:Q.sibling=ee,Q=ee);return te&&Pi(c,k),x}for(D=a(D);!ee.done;k++,ee=f.next())ee=y(D,c,k,ee.value,b),ee!==null&&(e&&ee.alternate!==null&&D.delete(ee.key===null?k:ee.key),o=s(ee,o,k),Q===null?x=ee:Q.sibling=ee,Q=ee);return e&&D.forEach(function(xp){return t(c,xp)}),te&&Pi(c,k),x}function ne(c,o,f,b){if(typeof f=="object"&&f!==null&&f.type===Me&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case J:e:{for(var x=f.key;o!==null;){if(o.key===x){if(x=f.type,x===Me){if(o.tag===7){i(c,o.sibling),b=l(o,f.props.children),b.return=c,c=b;break e}}else if(o.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Ve&&lo(x)===o.type){i(c,o.sibling),b=l(o,f.props),Ja(b,f),b.return=c,c=b;break e}i(c,o);break}else t(c,o);o=o.sibling}f.type===Me?(b=vi(f.props.children,c.mode,b,f.key),b.return=c,c=b):(b=Gl(f.type,f.key,f.props,null,c.mode,b),Ja(b,f),b.return=c,c=b)}return n(c);case Ae:e:{for(x=f.key;o!==null;){if(o.key===x)if(o.tag===4&&o.stateNode.containerInfo===f.containerInfo&&o.stateNode.implementation===f.implementation){i(c,o.sibling),b=l(o,f.children||[]),b.return=c,c=b;break e}else{i(c,o);break}else t(c,o);o=o.sibling}b=Sn(f,c.mode,b),b.return=c,c=b}return n(c);case Ve:return x=f._init,f=x(f._payload),ne(c,o,f,b)}if(Oe(f))return j(c,o,f,b);if(De(f)){if(x=De(f),typeof x!="function")throw Error(p(150));return f=x.call(f),z(c,o,f,b)}if(typeof f.then=="function")return ne(c,o,ts(f),b);if(f.$$typeof===xe)return ne(c,o,Ll(c,f),b);is(c,f)}return typeof f=="string"&&f!==""||typeof f=="number"||typeof f=="bigint"?(f=""+f,o!==null&&o.tag===6?(i(c,o.sibling),b=l(o,f),b.return=c,c=b):(i(c,o),b=yn(f,c.mode,b),b.return=c,c=b),n(c)):i(c,o)}return function(c,o,f,b){try{Ka=0;var x=ne(c,o,f,b);return sa=null,x}catch(D){if(D===Ua||D===Yl)throw D;var Q=Je(29,D,null,c.mode);return Q.lanes=b,Q.return=c,Q}finally{}}}var na=so(!0),no=so(!1),rt=P(null),wt=null;function Jt(e){var t=e.alternate;M(be,be.current&1),M(rt,e),wt===null&&(t===null||ta.current!==null||t.memoizedState!==null)&&(wt=e)}function uo(e){if(e.tag===22){if(M(be,be.current),M(rt,e),wt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(wt=e)}}else Wt()}function Wt(){M(be,be.current),M(rt,rt.current)}function zt(e){E(rt),wt===e&&(wt=null),E(be)}var be=P(0);function as(e){for(var t=e;t!==null;){if(t.tag===13){var i=t.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||_u(i)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Jn(e,t,i,a){t=e.memoizedState,i=i(a,t),i=i==null?t:A({},t,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var Wn={enqueueSetState:function(e,t,i){e=e._reactInternals;var a=et(),l=Qt(a);l.payload=t,i!=null&&(l.callback=i),t=Zt(e,l,a),t!==null&&(tt(t,e,a),La(t,e,a))},enqueueReplaceState:function(e,t,i){e=e._reactInternals;var a=et(),l=Qt(a);l.tag=1,l.payload=t,i!=null&&(l.callback=i),t=Zt(e,l,a),t!==null&&(tt(t,e,a),La(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var i=et(),a=Qt(i);a.tag=2,t!=null&&(a.callback=t),t=Zt(e,a,i),t!==null&&(tt(t,e,i),La(t,e,i))}};function ro(e,t,i,a,l,s,n){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,s,n):t.prototype&&t.prototype.isPureReactComponent?!Oa(i,a)||!Oa(l,s):!0}function oo(e,t,i,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(i,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(i,a),t.state!==e&&Wn.enqueueReplaceState(t,t.state,null)}function xi(e,t){var i=t;if("ref"in t){i={};for(var a in t)a!=="ref"&&(i[a]=t[a])}if(e=e.defaultProps){i===t&&(i=A({},i));for(var l in e)i[l]===void 0&&(i[l]=e[l])}return i}var ls=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function co(e){ls(e)}function fo(e){console.error(e)}function mo(e){ls(e)}function ss(e,t){try{var i=e.onUncaughtError;i(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function po(e,t,i){try{var a=e.onCaughtError;a(i.value,{componentStack:i.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function In(e,t,i){return i=Qt(i),i.tag=3,i.payload={element:null},i.callback=function(){ss(e,t)},i}function ho(e){return e=Qt(e),e.tag=3,e}function yo(e,t,i,a){var l=i.type.getDerivedStateFromError;if(typeof l=="function"){var s=a.value;e.payload=function(){return l(s)},e.callback=function(){po(t,i,a)}}var n=i.stateNode;n!==null&&typeof n.componentDidCatch=="function"&&(e.callback=function(){po(t,i,a),typeof l!="function"&&(ai===null?ai=new Set([this]):ai.add(this));var u=a.stack;this.componentDidCatch(a.value,{componentStack:u!==null?u:""})})}function xm(e,t,i,a,l){if(i.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=i.alternate,t!==null&&ja(t,i,l,!0),i=rt.current,i!==null){switch(i.tag){case 13:return wt===null?Fu():i.alternate===null&&pe===0&&(pe=3),i.flags&=-257,i.flags|=65536,i.lanes=l,a===Cn?i.flags|=16384:(t=i.updateQueue,t===null?i.updateQueue=new Set([a]):t.add(a),Tu(e,a,l)),!1;case 22:return i.flags|=65536,a===Cn?i.flags|=16384:(t=i.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},i.updateQueue=t):(i=t.retryQueue,i===null?t.retryQueue=new Set([a]):i.add(a)),Tu(e,a,l)),!1}throw Error(p(435,i.tag))}return Tu(e,a,l),Fu(),!1}if(te)return t=rt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==bn&&(e=Error(p(422),{cause:a}),Ha(lt(e,i)))):(a!==bn&&(t=Error(p(423),{cause:a}),Ha(lt(t,i))),e=e.current.alternate,e.flags|=65536,l&=-l,e.lanes|=l,a=lt(a,i),l=In(e.stateNode,a,l),xn(e,l),pe!==4&&(pe=2)),!1;var s=Error(p(520),{cause:a});if(s=lt(s,i),al===null?al=[s]:al.push(s),pe!==4&&(pe=2),t===null)return!0;a=lt(a,i),i=t;do{switch(i.tag){case 3:return i.flags|=65536,e=l&-l,i.lanes|=e,e=In(i.stateNode,a,e),xn(i,e),!1;case 1:if(t=i.type,s=i.stateNode,(i.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(ai===null||!ai.has(s))))return i.flags|=65536,l&=-l,i.lanes|=l,l=ho(l),yo(l,e,i,a),xn(i,l),!1}i=i.return}while(i!==null);return!1}var So=Error(p(461)),Fe=!1;function Ce(e,t,i,a){t.child=e===null?no(t,null,i,a):na(t,e.child,i,a)}function go(e,t,i,a,l){i=i.render;var s=t.ref;if("ref"in a){var n={};for(var u in a)u!=="ref"&&(n[u]=a[u])}else n=a;return Ci(t),a=kn(e,t,i,n,s,l),u=Hn(),e!==null&&!Fe?(jn(e,t,l),Rt(e,t,l)):(te&&u&&gn(t),t.flags|=1,Ce(e,t,a,l),t.child)}function wo(e,t,i,a,l){if(e===null){var s=i.type;return typeof s=="function"&&!hn(s)&&s.defaultProps===void 0&&i.compare===null?(t.tag=15,t.type=s,bo(e,t,s,a,l)):(e=Gl(i.type,null,a,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!nu(e,l)){var n=s.memoizedProps;if(i=i.compare,i=i!==null?i:Oa,i(n,a)&&e.ref===t.ref)return Rt(e,t,l)}return t.flags|=1,e=Ct(s,a),e.ref=t.ref,e.return=t,t.child=e}function bo(e,t,i,a,l){if(e!==null){var s=e.memoizedProps;if(Oa(s,a)&&e.ref===t.ref)if(Fe=!1,t.pendingProps=a=s,nu(e,l))(e.flags&131072)!==0&&(Fe=!0);else return t.lanes=e.lanes,Rt(e,t,l)}return $n(e,t,i,a,l)}function vo(e,t,i){var a=t.pendingProps,l=a.children,s=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=s!==null?s.baseLanes|i:i,e!==null){for(l=t.child=e.child,s=0;l!==null;)s=s|l.lanes|l.childLanes,l=l.sibling;t.childLanes=s&~a}else t.childLanes=0,t.child=null;return Fo(e,t,a,i)}if((i&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ql(t,s!==null?s.cachePool:null),s!==null?wd(t,s):On(),uo(t);else return t.lanes=t.childLanes=536870912,Fo(e,t,s!==null?s.baseLanes|i:i,i)}else s!==null?(ql(t,s.cachePool),wd(t,s),Wt(),t.memoizedState=null):(e!==null&&ql(t,null),On(),Wt());return Ce(e,t,l,i),t.child}function Fo(e,t,i,a){var l=Mn();return l=l===null?null:{parent:we._currentValue,pool:l},t.memoizedState={baseLanes:i,cachePool:l},e!==null&&ql(t,null),On(),uo(t),e!==null&&ja(e,t,a,!0),null}function ns(e,t){var i=t.ref;if(i===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(p(284));(e===null||e.ref!==i)&&(t.flags|=4194816)}}function $n(e,t,i,a,l){return Ci(t),i=kn(e,t,i,a,void 0,l),a=Hn(),e!==null&&!Fe?(jn(e,t,l),Rt(e,t,l)):(te&&a&&gn(t),t.flags|=1,Ce(e,t,i,l),t.child)}function Po(e,t,i,a,l,s){return Ci(t),t.updateQueue=null,i=vd(t,a,i,l),bd(e),a=Hn(),e!==null&&!Fe?(jn(e,t,s),Rt(e,t,s)):(te&&a&&gn(t),t.flags|=1,Ce(e,t,i,s),t.child)}function To(e,t,i,a,l){if(Ci(t),t.stateNode===null){var s=Ji,n=i.contextType;typeof n=="object"&&n!==null&&(s=Re(n)),s=new i(a,s),t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=Wn,t.stateNode=s,s._reactInternals=t,s=t.stateNode,s.props=a,s.state=t.memoizedState,s.refs={},Nn(t),n=i.contextType,s.context=typeof n=="object"&&n!==null?Re(n):Ji,s.state=t.memoizedState,n=i.getDerivedStateFromProps,typeof n=="function"&&(Jn(t,i,n,a),s.state=t.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(n=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),n!==s.state&&Wn.enqueueReplaceState(s,s.state,null),Ya(t,a,s,l),qa(),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){s=t.stateNode;var u=t.memoizedProps,r=xi(i,u);s.props=r;var m=s.context,g=i.contextType;n=Ji,typeof g=="object"&&g!==null&&(n=Re(g));var F=i.getDerivedStateFromProps;g=typeof F=="function"||typeof s.getSnapshotBeforeUpdate=="function",u=t.pendingProps!==u,g||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u||m!==n)&&oo(t,s,a,n),Xt=!1;var h=t.memoizedState;s.state=h,Ya(t,a,s,l),qa(),m=t.memoizedState,u||h!==m||Xt?(typeof F=="function"&&(Jn(t,i,F,a),m=t.memoizedState),(r=Xt||ro(t,i,r,a,h,m,n))?(g||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=m),s.props=a,s.state=m,s.context=n,a=r):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{s=t.stateNode,En(e,t),n=t.memoizedProps,g=xi(i,n),s.props=g,F=t.pendingProps,h=s.context,m=i.contextType,r=Ji,typeof m=="object"&&m!==null&&(r=Re(m)),u=i.getDerivedStateFromProps,(m=typeof u=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(n!==F||h!==r)&&oo(t,s,a,r),Xt=!1,h=t.memoizedState,s.state=h,Ya(t,a,s,l),qa();var y=t.memoizedState;n!==F||h!==y||Xt||e!==null&&e.dependencies!==null&&Bl(e.dependencies)?(typeof u=="function"&&(Jn(t,i,u,a),y=t.memoizedState),(g=Xt||ro(t,i,g,a,h,y,r)||e!==null&&e.dependencies!==null&&Bl(e.dependencies))?(m||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(a,y,r),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(a,y,r)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||n===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||n===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=y),s.props=a,s.state=y,s.context=r,a=g):(typeof s.componentDidUpdate!="function"||n===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||n===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),a=!1)}return s=a,ns(e,t),a=(t.flags&128)!==0,s||a?(s=t.stateNode,i=a&&typeof i.getDerivedStateFromError!="function"?null:s.render(),t.flags|=1,e!==null&&a?(t.child=na(t,e.child,null,l),t.child=na(t,null,i,l)):Ce(e,t,i,l),t.memoizedState=s.state,e=t.child):e=Rt(e,t,l),e}function Ao(e,t,i,a){return ka(),t.flags|=256,Ce(e,t,i,a),t.child}var eu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function tu(e){return{baseLanes:e,cachePool:cd()}}function iu(e,t,i){return e=e!==null?e.childLanes&~i:0,t&&(e|=dt),e}function Mo(e,t,i){var a=t.pendingProps,l=!1,s=(t.flags&128)!==0,n;if((n=s)||(n=e!==null&&e.memoizedState===null?!1:(be.current&2)!==0),n&&(l=!0,t.flags&=-129),n=(t.flags&32)!==0,t.flags&=-33,e===null){if(te){if(l?Jt(t):Wt(),te){var u=me,r;if(r=u){e:{for(r=u,u=gt;r.nodeType!==8;){if(!u){u=null;break e}if(r=ht(r.nextSibling),r===null){u=null;break e}}u=r}u!==null?(t.memoizedState={dehydrated:u,treeContext:Fi!==null?{id:Nt,overflow:Et}:null,retryLane:536870912,hydrationErrors:null},r=Je(18,null,null,0),r.stateNode=u,r.return=t,t.child=r,He=t,me=null,r=!0):r=!1}r||Ai(t)}if(u=t.memoizedState,u!==null&&(u=u.dehydrated,u!==null))return _u(u)?t.lanes=32:t.lanes=536870912,null;zt(t)}return u=a.children,a=a.fallback,l?(Wt(),l=t.mode,u=us({mode:"hidden",children:u},l),a=vi(a,l,i,null),u.return=t,a.return=t,u.sibling=a,t.child=u,l=t.child,l.memoizedState=tu(i),l.childLanes=iu(e,n,i),t.memoizedState=eu,a):(Jt(t),au(t,u))}if(r=e.memoizedState,r!==null&&(u=r.dehydrated,u!==null)){if(s)t.flags&256?(Jt(t),t.flags&=-257,t=lu(e,t,i)):t.memoizedState!==null?(Wt(),t.child=e.child,t.flags|=128,t=null):(Wt(),l=a.fallback,u=t.mode,a=us({mode:"visible",children:a.children},u),l=vi(l,u,i,null),l.flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,na(t,e.child,null,i),a=t.child,a.memoizedState=tu(i),a.childLanes=iu(e,n,i),t.memoizedState=eu,t=l);else if(Jt(t),_u(u)){if(n=u.nextSibling&&u.nextSibling.dataset,n)var m=n.dgst;n=m,a=Error(p(419)),a.stack="",a.digest=n,Ha({value:a,source:null,stack:null}),t=lu(e,t,i)}else if(Fe||ja(e,t,i,!1),n=(i&e.childLanes)!==0,Fe||n){if(n=de,n!==null&&(a=i&-i,a=(a&42)!==0?1:_s(a),a=(a&(n.suspendedLanes|i))!==0?0:a,a!==0&&a!==r.retryLane))throw r.retryLane=a,Ki(e,a),tt(n,e,a),So;u.data==="$?"||Fu(),t=lu(e,t,i)}else u.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=r.treeContext,me=ht(u.nextSibling),He=t,te=!0,Ti=null,gt=!1,e!==null&&(nt[ut++]=Nt,nt[ut++]=Et,nt[ut++]=Fi,Nt=e.id,Et=e.overflow,Fi=t),t=au(t,a.children),t.flags|=4096);return t}return l?(Wt(),l=a.fallback,u=t.mode,r=e.child,m=r.sibling,a=Ct(r,{mode:"hidden",children:a.children}),a.subtreeFlags=r.subtreeFlags&65011712,m!==null?l=Ct(m,l):(l=vi(l,u,i,null),l.flags|=2),l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,u=e.child.memoizedState,u===null?u=tu(i):(r=u.cachePool,r!==null?(m=we._currentValue,r=r.parent!==m?{parent:m,pool:m}:r):r=cd(),u={baseLanes:u.baseLanes|i,cachePool:r}),l.memoizedState=u,l.childLanes=iu(e,n,i),t.memoizedState=eu,a):(Jt(t),i=e.child,e=i.sibling,i=Ct(i,{mode:"visible",children:a.children}),i.return=t,i.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i)}function au(e,t){return t=us({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function us(e,t){return e=Je(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function lu(e,t,i){return na(t,e.child,null,i),e=au(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Co(e,t,i){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Fn(e.return,t,i)}function su(e,t,i,a,l){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:i,tailMode:l}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=a,s.tail=i,s.tailMode=l)}function No(e,t,i){var a=t.pendingProps,l=a.revealOrder,s=a.tail;if(Ce(e,t,a.children,i),a=be.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Co(e,i,t);else if(e.tag===19)Co(e,i,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(M(be,a),l){case"forwards":for(i=t.child,l=null;i!==null;)e=i.alternate,e!==null&&as(e)===null&&(l=i),i=i.sibling;i=l,i===null?(l=t.child,t.child=null):(l=i.sibling,i.sibling=null),su(t,!1,l,i,s);break;case"backwards":for(i=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&as(e)===null){t.child=l;break}e=l.sibling,l.sibling=i,i=l,l=e}su(t,!0,i,null,s);break;case"together":su(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Rt(e,t,i){if(e!==null&&(t.dependencies=e.dependencies),ii|=t.lanes,(i&t.childLanes)===0)if(e!==null){if(ja(e,t,i,!1),(i&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(p(153));if(t.child!==null){for(e=t.child,i=Ct(e,e.pendingProps),t.child=i,i.return=t;e.sibling!==null;)e=e.sibling,i=i.sibling=Ct(e,e.pendingProps),i.return=t;i.sibling=null}return t.child}function nu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Bl(e)))}function Dm(e,t,i){switch(t.tag){case 3:oe(t,t.stateNode.containerInfo),Vt(t,we,e.memoizedState.cache),ka();break;case 27:case 5:Rs(t);break;case 4:oe(t,t.stateNode.containerInfo);break;case 10:Vt(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Jt(t),t.flags|=128,null):(i&t.child.childLanes)!==0?Mo(e,t,i):(Jt(t),e=Rt(e,t,i),e!==null?e.sibling:null);Jt(t);break;case 19:var l=(e.flags&128)!==0;if(a=(i&t.childLanes)!==0,a||(ja(e,t,i,!1),a=(i&t.childLanes)!==0),l){if(a)return No(e,t,i);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),M(be,be.current),a)break;return null;case 22:case 23:return t.lanes=0,vo(e,t,i);case 24:Vt(t,we,e.memoizedState.cache)}return Rt(e,t,i)}function Eo(e,t,i){if(e!==null)if(e.memoizedProps!==t.pendingProps)Fe=!0;else{if(!nu(e,i)&&(t.flags&128)===0)return Fe=!1,Dm(e,t,i);Fe=(e.flags&131072)!==0}else Fe=!1,te&&(t.flags&1048576)!==0&&ld(t,Ul,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,l=a._init;if(a=l(a._payload),t.type=a,typeof a=="function")hn(a)?(e=xi(a,e),t.tag=1,t=To(null,t,a,e,i)):(t.tag=0,t=$n(null,t,a,e,i));else{if(a!=null){if(l=a.$$typeof,l===ft){t.tag=11,t=go(null,t,a,e,i);break e}else if(l===Ye){t.tag=14,t=wo(null,t,a,e,i);break e}}throw t=hi(a)||a,Error(p(306,t,""))}}return t;case 0:return $n(e,t,t.type,t.pendingProps,i);case 1:return a=t.type,l=xi(a,t.pendingProps),To(e,t,a,l,i);case 3:e:{if(oe(t,t.stateNode.containerInfo),e===null)throw Error(p(387));a=t.pendingProps;var s=t.memoizedState;l=s.element,En(e,t),Ya(t,a,null,i);var n=t.memoizedState;if(a=n.cache,Vt(t,we,a),a!==s.cache&&Pn(t,[we],i,!0),qa(),a=n.element,s.isDehydrated)if(s={element:a,isDehydrated:!1,cache:n.cache},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){t=Ao(e,t,a,i);break e}else if(a!==l){l=lt(Error(p(424)),t),Ha(l),t=Ao(e,t,a,i);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(me=ht(e.firstChild),He=t,te=!0,Ti=null,gt=!0,i=no(t,null,a,i),t.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(ka(),a===l){t=Rt(e,t,i);break e}Ce(e,t,a,i)}t=t.child}return t;case 26:return ns(e,t),e===null?(i=zc(t.type,null,t.pendingProps,null))?t.memoizedState=i:te||(i=t.type,e=t.pendingProps,a=vs(U.current).createElement(i),a[ze]=t,a[Ge]=e,Ee(a,i,e),ve(a),t.stateNode=a):t.memoizedState=zc(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Rs(t),e===null&&te&&(a=t.stateNode=xc(t.type,t.pendingProps,U.current),He=t,gt=!0,l=me,ni(t.type)?(Uu=l,me=ht(a.firstChild)):me=l),Ce(e,t,t.pendingProps.children,i),ns(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&te&&((l=a=me)&&(a=sp(a,t.type,t.pendingProps,gt),a!==null?(t.stateNode=a,He=t,me=ht(a.firstChild),gt=!1,l=!0):l=!1),l||Ai(t)),Rs(t),l=t.type,s=t.pendingProps,n=e!==null?e.memoizedProps:null,a=s.children,Hu(l,s)?a=null:n!==null&&Hu(l,n)&&(t.flags|=32),t.memoizedState!==null&&(l=kn(e,t,Pm,null,null,i),fl._currentValue=l),ns(e,t),Ce(e,t,a,i),t.child;case 6:return e===null&&te&&((e=i=me)&&(i=np(i,t.pendingProps,gt),i!==null?(t.stateNode=i,He=t,me=null,e=!0):e=!1),e||Ai(t)),null;case 13:return Mo(e,t,i);case 4:return oe(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=na(t,null,a,i):Ce(e,t,a,i),t.child;case 11:return go(e,t,t.type,t.pendingProps,i);case 7:return Ce(e,t,t.pendingProps,i),t.child;case 8:return Ce(e,t,t.pendingProps.children,i),t.child;case 12:return Ce(e,t,t.pendingProps.children,i),t.child;case 10:return a=t.pendingProps,Vt(t,t.type,a.value),Ce(e,t,a.children,i),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,Ci(t),l=Re(l),a=a(l),t.flags|=1,Ce(e,t,a,i),t.child;case 14:return wo(e,t,t.type,t.pendingProps,i);case 15:return bo(e,t,t.type,t.pendingProps,i);case 19:return No(e,t,i);case 31:return a=t.pendingProps,i=t.mode,a={mode:a.mode,children:a.children},e===null?(i=us(a,i),i.ref=t.ref,t.child=i,i.return=t,t=i):(i=Ct(e.child,a),i.ref=t.ref,t.child=i,i.return=t,t=i),t;case 22:return vo(e,t,i);case 24:return Ci(t),a=Re(we),e===null?(l=Mn(),l===null&&(l=de,s=Tn(),l.pooledCache=s,s.refCount++,s!==null&&(l.pooledCacheLanes|=i),l=s),t.memoizedState={parent:a,cache:l},Nn(t),Vt(t,we,l)):((e.lanes&i)!==0&&(En(e,t),Ya(t,null,null,i),qa()),l=e.memoizedState,s=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=l),Vt(t,we,a)):(a=s.cache,Vt(t,we,a),a!==l.cache&&Pn(t,[we],i,!0))),Ce(e,t,t.pendingProps.children,i),t.child;case 29:throw t.pendingProps}throw Error(p(156,t.tag))}function kt(e){e.flags|=4}function xo(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Gc(t)){if(t=rt.current,t!==null&&((W&4194048)===W?wt!==null:(W&62914560)!==W&&(W&536870912)===0||t!==wt))throw Ba=Cn,fd;e.flags|=8192}}function rs(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ur():536870912,e.lanes|=t,oa|=t)}function Wa(e,t){if(!te)switch(e.tailMode){case"hidden":t=e.tail;for(var i=null;t!==null;)t.alternate!==null&&(i=t),t=t.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var a=null;i!==null;)i.alternate!==null&&(a=i),i=i.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,i=0,a=0;if(t)for(var l=e.child;l!==null;)i|=l.lanes|l.childLanes,a|=l.subtreeFlags&65011712,a|=l.flags&65011712,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)i|=l.lanes|l.childLanes,a|=l.subtreeFlags,a|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=a,e.childLanes=i,t}function Om(e,t,i){var a=t.pendingProps;switch(wn(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(t),null;case 1:return fe(t),null;case 3:return i=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Dt(we),Bt(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(Ra(t)?kt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,ud())),fe(t),null;case 26:return i=t.memoizedState,e===null?(kt(t),i!==null?(fe(t),xo(t,i)):(fe(t),t.flags&=-16777217)):i?i!==e.memoizedState?(kt(t),fe(t),xo(t,i)):(fe(t),t.flags&=-16777217):(e.memoizedProps!==a&&kt(t),fe(t),t.flags&=-16777217),null;case 27:wl(t),i=U.current;var l=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&kt(t);else{if(!a){if(t.stateNode===null)throw Error(p(166));return fe(t),null}e=O.current,Ra(t)?sd(t):(e=xc(l,a,i),t.stateNode=e,kt(t))}return fe(t),null;case 5:if(wl(t),i=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&kt(t);else{if(!a){if(t.stateNode===null)throw Error(p(166));return fe(t),null}if(e=O.current,Ra(t))sd(t);else{switch(l=vs(U.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?l.createElement("select",{is:a.is}):l.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?l.createElement(i,{is:a.is}):l.createElement(i)}}e[ze]=t,e[Ge]=a;e:for(l=t.child;l!==null;){if(l.tag===5||l.tag===6)e.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;l.sibling===null;){if(l.return===null||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(Ee(e,i,a),i){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&kt(t)}}return fe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&kt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(p(166));if(e=U.current,Ra(t)){if(e=t.stateNode,i=t.memoizedProps,a=null,l=He,l!==null)switch(l.tag){case 27:case 5:a=l.memoizedProps}e[ze]=t,e=!!(e.nodeValue===i||a!==null&&a.suppressHydrationWarning===!0||Pc(e.nodeValue,i)),e||Ai(t)}else e=vs(e).createTextNode(a),e[ze]=t,t.stateNode=e}return fe(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(l=Ra(t),a!==null&&a.dehydrated!==null){if(e===null){if(!l)throw Error(p(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(p(317));l[ze]=t}else ka(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;fe(t),l=!1}else l=ud(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return t.flags&256?(zt(t),t):(zt(t),null)}if(zt(t),(t.flags&128)!==0)return t.lanes=i,t;if(i=a!==null,e=e!==null&&e.memoizedState!==null,i){a=t.child,l=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(l=a.alternate.memoizedState.cachePool.pool);var s=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(s=a.memoizedState.cachePool.pool),s!==l&&(a.flags|=2048)}return i!==e&&i&&(t.child.flags|=8192),rs(t,t.updateQueue),fe(t),null;case 4:return Bt(),e===null&&Du(t.stateNode.containerInfo),fe(t),null;case 10:return Dt(t.type),fe(t),null;case 19:if(E(be),l=t.memoizedState,l===null)return fe(t),null;if(a=(t.flags&128)!==0,s=l.rendering,s===null)if(a)Wa(l,!1);else{if(pe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(s=as(e),s!==null){for(t.flags|=128,Wa(l,!1),e=s.updateQueue,t.updateQueue=e,rs(t,e),t.subtreeFlags=0,e=i,i=t.child;i!==null;)ad(i,e),i=i.sibling;return M(be,be.current&1|2),t.child}e=e.sibling}l.tail!==null&&St()>cs&&(t.flags|=128,a=!0,Wa(l,!1),t.lanes=4194304)}else{if(!a)if(e=as(s),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,rs(t,e),Wa(l,!0),l.tail===null&&l.tailMode==="hidden"&&!s.alternate&&!te)return fe(t),null}else 2*St()-l.renderingStartTime>cs&&i!==536870912&&(t.flags|=128,a=!0,Wa(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(e=l.last,e!==null?e.sibling=s:t.child=s,l.last=s)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=St(),t.sibling=null,e=be.current,M(be,a?e&1|2:e&1),t):(fe(t),null);case 22:case 23:return zt(t),zn(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(i&536870912)!==0&&(t.flags&128)===0&&(fe(t),t.subtreeFlags&6&&(t.flags|=8192)):fe(t),i=t.updateQueue,i!==null&&rs(t,i.retryQueue),i=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==i&&(t.flags|=2048),e!==null&&E(Ni),null;case 24:return i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),Dt(we),fe(t),null;case 25:return null;case 30:return null}throw Error(p(156,t.tag))}function zm(e,t){switch(wn(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Dt(we),Bt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return wl(t),null;case 13:if(zt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(p(340));ka()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return E(be),null;case 4:return Bt(),null;case 10:return Dt(t.type),null;case 22:case 23:return zt(t),zn(),e!==null&&E(Ni),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Dt(we),null;case 25:return null;default:return null}}function Do(e,t){switch(wn(t),t.tag){case 3:Dt(we),Bt();break;case 26:case 27:case 5:wl(t);break;case 4:Bt();break;case 13:zt(t);break;case 19:E(be);break;case 10:Dt(t.type);break;case 22:case 23:zt(t),zn(),e!==null&&E(Ni);break;case 24:Dt(we)}}function Ia(e,t){try{var i=t.updateQueue,a=i!==null?i.lastEffect:null;if(a!==null){var l=a.next;i=l;do{if((i.tag&e)===e){a=void 0;var s=i.create,n=i.inst;a=s(),n.destroy=a}i=i.next}while(i!==l)}}catch(u){re(t,t.return,u)}}function It(e,t,i){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var s=l.next;a=s;do{if((a.tag&e)===e){var n=a.inst,u=n.destroy;if(u!==void 0){n.destroy=void 0,l=t;var r=i,m=u;try{m()}catch(g){re(l,r,g)}}}a=a.next}while(a!==s)}}catch(g){re(t,t.return,g)}}function Oo(e){var t=e.updateQueue;if(t!==null){var i=e.stateNode;try{gd(t,i)}catch(a){re(e,e.return,a)}}}function zo(e,t,i){i.props=xi(e.type,e.memoizedProps),i.state=e.memoizedState;try{i.componentWillUnmount()}catch(a){re(e,t,a)}}function $a(e,t){try{var i=e.ref;if(i!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof i=="function"?e.refCleanup=i(a):i.current=a}}catch(l){re(e,t,l)}}function bt(e,t){var i=e.ref,a=e.refCleanup;if(i!==null)if(typeof a=="function")try{a()}catch(l){re(e,t,l)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(l){re(e,t,l)}else i.current=null}function Ro(e){var t=e.type,i=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":i.autoFocus&&a.focus();break e;case"img":i.src?a.src=i.src:i.srcSet&&(a.srcset=i.srcSet)}}catch(l){re(e,e.return,l)}}function uu(e,t,i){try{var a=e.stateNode;ep(a,e.type,i,t),a[Ge]=t}catch(l){re(e,e.return,l)}}function ko(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ni(e.type)||e.tag===4}function ru(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ko(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ni(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function du(e,t,i){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(e,t):(t=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,t.appendChild(e),i=i._reactRootContainer,i!=null||t.onclick!==null||(t.onclick=bs));else if(a!==4&&(a===27&&ni(e.type)&&(i=e.stateNode,t=null),e=e.child,e!==null))for(du(e,t,i),e=e.sibling;e!==null;)du(e,t,i),e=e.sibling}function ds(e,t,i){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?i.insertBefore(e,t):i.appendChild(e);else if(a!==4&&(a===27&&ni(e.type)&&(i=e.stateNode),e=e.child,e!==null))for(ds(e,t,i),e=e.sibling;e!==null;)ds(e,t,i),e=e.sibling}function Ho(e){var t=e.stateNode,i=e.memoizedProps;try{for(var a=e.type,l=t.attributes;l.length;)t.removeAttributeNode(l[0]);Ee(t,a,i),t[ze]=e,t[Ge]=i}catch(s){re(e,e.return,s)}}var Ht=!1,ye=!1,ou=!1,jo=typeof WeakSet=="function"?WeakSet:Set,Pe=null;function Rm(e,t){if(e=e.containerInfo,Ru=Cs,e=Qr(e),rn(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var a=i.getSelection&&i.getSelection();if(a&&a.rangeCount!==0){i=a.anchorNode;var l=a.anchorOffset,s=a.focusNode;a=a.focusOffset;try{i.nodeType,s.nodeType}catch{i=null;break e}var n=0,u=-1,r=-1,m=0,g=0,F=e,h=null;t:for(;;){for(var y;F!==i||l!==0&&F.nodeType!==3||(u=n+l),F!==s||a!==0&&F.nodeType!==3||(r=n+a),F.nodeType===3&&(n+=F.nodeValue.length),(y=F.firstChild)!==null;)h=F,F=y;for(;;){if(F===e)break t;if(h===i&&++m===l&&(u=n),h===s&&++g===a&&(r=n),(y=F.nextSibling)!==null)break;F=h,h=F.parentNode}F=y}i=u===-1||r===-1?null:{start:u,end:r}}else i=null}i=i||{start:0,end:0}}else i=null;for(ku={focusedElem:e,selectionRange:i},Cs=!1,Pe=t;Pe!==null;)if(t=Pe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Pe=e;else for(;Pe!==null;){switch(t=Pe,s=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&s!==null){e=void 0,i=t,l=s.memoizedProps,s=s.memoizedState,a=i.stateNode;try{var j=xi(i.type,l,i.elementType===i.type);e=a.getSnapshotBeforeUpdate(j,s),a.__reactInternalSnapshotBeforeUpdate=e}catch(z){re(i,i.return,z)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,i=e.nodeType,i===9)Gu(e);else if(i===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Gu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(p(163))}if(e=t.sibling,e!==null){e.return=t.return,Pe=e;break}Pe=t.return}}function Go(e,t,i){var a=i.flags;switch(i.tag){case 0:case 11:case 15:$t(e,i),a&4&&Ia(5,i);break;case 1:if($t(e,i),a&4)if(e=i.stateNode,t===null)try{e.componentDidMount()}catch(n){re(i,i.return,n)}else{var l=xi(i.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(l,t,e.__reactInternalSnapshotBeforeUpdate)}catch(n){re(i,i.return,n)}}a&64&&Oo(i),a&512&&$a(i,i.return);break;case 3:if($t(e,i),a&64&&(e=i.updateQueue,e!==null)){if(t=null,i.child!==null)switch(i.child.tag){case 27:case 5:t=i.child.stateNode;break;case 1:t=i.child.stateNode}try{gd(e,t)}catch(n){re(i,i.return,n)}}break;case 27:t===null&&a&4&&Ho(i);case 26:case 5:$t(e,i),t===null&&a&4&&Ro(i),a&512&&$a(i,i.return);break;case 12:$t(e,i);break;case 13:$t(e,i),a&4&&Bo(e,i),a&64&&(e=i.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(i=qm.bind(null,i),up(e,i))));break;case 22:if(a=i.memoizedState!==null||Ht,!a){t=t!==null&&t.memoizedState!==null||ye,l=Ht;var s=ye;Ht=a,(ye=t)&&!s?ei(e,i,(i.subtreeFlags&8772)!==0):$t(e,i),Ht=l,ye=s}break;case 30:break;default:$t(e,i)}}function _o(e){var t=e.alternate;t!==null&&(e.alternate=null,_o(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Ls(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ce=null,Be=!1;function jt(e,t,i){for(i=i.child;i!==null;)Uo(e,t,i),i=i.sibling}function Uo(e,t,i){if(Qe&&typeof Qe.onCommitFiberUnmount=="function")try{Qe.onCommitFiberUnmount(ba,i)}catch{}switch(i.tag){case 26:ye||bt(i,t),jt(e,t,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:ye||bt(i,t);var a=ce,l=Be;ni(i.type)&&(ce=i.stateNode,Be=!1),jt(e,t,i),rl(i.stateNode),ce=a,Be=l;break;case 5:ye||bt(i,t);case 6:if(a=ce,l=Be,ce=null,jt(e,t,i),ce=a,Be=l,ce!==null)if(Be)try{(ce.nodeType===9?ce.body:ce.nodeName==="HTML"?ce.ownerDocument.body:ce).removeChild(i.stateNode)}catch(s){re(i,t,s)}else try{ce.removeChild(i.stateNode)}catch(s){re(i,t,s)}break;case 18:ce!==null&&(Be?(e=ce,Nc(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,i.stateNode),yl(e)):Nc(ce,i.stateNode));break;case 4:a=ce,l=Be,ce=i.stateNode.containerInfo,Be=!0,jt(e,t,i),ce=a,Be=l;break;case 0:case 11:case 14:case 15:ye||It(2,i,t),ye||It(4,i,t),jt(e,t,i);break;case 1:ye||(bt(i,t),a=i.stateNode,typeof a.componentWillUnmount=="function"&&zo(i,t,a)),jt(e,t,i);break;case 21:jt(e,t,i);break;case 22:ye=(a=ye)||i.memoizedState!==null,jt(e,t,i),ye=a;break;default:jt(e,t,i)}}function Bo(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{yl(e)}catch(i){re(t,t.return,i)}}function km(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new jo),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new jo),t;default:throw Error(p(435,e.tag))}}function cu(e,t){var i=km(e);t.forEach(function(a){var l=Ym.bind(null,e,a);i.has(a)||(i.add(a),a.then(l,l))})}function We(e,t){var i=t.deletions;if(i!==null)for(var a=0;a<i.length;a++){var l=i[a],s=e,n=t,u=n;e:for(;u!==null;){switch(u.tag){case 27:if(ni(u.type)){ce=u.stateNode,Be=!1;break e}break;case 5:ce=u.stateNode,Be=!1;break e;case 3:case 4:ce=u.stateNode.containerInfo,Be=!0;break e}u=u.return}if(ce===null)throw Error(p(160));Uo(s,n,l),ce=null,Be=!1,s=l.alternate,s!==null&&(s.return=null),l.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Lo(t,e),t=t.sibling}var pt=null;function Lo(e,t){var i=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:We(t,e),Ie(e),a&4&&(It(3,e,e.return),Ia(3,e),It(5,e,e.return));break;case 1:We(t,e),Ie(e),a&512&&(ye||i===null||bt(i,i.return)),a&64&&Ht&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(i=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=i===null?a:i.concat(a))));break;case 26:var l=pt;if(We(t,e),Ie(e),a&512&&(ye||i===null||bt(i,i.return)),a&4){var s=i!==null?i.memoizedState:null;if(a=e.memoizedState,i===null)if(a===null)if(e.stateNode===null){e:{a=e.type,i=e.memoizedProps,l=l.ownerDocument||l;t:switch(a){case"title":s=l.getElementsByTagName("title")[0],(!s||s[Pa]||s[ze]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=l.createElement(a),l.head.insertBefore(s,l.querySelector("head > title"))),Ee(s,a,i),s[ze]=e,ve(s),a=s;break e;case"link":var n=Hc("link","href",l).get(a+(i.href||""));if(n){for(var u=0;u<n.length;u++)if(s=n[u],s.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&s.getAttribute("rel")===(i.rel==null?null:i.rel)&&s.getAttribute("title")===(i.title==null?null:i.title)&&s.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){n.splice(u,1);break t}}s=l.createElement(a),Ee(s,a,i),l.head.appendChild(s);break;case"meta":if(n=Hc("meta","content",l).get(a+(i.content||""))){for(u=0;u<n.length;u++)if(s=n[u],s.getAttribute("content")===(i.content==null?null:""+i.content)&&s.getAttribute("name")===(i.name==null?null:i.name)&&s.getAttribute("property")===(i.property==null?null:i.property)&&s.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&s.getAttribute("charset")===(i.charSet==null?null:i.charSet)){n.splice(u,1);break t}}s=l.createElement(a),Ee(s,a,i),l.head.appendChild(s);break;default:throw Error(p(468,a))}s[ze]=e,ve(s),a=s}e.stateNode=a}else jc(l,e.type,e.stateNode);else e.stateNode=kc(l,a,e.memoizedProps);else s!==a?(s===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):s.count--,a===null?jc(l,e.type,e.stateNode):kc(l,a,e.memoizedProps)):a===null&&e.stateNode!==null&&uu(e,e.memoizedProps,i.memoizedProps)}break;case 27:We(t,e),Ie(e),a&512&&(ye||i===null||bt(i,i.return)),i!==null&&a&4&&uu(e,e.memoizedProps,i.memoizedProps);break;case 5:if(We(t,e),Ie(e),a&512&&(ye||i===null||bt(i,i.return)),e.flags&32){l=e.stateNode;try{Li(l,"")}catch(y){re(e,e.return,y)}}a&4&&e.stateNode!=null&&(l=e.memoizedProps,uu(e,l,i!==null?i.memoizedProps:l)),a&1024&&(ou=!0);break;case 6:if(We(t,e),Ie(e),a&4){if(e.stateNode===null)throw Error(p(162));a=e.memoizedProps,i=e.stateNode;try{i.nodeValue=a}catch(y){re(e,e.return,y)}}break;case 3:if(Ts=null,l=pt,pt=Fs(t.containerInfo),We(t,e),pt=l,Ie(e),a&4&&i!==null&&i.memoizedState.isDehydrated)try{yl(t.containerInfo)}catch(y){re(e,e.return,y)}ou&&(ou=!1,qo(e));break;case 4:a=pt,pt=Fs(e.stateNode.containerInfo),We(t,e),Ie(e),pt=a;break;case 12:We(t,e),Ie(e);break;case 13:We(t,e),Ie(e),e.child.flags&8192&&e.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(Su=St()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,cu(e,a)));break;case 22:l=e.memoizedState!==null;var r=i!==null&&i.memoizedState!==null,m=Ht,g=ye;if(Ht=m||l,ye=g||r,We(t,e),ye=g,Ht=m,Ie(e),a&8192)e:for(t=e.stateNode,t._visibility=l?t._visibility&-2:t._visibility|1,l&&(i===null||r||Ht||ye||Di(e)),i=null,t=e;;){if(t.tag===5||t.tag===26){if(i===null){r=i=t;try{if(s=r.stateNode,l)n=s.style,typeof n.setProperty=="function"?n.setProperty("display","none","important"):n.display="none";else{u=r.stateNode;var F=r.memoizedProps.style,h=F!=null&&F.hasOwnProperty("display")?F.display:null;u.style.display=h==null||typeof h=="boolean"?"":(""+h).trim()}}catch(y){re(r,r.return,y)}}}else if(t.tag===6){if(i===null){r=t;try{r.stateNode.nodeValue=l?"":r.memoizedProps}catch(y){re(r,r.return,y)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;i===t&&(i=null),t=t.return}i===t&&(i=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(i=a.retryQueue,i!==null&&(a.retryQueue=null,cu(e,i))));break;case 19:We(t,e),Ie(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,cu(e,a)));break;case 30:break;case 21:break;default:We(t,e),Ie(e)}}function Ie(e){var t=e.flags;if(t&2){try{for(var i,a=e.return;a!==null;){if(ko(a)){i=a;break}a=a.return}if(i==null)throw Error(p(160));switch(i.tag){case 27:var l=i.stateNode,s=ru(e);ds(e,s,l);break;case 5:var n=i.stateNode;i.flags&32&&(Li(n,""),i.flags&=-33);var u=ru(e);ds(e,u,n);break;case 3:case 4:var r=i.stateNode.containerInfo,m=ru(e);du(e,m,r);break;default:throw Error(p(161))}}catch(g){re(e,e.return,g)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function qo(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;qo(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function $t(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Go(e,t.alternate,t),t=t.sibling}function Di(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:It(4,t,t.return),Di(t);break;case 1:bt(t,t.return);var i=t.stateNode;typeof i.componentWillUnmount=="function"&&zo(t,t.return,i),Di(t);break;case 27:rl(t.stateNode);case 26:case 5:bt(t,t.return),Di(t);break;case 22:t.memoizedState===null&&Di(t);break;case 30:Di(t);break;default:Di(t)}e=e.sibling}}function ei(e,t,i){for(i=i&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,l=e,s=t,n=s.flags;switch(s.tag){case 0:case 11:case 15:ei(l,s,i),Ia(4,s);break;case 1:if(ei(l,s,i),a=s,l=a.stateNode,typeof l.componentDidMount=="function")try{l.componentDidMount()}catch(m){re(a,a.return,m)}if(a=s,l=a.updateQueue,l!==null){var u=a.stateNode;try{var r=l.shared.hiddenCallbacks;if(r!==null)for(l.shared.hiddenCallbacks=null,l=0;l<r.length;l++)Sd(r[l],u)}catch(m){re(a,a.return,m)}}i&&n&64&&Oo(s),$a(s,s.return);break;case 27:Ho(s);case 26:case 5:ei(l,s,i),i&&a===null&&n&4&&Ro(s),$a(s,s.return);break;case 12:ei(l,s,i);break;case 13:ei(l,s,i),i&&n&4&&Bo(l,s);break;case 22:s.memoizedState===null&&ei(l,s,i),$a(s,s.return);break;case 30:break;default:ei(l,s,i)}t=t.sibling}}function fu(e,t){var i=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==i&&(e!=null&&e.refCount++,i!=null&&Ga(i))}function mu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ga(e))}function vt(e,t,i,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Yo(e,t,i,a),t=t.sibling}function Yo(e,t,i,a){var l=t.flags;switch(t.tag){case 0:case 11:case 15:vt(e,t,i,a),l&2048&&Ia(9,t);break;case 1:vt(e,t,i,a);break;case 3:vt(e,t,i,a),l&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ga(e)));break;case 12:if(l&2048){vt(e,t,i,a),e=t.stateNode;try{var s=t.memoizedProps,n=s.id,u=s.onPostCommit;typeof u=="function"&&u(n,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(r){re(t,t.return,r)}}else vt(e,t,i,a);break;case 13:vt(e,t,i,a);break;case 23:break;case 22:s=t.stateNode,n=t.alternate,t.memoizedState!==null?s._visibility&2?vt(e,t,i,a):el(e,t):s._visibility&2?vt(e,t,i,a):(s._visibility|=2,ua(e,t,i,a,(t.subtreeFlags&10256)!==0)),l&2048&&fu(n,t);break;case 24:vt(e,t,i,a),l&2048&&mu(t.alternate,t);break;default:vt(e,t,i,a)}}function ua(e,t,i,a,l){for(l=l&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var s=e,n=t,u=i,r=a,m=n.flags;switch(n.tag){case 0:case 11:case 15:ua(s,n,u,r,l),Ia(8,n);break;case 23:break;case 22:var g=n.stateNode;n.memoizedState!==null?g._visibility&2?ua(s,n,u,r,l):el(s,n):(g._visibility|=2,ua(s,n,u,r,l)),l&&m&2048&&fu(n.alternate,n);break;case 24:ua(s,n,u,r,l),l&&m&2048&&mu(n.alternate,n);break;default:ua(s,n,u,r,l)}t=t.sibling}}function el(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var i=e,a=t,l=a.flags;switch(a.tag){case 22:el(i,a),l&2048&&fu(a.alternate,a);break;case 24:el(i,a),l&2048&&mu(a.alternate,a);break;default:el(i,a)}t=t.sibling}}var tl=8192;function ra(e){if(e.subtreeFlags&tl)for(e=e.child;e!==null;)Vo(e),e=e.sibling}function Vo(e){switch(e.tag){case 26:ra(e),e.flags&tl&&e.memoizedState!==null&&bp(pt,e.memoizedState,e.memoizedProps);break;case 5:ra(e);break;case 3:case 4:var t=pt;pt=Fs(e.stateNode.containerInfo),ra(e),pt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=tl,tl=16777216,ra(e),tl=t):ra(e));break;default:ra(e)}}function Xo(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function il(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var i=0;i<t.length;i++){var a=t[i];Pe=a,Zo(a,e)}Xo(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Qo(e),e=e.sibling}function Qo(e){switch(e.tag){case 0:case 11:case 15:il(e),e.flags&2048&&It(9,e,e.return);break;case 3:il(e);break;case 12:il(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,os(e)):il(e);break;default:il(e)}}function os(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var i=0;i<t.length;i++){var a=t[i];Pe=a,Zo(a,e)}Xo(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:It(8,t,t.return),os(t);break;case 22:i=t.stateNode,i._visibility&2&&(i._visibility&=-3,os(t));break;default:os(t)}e=e.sibling}}function Zo(e,t){for(;Pe!==null;){var i=Pe;switch(i.tag){case 0:case 11:case 15:It(8,i,t);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var a=i.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Ga(i.memoizedState.cache)}if(a=i.child,a!==null)a.return=i,Pe=a;else e:for(i=e;Pe!==null;){a=Pe;var l=a.sibling,s=a.return;if(_o(a),a===i){Pe=null;break e}if(l!==null){l.return=s,Pe=l;break e}Pe=s}}}var Hm={getCacheForType:function(e){var t=Re(we),i=t.data.get(e);return i===void 0&&(i=e(),t.data.set(e,i)),i}},jm=typeof WeakMap=="function"?WeakMap:Map,ie=0,de=null,Z=null,W=0,ae=0,$e=null,ti=!1,da=!1,pu=!1,Gt=0,pe=0,ii=0,Oi=0,hu=0,dt=0,oa=0,al=null,Le=null,yu=!1,Su=0,cs=1/0,fs=null,ai=null,Ne=0,li=null,ca=null,fa=0,gu=0,wu=null,Ko=null,ll=0,bu=null;function et(){if((ie&2)!==0&&W!==0)return W&-W;if(w.T!==null){var e=$i;return e!==0?e:Cu()}return or()}function Jo(){dt===0&&(dt=(W&536870912)===0||te?nr():536870912);var e=rt.current;return e!==null&&(e.flags|=32),dt}function tt(e,t,i){(e===de&&(ae===2||ae===9)||e.cancelPendingCommit!==null)&&(ma(e,0),si(e,W,dt,!1)),Fa(e,i),((ie&2)===0||e!==de)&&(e===de&&((ie&2)===0&&(Oi|=i),pe===4&&si(e,W,dt,!1)),Ft(e))}function Wo(e,t,i){if((ie&6)!==0)throw Error(p(327));var a=!i&&(t&124)===0&&(t&e.expiredLanes)===0||va(e,t),l=a?Um(e,t):Pu(e,t,!0),s=a;do{if(l===0){da&&!a&&si(e,t,0,!1);break}else{if(i=e.current.alternate,s&&!Gm(i)){l=Pu(e,t,!1),s=!1;continue}if(l===2){if(s=t,e.errorRecoveryDisabledLanes&s)var n=0;else n=e.pendingLanes&-536870913,n=n!==0?n:n&536870912?536870912:0;if(n!==0){t=n;e:{var u=e;l=al;var r=u.current.memoizedState.isDehydrated;if(r&&(ma(u,n).flags|=256),n=Pu(u,n,!1),n!==2){if(pu&&!r){u.errorRecoveryDisabledLanes|=s,Oi|=s,l=4;break e}s=Le,Le=l,s!==null&&(Le===null?Le=s:Le.push.apply(Le,s))}l=n}if(s=!1,l!==2)continue}}if(l===1){ma(e,0),si(e,t,0,!0);break}e:{switch(a=e,s=l,s){case 0:case 1:throw Error(p(345));case 4:if((t&4194048)!==t)break;case 6:si(a,t,dt,!ti);break e;case 2:Le=null;break;case 3:case 5:break;default:throw Error(p(329))}if((t&62914560)===t&&(l=Su+300-St(),10<l)){if(si(a,t,dt,!ti),Pl(a,0,!0)!==0)break e;a.timeoutHandle=Mc(Io.bind(null,a,i,Le,fs,yu,t,dt,Oi,oa,ti,s,2,-0,0),l);break e}Io(a,i,Le,fs,yu,t,dt,Oi,oa,ti,s,0,-0,0)}}break}while(!0);Ft(e)}function Io(e,t,i,a,l,s,n,u,r,m,g,F,h,y){if(e.timeoutHandle=-1,F=t.subtreeFlags,(F&8192||(F&16785408)===16785408)&&(cl={stylesheets:null,count:0,unsuspend:wp},Vo(t),F=vp(),F!==null)){e.cancelPendingCommit=F(sc.bind(null,e,t,s,i,a,l,n,u,r,g,1,h,y)),si(e,s,n,!m);return}sc(e,t,s,i,a,l,n,u,r)}function Gm(e){for(var t=e;;){var i=t.tag;if((i===0||i===11||i===15)&&t.flags&16384&&(i=t.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var a=0;a<i.length;a++){var l=i[a],s=l.getSnapshot;l=l.value;try{if(!Ke(s(),l))return!1}catch{return!1}}if(i=t.child,t.subtreeFlags&16384&&i!==null)i.return=t,t=i;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function si(e,t,i,a){t&=~hu,t&=~Oi,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var l=t;0<l;){var s=31-Ze(l),n=1<<s;a[s]=-1,l&=~n}i!==0&&rr(e,i,t)}function ms(){return(ie&6)===0?(sl(0),!1):!0}function vu(){if(Z!==null){if(ae===0)var e=Z.return;else e=Z,xt=Mi=null,Gn(e),sa=null,Ka=0,e=Z;for(;e!==null;)Do(e.alternate,e),e=e.return;Z=null}}function ma(e,t){var i=e.timeoutHandle;i!==-1&&(e.timeoutHandle=-1,ip(i)),i=e.cancelPendingCommit,i!==null&&(e.cancelPendingCommit=null,i()),vu(),de=e,Z=i=Ct(e.current,null),W=t,ae=0,$e=null,ti=!1,da=va(e,t),pu=!1,oa=dt=hu=Oi=ii=pe=0,Le=al=null,yu=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var l=31-Ze(a),s=1<<l;t|=e[l],a&=~s}return Gt=t,kl(),i}function $o(e,t){X=null,w.H=es,t===Ua||t===Yl?(t=hd(),ae=3):t===fd?(t=hd(),ae=4):ae=t===So?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,$e=t,Z===null&&(pe=1,ss(e,lt(t,e.current)))}function ec(){var e=w.H;return w.H=es,e===null?es:e}function tc(){var e=w.A;return w.A=Hm,e}function Fu(){pe=4,ti||(W&4194048)!==W&&rt.current!==null||(da=!0),(ii&134217727)===0&&(Oi&134217727)===0||de===null||si(de,W,dt,!1)}function Pu(e,t,i){var a=ie;ie|=2;var l=ec(),s=tc();(de!==e||W!==t)&&(fs=null,ma(e,t)),t=!1;var n=pe;e:do try{if(ae!==0&&Z!==null){var u=Z,r=$e;switch(ae){case 8:vu(),n=6;break e;case 3:case 2:case 9:case 6:rt.current===null&&(t=!0);var m=ae;if(ae=0,$e=null,pa(e,u,r,m),i&&da){n=0;break e}break;default:m=ae,ae=0,$e=null,pa(e,u,r,m)}}_m(),n=pe;break}catch(g){$o(e,g)}while(!0);return t&&e.shellSuspendCounter++,xt=Mi=null,ie=a,w.H=l,w.A=s,Z===null&&(de=null,W=0,kl()),n}function _m(){for(;Z!==null;)ic(Z)}function Um(e,t){var i=ie;ie|=2;var a=ec(),l=tc();de!==e||W!==t?(fs=null,cs=St()+500,ma(e,t)):da=va(e,t);e:do try{if(ae!==0&&Z!==null){t=Z;var s=$e;t:switch(ae){case 1:ae=0,$e=null,pa(e,t,s,1);break;case 2:case 9:if(md(s)){ae=0,$e=null,ac(t);break}t=function(){ae!==2&&ae!==9||de!==e||(ae=7),Ft(e)},s.then(t,t);break e;case 3:ae=7;break e;case 4:ae=5;break e;case 7:md(s)?(ae=0,$e=null,ac(t)):(ae=0,$e=null,pa(e,t,s,7));break;case 5:var n=null;switch(Z.tag){case 26:n=Z.memoizedState;case 5:case 27:var u=Z;if(!n||Gc(n)){ae=0,$e=null;var r=u.sibling;if(r!==null)Z=r;else{var m=u.return;m!==null?(Z=m,ps(m)):Z=null}break t}}ae=0,$e=null,pa(e,t,s,5);break;case 6:ae=0,$e=null,pa(e,t,s,6);break;case 8:vu(),pe=6;break e;default:throw Error(p(462))}}Bm();break}catch(g){$o(e,g)}while(!0);return xt=Mi=null,w.H=a,w.A=l,ie=i,Z!==null?0:(de=null,W=0,kl(),pe)}function Bm(){for(;Z!==null&&!df();)ic(Z)}function ic(e){var t=Eo(e.alternate,e,Gt);e.memoizedProps=e.pendingProps,t===null?ps(e):Z=t}function ac(e){var t=e,i=t.alternate;switch(t.tag){case 15:case 0:t=Po(i,t,t.pendingProps,t.type,void 0,W);break;case 11:t=Po(i,t,t.pendingProps,t.type.render,t.ref,W);break;case 5:Gn(t);default:Do(i,t),t=Z=ad(t,Gt),t=Eo(i,t,Gt)}e.memoizedProps=e.pendingProps,t===null?ps(e):Z=t}function pa(e,t,i,a){xt=Mi=null,Gn(t),sa=null,Ka=0;var l=t.return;try{if(xm(e,l,t,i,W)){pe=1,ss(e,lt(i,e.current)),Z=null;return}}catch(s){if(l!==null)throw Z=l,s;pe=1,ss(e,lt(i,e.current)),Z=null;return}t.flags&32768?(te||a===1?e=!0:da||(W&536870912)!==0?e=!1:(ti=e=!0,(a===2||a===9||a===3||a===6)&&(a=rt.current,a!==null&&a.tag===13&&(a.flags|=16384))),lc(t,e)):ps(t)}function ps(e){var t=e;do{if((t.flags&32768)!==0){lc(t,ti);return}e=t.return;var i=Om(t.alternate,t,Gt);if(i!==null){Z=i;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);pe===0&&(pe=5)}function lc(e,t){do{var i=zm(e.alternate,e);if(i!==null){i.flags&=32767,Z=i;return}if(i=e.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!t&&(e=e.sibling,e!==null)){Z=e;return}Z=e=i}while(e!==null);pe=6,Z=null}function sc(e,t,i,a,l,s,n,u,r){e.cancelPendingCommit=null;do hs();while(Ne!==0);if((ie&6)!==0)throw Error(p(327));if(t!==null){if(t===e.current)throw Error(p(177));if(s=t.lanes|t.childLanes,s|=mn,wf(e,i,s,n,u,r),e===de&&(Z=de=null,W=0),ca=t,li=e,fa=i,gu=s,wu=l,Ko=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Vm(bl,function(){return oc(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=w.T,w.T=null,l=N.p,N.p=2,n=ie,ie|=4;try{Rm(e,t,i)}finally{ie=n,N.p=l,w.T=a}}Ne=1,nc(),uc(),rc()}}function nc(){if(Ne===1){Ne=0;var e=li,t=ca,i=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||i){i=w.T,w.T=null;var a=N.p;N.p=2;var l=ie;ie|=4;try{Lo(t,e);var s=ku,n=Qr(e.containerInfo),u=s.focusedElem,r=s.selectionRange;if(n!==u&&u&&u.ownerDocument&&Xr(u.ownerDocument.documentElement,u)){if(r!==null&&rn(u)){var m=r.start,g=r.end;if(g===void 0&&(g=m),"selectionStart"in u)u.selectionStart=m,u.selectionEnd=Math.min(g,u.value.length);else{var F=u.ownerDocument||document,h=F&&F.defaultView||window;if(h.getSelection){var y=h.getSelection(),j=u.textContent.length,z=Math.min(r.start,j),ne=r.end===void 0?z:Math.min(r.end,j);!y.extend&&z>ne&&(n=ne,ne=z,z=n);var c=Vr(u,z),o=Vr(u,ne);if(c&&o&&(y.rangeCount!==1||y.anchorNode!==c.node||y.anchorOffset!==c.offset||y.focusNode!==o.node||y.focusOffset!==o.offset)){var f=F.createRange();f.setStart(c.node,c.offset),y.removeAllRanges(),z>ne?(y.addRange(f),y.extend(o.node,o.offset)):(f.setEnd(o.node,o.offset),y.addRange(f))}}}}for(F=[],y=u;y=y.parentNode;)y.nodeType===1&&F.push({element:y,left:y.scrollLeft,top:y.scrollTop});for(typeof u.focus=="function"&&u.focus(),u=0;u<F.length;u++){var b=F[u];b.element.scrollLeft=b.left,b.element.scrollTop=b.top}}Cs=!!Ru,ku=Ru=null}finally{ie=l,N.p=a,w.T=i}}e.current=t,Ne=2}}function uc(){if(Ne===2){Ne=0;var e=li,t=ca,i=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||i){i=w.T,w.T=null;var a=N.p;N.p=2;var l=ie;ie|=4;try{Go(e,t.alternate,t)}finally{ie=l,N.p=a,w.T=i}}Ne=3}}function rc(){if(Ne===4||Ne===3){Ne=0,of();var e=li,t=ca,i=fa,a=Ko;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ne=5:(Ne=0,ca=li=null,dc(e,e.pendingLanes));var l=e.pendingLanes;if(l===0&&(ai=null),Us(i),t=t.stateNode,Qe&&typeof Qe.onCommitFiberRoot=="function")try{Qe.onCommitFiberRoot(ba,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=w.T,l=N.p,N.p=2,w.T=null;try{for(var s=e.onRecoverableError,n=0;n<a.length;n++){var u=a[n];s(u.value,{componentStack:u.stack})}}finally{w.T=t,N.p=l}}(fa&3)!==0&&hs(),Ft(e),l=e.pendingLanes,(i&4194090)!==0&&(l&42)!==0?e===bu?ll++:(ll=0,bu=e):ll=0,sl(0)}}function dc(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ga(t)))}function hs(e){return nc(),uc(),rc(),oc()}function oc(){if(Ne!==5)return!1;var e=li,t=gu;gu=0;var i=Us(fa),a=w.T,l=N.p;try{N.p=32>i?32:i,w.T=null,i=wu,wu=null;var s=li,n=fa;if(Ne=0,ca=li=null,fa=0,(ie&6)!==0)throw Error(p(331));var u=ie;if(ie|=4,Qo(s.current),Yo(s,s.current,n,i),ie=u,sl(0,!1),Qe&&typeof Qe.onPostCommitFiberRoot=="function")try{Qe.onPostCommitFiberRoot(ba,s)}catch{}return!0}finally{N.p=l,w.T=a,dc(e,t)}}function cc(e,t,i){t=lt(i,t),t=In(e.stateNode,t,2),e=Zt(e,t,2),e!==null&&(Fa(e,2),Ft(e))}function re(e,t,i){if(e.tag===3)cc(e,e,i);else for(;t!==null;){if(t.tag===3){cc(t,e,i);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ai===null||!ai.has(a))){e=lt(i,e),i=ho(2),a=Zt(t,i,2),a!==null&&(yo(i,a,t,e),Fa(a,2),Ft(a));break}}t=t.return}}function Tu(e,t,i){var a=e.pingCache;if(a===null){a=e.pingCache=new jm;var l=new Set;a.set(t,l)}else l=a.get(t),l===void 0&&(l=new Set,a.set(t,l));l.has(i)||(pu=!0,l.add(i),e=Lm.bind(null,e,t,i),t.then(e,e))}function Lm(e,t,i){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&i,e.warmLanes&=~i,de===e&&(W&i)===i&&(pe===4||pe===3&&(W&62914560)===W&&300>St()-Su?(ie&2)===0&&ma(e,0):hu|=i,oa===W&&(oa=0)),Ft(e)}function fc(e,t){t===0&&(t=ur()),e=Ki(e,t),e!==null&&(Fa(e,t),Ft(e))}function qm(e){var t=e.memoizedState,i=0;t!==null&&(i=t.retryLane),fc(e,i)}function Ym(e,t){var i=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;l!==null&&(i=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(p(314))}a!==null&&a.delete(t),fc(e,i)}function Vm(e,t){return Hs(e,t)}var ys=null,ha=null,Au=!1,Ss=!1,Mu=!1,zi=0;function Ft(e){e!==ha&&e.next===null&&(ha===null?ys=ha=e:ha=ha.next=e),Ss=!0,Au||(Au=!0,Qm())}function sl(e,t){if(!Mu&&Ss){Mu=!0;do for(var i=!1,a=ys;a!==null;){if(e!==0){var l=a.pendingLanes;if(l===0)var s=0;else{var n=a.suspendedLanes,u=a.pingedLanes;s=(1<<31-Ze(42|e)+1)-1,s&=l&~(n&~u),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(i=!0,yc(a,s))}else s=W,s=Pl(a,a===de?s:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(s&3)===0||va(a,s)||(i=!0,yc(a,s));a=a.next}while(i);Mu=!1}}function Xm(){mc()}function mc(){Ss=Au=!1;var e=0;zi!==0&&(tp()&&(e=zi),zi=0);for(var t=St(),i=null,a=ys;a!==null;){var l=a.next,s=pc(a,t);s===0?(a.next=null,i===null?ys=l:i.next=l,l===null&&(ha=i)):(i=a,(e!==0||(s&3)!==0)&&(Ss=!0)),a=l}sl(e)}function pc(e,t){for(var i=e.suspendedLanes,a=e.pingedLanes,l=e.expirationTimes,s=e.pendingLanes&-62914561;0<s;){var n=31-Ze(s),u=1<<n,r=l[n];r===-1?((u&i)===0||(u&a)!==0)&&(l[n]=gf(u,t)):r<=t&&(e.expiredLanes|=u),s&=~u}if(t=de,i=W,i=Pl(e,e===t?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,i===0||e===t&&(ae===2||ae===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&js(a),e.callbackNode=null,e.callbackPriority=0;if((i&3)===0||va(e,i)){if(t=i&-i,t===e.callbackPriority)return t;switch(a!==null&&js(a),Us(i)){case 2:case 8:i=lr;break;case 32:i=bl;break;case 268435456:i=sr;break;default:i=bl}return a=hc.bind(null,e),i=Hs(i,a),e.callbackPriority=t,e.callbackNode=i,t}return a!==null&&a!==null&&js(a),e.callbackPriority=2,e.callbackNode=null,2}function hc(e,t){if(Ne!==0&&Ne!==5)return e.callbackNode=null,e.callbackPriority=0,null;var i=e.callbackNode;if(hs()&&e.callbackNode!==i)return null;var a=W;return a=Pl(e,e===de?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Wo(e,a,t),pc(e,St()),e.callbackNode!=null&&e.callbackNode===i?hc.bind(null,e):null)}function yc(e,t){if(hs())return null;Wo(e,t,!0)}function Qm(){ap(function(){(ie&6)!==0?Hs(ar,Xm):mc()})}function Cu(){return zi===0&&(zi=nr()),zi}function Sc(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Nl(""+e)}function gc(e,t){var i=t.ownerDocument.createElement("input");return i.name=t.name,i.value=t.value,e.id&&i.setAttribute("form",e.id),t.parentNode.insertBefore(i,t),e=new FormData(e),i.parentNode.removeChild(i),e}function Zm(e,t,i,a,l){if(t==="submit"&&i&&i.stateNode===l){var s=Sc((l[Ge]||null).action),n=a.submitter;n&&(t=(t=n[Ge]||null)?Sc(t.formAction):n.getAttribute("formAction"),t!==null&&(s=t,n=null));var u=new Ol("action","action",null,a,l);e.push({event:u,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(zi!==0){var r=n?gc(l,n):new FormData(l);Qn(i,{pending:!0,data:r,method:l.method,action:s},null,r)}}else typeof s=="function"&&(u.preventDefault(),r=n?gc(l,n):new FormData(l),Qn(i,{pending:!0,data:r,method:l.method,action:s},s,r))},currentTarget:l}]})}}for(var Nu=0;Nu<fn.length;Nu++){var Eu=fn[Nu],Km=Eu.toLowerCase(),Jm=Eu[0].toUpperCase()+Eu.slice(1);mt(Km,"on"+Jm)}mt(Jr,"onAnimationEnd"),mt(Wr,"onAnimationIteration"),mt(Ir,"onAnimationStart"),mt("dblclick","onDoubleClick"),mt("focusin","onFocus"),mt("focusout","onBlur"),mt(mm,"onTransitionRun"),mt(pm,"onTransitionStart"),mt(hm,"onTransitionCancel"),mt($r,"onTransitionEnd"),_i("onMouseEnter",["mouseout","mouseover"]),_i("onMouseLeave",["mouseout","mouseover"]),_i("onPointerEnter",["pointerout","pointerover"]),_i("onPointerLeave",["pointerout","pointerover"]),Si("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Si("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Si("onBeforeInput",["compositionend","keypress","textInput","paste"]),Si("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Si("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Si("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var nl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(nl));function wc(e,t){t=(t&4)!==0;for(var i=0;i<e.length;i++){var a=e[i],l=a.event;a=a.listeners;e:{var s=void 0;if(t)for(var n=a.length-1;0<=n;n--){var u=a[n],r=u.instance,m=u.currentTarget;if(u=u.listener,r!==s&&l.isPropagationStopped())break e;s=u,l.currentTarget=m;try{s(l)}catch(g){ls(g)}l.currentTarget=null,s=r}else for(n=0;n<a.length;n++){if(u=a[n],r=u.instance,m=u.currentTarget,u=u.listener,r!==s&&l.isPropagationStopped())break e;s=u,l.currentTarget=m;try{s(l)}catch(g){ls(g)}l.currentTarget=null,s=r}}}}function K(e,t){var i=t[Bs];i===void 0&&(i=t[Bs]=new Set);var a=e+"__bubble";i.has(a)||(bc(t,e,2,!1),i.add(a))}function xu(e,t,i){var a=0;t&&(a|=4),bc(i,e,a,t)}var gs="_reactListening"+Math.random().toString(36).slice(2);function Du(e){if(!e[gs]){e[gs]=!0,fr.forEach(function(i){i!=="selectionchange"&&(Wm.has(i)||xu(i,!1,e),xu(i,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[gs]||(t[gs]=!0,xu("selectionchange",!1,t))}}function bc(e,t,i,a){switch(Yc(t)){case 2:var l=Tp;break;case 8:l=Ap;break;default:l=Vu}i=l.bind(null,t,i,e),l=void 0,!Is||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),a?l!==void 0?e.addEventListener(t,i,{capture:!0,passive:l}):e.addEventListener(t,i,!0):l!==void 0?e.addEventListener(t,i,{passive:l}):e.addEventListener(t,i,!1)}function Ou(e,t,i,a,l){var s=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var n=a.tag;if(n===3||n===4){var u=a.stateNode.containerInfo;if(u===l)break;if(n===4)for(n=a.return;n!==null;){var r=n.tag;if((r===3||r===4)&&n.stateNode.containerInfo===l)return;n=n.return}for(;u!==null;){if(n=Hi(u),n===null)return;if(r=n.tag,r===5||r===6||r===26||r===27){a=s=n;continue e}u=u.parentNode}}a=a.return}Mr(function(){var m=s,g=Js(i),F=[];e:{var h=ed.get(e);if(h!==void 0){var y=Ol,j=e;switch(e){case"keypress":if(xl(i)===0)break e;case"keydown":case"keyup":y=Vf;break;case"focusin":j="focus",y=an;break;case"focusout":j="blur",y=an;break;case"beforeblur":case"afterblur":y=an;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Er;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=zf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Zf;break;case Jr:case Wr:case Ir:y=Hf;break;case $r:y=Jf;break;case"scroll":case"scrollend":y=Df;break;case"wheel":y=If;break;case"copy":case"cut":case"paste":y=Gf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Dr;break;case"toggle":case"beforetoggle":y=em}var z=(t&4)!==0,ne=!z&&(e==="scroll"||e==="scrollend"),c=z?h!==null?h+"Capture":null:h;z=[];for(var o=m,f;o!==null;){var b=o;if(f=b.stateNode,b=b.tag,b!==5&&b!==26&&b!==27||f===null||c===null||(b=Aa(o,c),b!=null&&z.push(ul(o,b,f))),ne)break;o=o.return}0<z.length&&(h=new y(h,j,null,i,g),F.push({event:h,listeners:z}))}}if((t&7)===0){e:{if(h=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",h&&i!==Ks&&(j=i.relatedTarget||i.fromElement)&&(Hi(j)||j[ki]))break e;if((y||h)&&(h=g.window===g?g:(h=g.ownerDocument)?h.defaultView||h.parentWindow:window,y?(j=i.relatedTarget||i.toElement,y=m,j=j?Hi(j):null,j!==null&&(ne=Y(j),z=j.tag,j!==ne||z!==5&&z!==27&&z!==6)&&(j=null)):(y=null,j=m),y!==j)){if(z=Er,b="onMouseLeave",c="onMouseEnter",o="mouse",(e==="pointerout"||e==="pointerover")&&(z=Dr,b="onPointerLeave",c="onPointerEnter",o="pointer"),ne=y==null?h:Ta(y),f=j==null?h:Ta(j),h=new z(b,o+"leave",y,i,g),h.target=ne,h.relatedTarget=f,b=null,Hi(g)===m&&(z=new z(c,o+"enter",j,i,g),z.target=f,z.relatedTarget=ne,b=z),ne=b,y&&j)t:{for(z=y,c=j,o=0,f=z;f;f=ya(f))o++;for(f=0,b=c;b;b=ya(b))f++;for(;0<o-f;)z=ya(z),o--;for(;0<f-o;)c=ya(c),f--;for(;o--;){if(z===c||c!==null&&z===c.alternate)break t;z=ya(z),c=ya(c)}z=null}else z=null;y!==null&&vc(F,h,y,z,!1),j!==null&&ne!==null&&vc(F,ne,j,z,!0)}}e:{if(h=m?Ta(m):window,y=h.nodeName&&h.nodeName.toLowerCase(),y==="select"||y==="input"&&h.type==="file")var x=_r;else if(jr(h))if(Ur)x=om;else{x=rm;var Q=um}else y=h.nodeName,!y||y.toLowerCase()!=="input"||h.type!=="checkbox"&&h.type!=="radio"?m&&Zs(m.elementType)&&(x=_r):x=dm;if(x&&(x=x(e,m))){Gr(F,x,i,g);break e}Q&&Q(e,h,m),e==="focusout"&&m&&h.type==="number"&&m.memoizedProps.value!=null&&Qs(h,"number",h.value)}switch(Q=m?Ta(m):window,e){case"focusin":(jr(Q)||Q.contentEditable==="true")&&(Xi=Q,dn=m,za=null);break;case"focusout":za=dn=Xi=null;break;case"mousedown":on=!0;break;case"contextmenu":case"mouseup":case"dragend":on=!1,Zr(F,i,g);break;case"selectionchange":if(fm)break;case"keydown":case"keyup":Zr(F,i,g)}var D;if(sn)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Vi?kr(e,i)&&(k="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(k="onCompositionStart");k&&(Or&&i.locale!=="ko"&&(Vi||k!=="onCompositionStart"?k==="onCompositionEnd"&&Vi&&(D=Cr()):(Yt=g,$s="value"in Yt?Yt.value:Yt.textContent,Vi=!0)),Q=ws(m,k),0<Q.length&&(k=new xr(k,e,null,i,g),F.push({event:k,listeners:Q}),D?k.data=D:(D=Hr(i),D!==null&&(k.data=D)))),(D=im?am(e,i):lm(e,i))&&(k=ws(m,"onBeforeInput"),0<k.length&&(Q=new xr("onBeforeInput","beforeinput",null,i,g),F.push({event:Q,listeners:k}),Q.data=D)),Zm(F,e,m,i,g)}wc(F,t)})}function ul(e,t,i){return{instance:e,listener:t,currentTarget:i}}function ws(e,t){for(var i=t+"Capture",a=[];e!==null;){var l=e,s=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||s===null||(l=Aa(e,i),l!=null&&a.unshift(ul(e,l,s)),l=Aa(e,t),l!=null&&a.push(ul(e,l,s))),e.tag===3)return a;e=e.return}return[]}function ya(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function vc(e,t,i,a,l){for(var s=t._reactName,n=[];i!==null&&i!==a;){var u=i,r=u.alternate,m=u.stateNode;if(u=u.tag,r!==null&&r===a)break;u!==5&&u!==26&&u!==27||m===null||(r=m,l?(m=Aa(i,s),m!=null&&n.unshift(ul(i,m,r))):l||(m=Aa(i,s),m!=null&&n.push(ul(i,m,r)))),i=i.return}n.length!==0&&e.push({event:t,listeners:n})}var Im=/\r\n?/g,$m=/\u0000|\uFFFD/g;function Fc(e){return(typeof e=="string"?e:""+e).replace(Im,`
`).replace($m,"")}function Pc(e,t){return t=Fc(t),Fc(e)===t}function bs(){}function se(e,t,i,a,l,s){switch(i){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Li(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Li(e,""+a);break;case"className":Al(e,"class",a);break;case"tabIndex":Al(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Al(e,i,a);break;case"style":Tr(e,a,s);break;case"data":if(t!=="object"){Al(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||i!=="href")){e.removeAttribute(i);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(i);break}a=Nl(""+a),e.setAttribute(i,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(i==="formAction"?(t!=="input"&&se(e,t,"name",l.name,l,null),se(e,t,"formEncType",l.formEncType,l,null),se(e,t,"formMethod",l.formMethod,l,null),se(e,t,"formTarget",l.formTarget,l,null)):(se(e,t,"encType",l.encType,l,null),se(e,t,"method",l.method,l,null),se(e,t,"target",l.target,l,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(i);break}a=Nl(""+a),e.setAttribute(i,a);break;case"onClick":a!=null&&(e.onclick=bs);break;case"onScroll":a!=null&&K("scroll",e);break;case"onScrollEnd":a!=null&&K("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(p(61));if(i=a.__html,i!=null){if(l.children!=null)throw Error(p(60));e.innerHTML=i}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}i=Nl(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(i,""+a):e.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(i,""):e.removeAttribute(i);break;case"capture":case"download":a===!0?e.setAttribute(i,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(i,a):e.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(i,a):e.removeAttribute(i);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(i):e.setAttribute(i,a);break;case"popover":K("beforetoggle",e),K("toggle",e),Tl(e,"popover",a);break;case"xlinkActuate":At(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":At(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":At(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":At(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":At(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":At(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":At(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":At(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":At(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Tl(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=Ef.get(i)||i,Tl(e,i,a))}}function zu(e,t,i,a,l,s){switch(i){case"style":Tr(e,a,s);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(p(61));if(i=a.__html,i!=null){if(l.children!=null)throw Error(p(60));e.innerHTML=i}}break;case"children":typeof a=="string"?Li(e,a):(typeof a=="number"||typeof a=="bigint")&&Li(e,""+a);break;case"onScroll":a!=null&&K("scroll",e);break;case"onScrollEnd":a!=null&&K("scrollend",e);break;case"onClick":a!=null&&(e.onclick=bs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!mr.hasOwnProperty(i))e:{if(i[0]==="o"&&i[1]==="n"&&(l=i.endsWith("Capture"),t=i.slice(2,l?i.length-7:void 0),s=e[Ge]||null,s=s!=null?s[i]:null,typeof s=="function"&&e.removeEventListener(t,s,l),typeof a=="function")){typeof s!="function"&&s!==null&&(i in e?e[i]=null:e.hasAttribute(i)&&e.removeAttribute(i)),e.addEventListener(t,a,l);break e}i in e?e[i]=a:a===!0?e.setAttribute(i,""):Tl(e,i,a)}}}function Ee(e,t,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":K("error",e),K("load",e);var a=!1,l=!1,s;for(s in i)if(i.hasOwnProperty(s)){var n=i[s];if(n!=null)switch(s){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(p(137,t));default:se(e,t,s,n,i,null)}}l&&se(e,t,"srcSet",i.srcSet,i,null),a&&se(e,t,"src",i.src,i,null);return;case"input":K("invalid",e);var u=s=n=l=null,r=null,m=null;for(a in i)if(i.hasOwnProperty(a)){var g=i[a];if(g!=null)switch(a){case"name":l=g;break;case"type":n=g;break;case"checked":r=g;break;case"defaultChecked":m=g;break;case"value":s=g;break;case"defaultValue":u=g;break;case"children":case"dangerouslySetInnerHTML":if(g!=null)throw Error(p(137,t));break;default:se(e,t,a,g,i,null)}}br(e,s,u,r,m,n,l,!1),Ml(e);return;case"select":K("invalid",e),a=n=s=null;for(l in i)if(i.hasOwnProperty(l)&&(u=i[l],u!=null))switch(l){case"value":s=u;break;case"defaultValue":n=u;break;case"multiple":a=u;default:se(e,t,l,u,i,null)}t=s,i=n,e.multiple=!!a,t!=null?Bi(e,!!a,t,!1):i!=null&&Bi(e,!!a,i,!0);return;case"textarea":K("invalid",e),s=l=a=null;for(n in i)if(i.hasOwnProperty(n)&&(u=i[n],u!=null))switch(n){case"value":a=u;break;case"defaultValue":l=u;break;case"children":s=u;break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(p(91));break;default:se(e,t,n,u,i,null)}Fr(e,a,l,s),Ml(e);return;case"option":for(r in i)if(i.hasOwnProperty(r)&&(a=i[r],a!=null))switch(r){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:se(e,t,r,a,i,null)}return;case"dialog":K("beforetoggle",e),K("toggle",e),K("cancel",e),K("close",e);break;case"iframe":case"object":K("load",e);break;case"video":case"audio":for(a=0;a<nl.length;a++)K(nl[a],e);break;case"image":K("error",e),K("load",e);break;case"details":K("toggle",e);break;case"embed":case"source":case"link":K("error",e),K("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(m in i)if(i.hasOwnProperty(m)&&(a=i[m],a!=null))switch(m){case"children":case"dangerouslySetInnerHTML":throw Error(p(137,t));default:se(e,t,m,a,i,null)}return;default:if(Zs(t)){for(g in i)i.hasOwnProperty(g)&&(a=i[g],a!==void 0&&zu(e,t,g,a,i,void 0));return}}for(u in i)i.hasOwnProperty(u)&&(a=i[u],a!=null&&se(e,t,u,a,i,null))}function ep(e,t,i,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,s=null,n=null,u=null,r=null,m=null,g=null;for(y in i){var F=i[y];if(i.hasOwnProperty(y)&&F!=null)switch(y){case"checked":break;case"value":break;case"defaultValue":r=F;default:a.hasOwnProperty(y)||se(e,t,y,null,a,F)}}for(var h in a){var y=a[h];if(F=i[h],a.hasOwnProperty(h)&&(y!=null||F!=null))switch(h){case"type":s=y;break;case"name":l=y;break;case"checked":m=y;break;case"defaultChecked":g=y;break;case"value":n=y;break;case"defaultValue":u=y;break;case"children":case"dangerouslySetInnerHTML":if(y!=null)throw Error(p(137,t));break;default:y!==F&&se(e,t,h,y,a,F)}}Xs(e,n,u,r,m,g,s,l);return;case"select":y=n=u=h=null;for(s in i)if(r=i[s],i.hasOwnProperty(s)&&r!=null)switch(s){case"value":break;case"multiple":y=r;default:a.hasOwnProperty(s)||se(e,t,s,null,a,r)}for(l in a)if(s=a[l],r=i[l],a.hasOwnProperty(l)&&(s!=null||r!=null))switch(l){case"value":h=s;break;case"defaultValue":u=s;break;case"multiple":n=s;default:s!==r&&se(e,t,l,s,a,r)}t=u,i=n,a=y,h!=null?Bi(e,!!i,h,!1):!!a!=!!i&&(t!=null?Bi(e,!!i,t,!0):Bi(e,!!i,i?[]:"",!1));return;case"textarea":y=h=null;for(u in i)if(l=i[u],i.hasOwnProperty(u)&&l!=null&&!a.hasOwnProperty(u))switch(u){case"value":break;case"children":break;default:se(e,t,u,null,a,l)}for(n in a)if(l=a[n],s=i[n],a.hasOwnProperty(n)&&(l!=null||s!=null))switch(n){case"value":h=l;break;case"defaultValue":y=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(p(91));break;default:l!==s&&se(e,t,n,l,a,s)}vr(e,h,y);return;case"option":for(var j in i)if(h=i[j],i.hasOwnProperty(j)&&h!=null&&!a.hasOwnProperty(j))switch(j){case"selected":e.selected=!1;break;default:se(e,t,j,null,a,h)}for(r in a)if(h=a[r],y=i[r],a.hasOwnProperty(r)&&h!==y&&(h!=null||y!=null))switch(r){case"selected":e.selected=h&&typeof h!="function"&&typeof h!="symbol";break;default:se(e,t,r,h,a,y)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var z in i)h=i[z],i.hasOwnProperty(z)&&h!=null&&!a.hasOwnProperty(z)&&se(e,t,z,null,a,h);for(m in a)if(h=a[m],y=i[m],a.hasOwnProperty(m)&&h!==y&&(h!=null||y!=null))switch(m){case"children":case"dangerouslySetInnerHTML":if(h!=null)throw Error(p(137,t));break;default:se(e,t,m,h,a,y)}return;default:if(Zs(t)){for(var ne in i)h=i[ne],i.hasOwnProperty(ne)&&h!==void 0&&!a.hasOwnProperty(ne)&&zu(e,t,ne,void 0,a,h);for(g in a)h=a[g],y=i[g],!a.hasOwnProperty(g)||h===y||h===void 0&&y===void 0||zu(e,t,g,h,a,y);return}}for(var c in i)h=i[c],i.hasOwnProperty(c)&&h!=null&&!a.hasOwnProperty(c)&&se(e,t,c,null,a,h);for(F in a)h=a[F],y=i[F],!a.hasOwnProperty(F)||h===y||h==null&&y==null||se(e,t,F,h,a,y)}var Ru=null,ku=null;function vs(e){return e.nodeType===9?e:e.ownerDocument}function Tc(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ac(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Hu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ju=null;function tp(){var e=window.event;return e&&e.type==="popstate"?e===ju?!1:(ju=e,!0):(ju=null,!1)}var Mc=typeof setTimeout=="function"?setTimeout:void 0,ip=typeof clearTimeout=="function"?clearTimeout:void 0,Cc=typeof Promise=="function"?Promise:void 0,ap=typeof queueMicrotask=="function"?queueMicrotask:typeof Cc<"u"?function(e){return Cc.resolve(null).then(e).catch(lp)}:Mc;function lp(e){setTimeout(function(){throw e})}function ni(e){return e==="head"}function Nc(e,t){var i=t,a=0,l=0;do{var s=i.nextSibling;if(e.removeChild(i),s&&s.nodeType===8)if(i=s.data,i==="/$"){if(0<a&&8>a){i=a;var n=e.ownerDocument;if(i&1&&rl(n.documentElement),i&2&&rl(n.body),i&4)for(i=n.head,rl(i),n=i.firstChild;n;){var u=n.nextSibling,r=n.nodeName;n[Pa]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&n.rel.toLowerCase()==="stylesheet"||i.removeChild(n),n=u}}if(l===0){e.removeChild(s),yl(t);return}l--}else i==="$"||i==="$?"||i==="$!"?l++:a=i.charCodeAt(0)-48;else a=0;i=s}while(i);yl(t)}function Gu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var i=t;switch(t=t.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":Gu(i),Ls(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}e.removeChild(i)}}function sp(e,t,i,a){for(;e.nodeType===1;){var l=i;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Pa])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(s=e.getAttribute("rel"),s==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(s!==l.rel||e.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||e.getAttribute("title")!==(l.title==null?null:l.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(s=e.getAttribute("src"),(s!==(l.src==null?null:l.src)||e.getAttribute("type")!==(l.type==null?null:l.type)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var s=l.name==null?null:""+l.name;if(l.type==="hidden"&&e.getAttribute("name")===s)return e}else return e;if(e=ht(e.nextSibling),e===null)break}return null}function np(e,t,i){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!i||(e=ht(e.nextSibling),e===null))return null;return e}function _u(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function up(e,t){var i=e.ownerDocument;if(e.data!=="$?"||i.readyState==="complete")t();else{var a=function(){t(),i.removeEventListener("DOMContentLoaded",a)};i.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Uu=null;function Ec(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(t===0)return e;t--}else i==="/$"&&t++}e=e.previousSibling}return null}function xc(e,t,i){switch(t=vs(i),e){case"html":if(e=t.documentElement,!e)throw Error(p(452));return e;case"head":if(e=t.head,!e)throw Error(p(453));return e;case"body":if(e=t.body,!e)throw Error(p(454));return e;default:throw Error(p(451))}}function rl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ls(e)}var ot=new Map,Dc=new Set;function Fs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var _t=N.d;N.d={f:rp,r:dp,D:op,C:cp,L:fp,m:mp,X:hp,S:pp,M:yp};function rp(){var e=_t.f(),t=ms();return e||t}function dp(e){var t=ji(e);t!==null&&t.tag===5&&t.type==="form"?Jd(t):_t.r(e)}var Sa=typeof document>"u"?null:document;function Oc(e,t,i){var a=Sa;if(a&&typeof t=="string"&&t){var l=at(t);l='link[rel="'+e+'"][href="'+l+'"]',typeof i=="string"&&(l+='[crossorigin="'+i+'"]'),Dc.has(l)||(Dc.add(l),e={rel:e,crossOrigin:i,href:t},a.querySelector(l)===null&&(t=a.createElement("link"),Ee(t,"link",e),ve(t),a.head.appendChild(t)))}}function op(e){_t.D(e),Oc("dns-prefetch",e,null)}function cp(e,t){_t.C(e,t),Oc("preconnect",e,t)}function fp(e,t,i){_t.L(e,t,i);var a=Sa;if(a&&e&&t){var l='link[rel="preload"][as="'+at(t)+'"]';t==="image"&&i&&i.imageSrcSet?(l+='[imagesrcset="'+at(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(l+='[imagesizes="'+at(i.imageSizes)+'"]')):l+='[href="'+at(e)+'"]';var s=l;switch(t){case"style":s=ga(e);break;case"script":s=wa(e)}ot.has(s)||(e=A({rel:"preload",href:t==="image"&&i&&i.imageSrcSet?void 0:e,as:t},i),ot.set(s,e),a.querySelector(l)!==null||t==="style"&&a.querySelector(dl(s))||t==="script"&&a.querySelector(ol(s))||(t=a.createElement("link"),Ee(t,"link",e),ve(t),a.head.appendChild(t)))}}function mp(e,t){_t.m(e,t);var i=Sa;if(i&&e){var a=t&&typeof t.as=="string"?t.as:"script",l='link[rel="modulepreload"][as="'+at(a)+'"][href="'+at(e)+'"]',s=l;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=wa(e)}if(!ot.has(s)&&(e=A({rel:"modulepreload",href:e},t),ot.set(s,e),i.querySelector(l)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(ol(s)))return}a=i.createElement("link"),Ee(a,"link",e),ve(a),i.head.appendChild(a)}}}function pp(e,t,i){_t.S(e,t,i);var a=Sa;if(a&&e){var l=Gi(a).hoistableStyles,s=ga(e);t=t||"default";var n=l.get(s);if(!n){var u={loading:0,preload:null};if(n=a.querySelector(dl(s)))u.loading=5;else{e=A({rel:"stylesheet",href:e,"data-precedence":t},i),(i=ot.get(s))&&Bu(e,i);var r=n=a.createElement("link");ve(r),Ee(r,"link",e),r._p=new Promise(function(m,g){r.onload=m,r.onerror=g}),r.addEventListener("load",function(){u.loading|=1}),r.addEventListener("error",function(){u.loading|=2}),u.loading|=4,Ps(n,t,a)}n={type:"stylesheet",instance:n,count:1,state:u},l.set(s,n)}}}function hp(e,t){_t.X(e,t);var i=Sa;if(i&&e){var a=Gi(i).hoistableScripts,l=wa(e),s=a.get(l);s||(s=i.querySelector(ol(l)),s||(e=A({src:e,async:!0},t),(t=ot.get(l))&&Lu(e,t),s=i.createElement("script"),ve(s),Ee(s,"link",e),i.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},a.set(l,s))}}function yp(e,t){_t.M(e,t);var i=Sa;if(i&&e){var a=Gi(i).hoistableScripts,l=wa(e),s=a.get(l);s||(s=i.querySelector(ol(l)),s||(e=A({src:e,async:!0,type:"module"},t),(t=ot.get(l))&&Lu(e,t),s=i.createElement("script"),ve(s),Ee(s,"link",e),i.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},a.set(l,s))}}function zc(e,t,i,a){var l=(l=U.current)?Fs(l):null;if(!l)throw Error(p(446));switch(e){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(t=ga(i.href),i=Gi(l).hoistableStyles,a=i.get(t),a||(a={type:"style",instance:null,count:0,state:null},i.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){e=ga(i.href);var s=Gi(l).hoistableStyles,n=s.get(e);if(n||(l=l.ownerDocument||l,n={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(e,n),(s=l.querySelector(dl(e)))&&!s._p&&(n.instance=s,n.state.loading=5),ot.has(e)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},ot.set(e,i),s||Sp(l,e,i,n.state))),t&&a===null)throw Error(p(528,""));return n}if(t&&a!==null)throw Error(p(529,""));return null;case"script":return t=i.async,i=i.src,typeof i=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=wa(i),i=Gi(l).hoistableScripts,a=i.get(t),a||(a={type:"script",instance:null,count:0,state:null},i.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(p(444,e))}}function ga(e){return'href="'+at(e)+'"'}function dl(e){return'link[rel="stylesheet"]['+e+"]"}function Rc(e){return A({},e,{"data-precedence":e.precedence,precedence:null})}function Sp(e,t,i,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Ee(t,"link",i),ve(t),e.head.appendChild(t))}function wa(e){return'[src="'+at(e)+'"]'}function ol(e){return"script[async]"+e}function kc(e,t,i){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+at(i.href)+'"]');if(a)return t.instance=a,ve(a),a;var l=A({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),ve(a),Ee(a,"style",l),Ps(a,i.precedence,e),t.instance=a;case"stylesheet":l=ga(i.href);var s=e.querySelector(dl(l));if(s)return t.state.loading|=4,t.instance=s,ve(s),s;a=Rc(i),(l=ot.get(l))&&Bu(a,l),s=(e.ownerDocument||e).createElement("link"),ve(s);var n=s;return n._p=new Promise(function(u,r){n.onload=u,n.onerror=r}),Ee(s,"link",a),t.state.loading|=4,Ps(s,i.precedence,e),t.instance=s;case"script":return s=wa(i.src),(l=e.querySelector(ol(s)))?(t.instance=l,ve(l),l):(a=i,(l=ot.get(s))&&(a=A({},i),Lu(a,l)),e=e.ownerDocument||e,l=e.createElement("script"),ve(l),Ee(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(p(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Ps(a,i.precedence,e));return t.instance}function Ps(e,t,i){for(var a=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=a.length?a[a.length-1]:null,s=l,n=0;n<a.length;n++){var u=a[n];if(u.dataset.precedence===t)s=u;else if(s!==l)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=i.nodeType===9?i.head:i,t.insertBefore(e,t.firstChild))}function Bu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Lu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ts=null;function Hc(e,t,i){if(Ts===null){var a=new Map,l=Ts=new Map;l.set(i,a)}else l=Ts,a=l.get(i),a||(a=new Map,l.set(i,a));if(a.has(e))return a;for(a.set(e,null),i=i.getElementsByTagName(e),l=0;l<i.length;l++){var s=i[l];if(!(s[Pa]||s[ze]||e==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var n=s.getAttribute(t)||"";n=e+n;var u=a.get(n);u?u.push(s):a.set(n,[s])}}return a}function jc(e,t,i){e=e.ownerDocument||e,e.head.insertBefore(i,t==="title"?e.querySelector("head > title"):null)}function gp(e,t,i){if(i===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Gc(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var cl=null;function wp(){}function bp(e,t,i){if(cl===null)throw Error(p(475));var a=cl;if(t.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var l=ga(i.href),s=e.querySelector(dl(l));if(s){e=s._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=As.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=s,ve(s);return}s=e.ownerDocument||e,i=Rc(i),(l=ot.get(l))&&Bu(i,l),s=s.createElement("link"),ve(s);var n=s;n._p=new Promise(function(u,r){n.onload=u,n.onerror=r}),Ee(s,"link",i),t.instance=s}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=As.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function vp(){if(cl===null)throw Error(p(475));var e=cl;return e.stylesheets&&e.count===0&&qu(e,e.stylesheets),0<e.count?function(t){var i=setTimeout(function(){if(e.stylesheets&&qu(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(i)}}:null}function As(){if(this.count--,this.count===0){if(this.stylesheets)qu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ms=null;function qu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ms=new Map,t.forEach(Fp,e),Ms=null,As.call(e))}function Fp(e,t){if(!(t.state.loading&4)){var i=Ms.get(e);if(i)var a=i.get(null);else{i=new Map,Ms.set(e,i);for(var l=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<l.length;s++){var n=l[s];(n.nodeName==="LINK"||n.getAttribute("media")!=="not all")&&(i.set(n.dataset.precedence,n),a=n)}a&&i.set(null,a)}l=t.instance,n=l.getAttribute("data-precedence"),s=i.get(n)||a,s===a&&i.set(null,l),i.set(n,l),this.count++,a=As.bind(this),l.addEventListener("load",a),l.addEventListener("error",a),s?s.parentNode.insertBefore(l,s.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(l,e.firstChild)),t.state.loading|=4}}var fl={$$typeof:xe,Provider:null,Consumer:null,_currentValue:H,_currentValue2:H,_threadCount:0};function Pp(e,t,i,a,l,s,n,u){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Gs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Gs(0),this.hiddenUpdates=Gs(null),this.identifierPrefix=a,this.onUncaughtError=l,this.onCaughtError=s,this.onRecoverableError=n,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function _c(e,t,i,a,l,s,n,u,r,m,g,F){return e=new Pp(e,t,i,n,u,r,m,F),t=1,s===!0&&(t|=24),s=Je(3,null,null,t),e.current=s,s.stateNode=e,t=Tn(),t.refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:a,isDehydrated:i,cache:t},Nn(s),e}function Uc(e){return e?(e=Ji,e):Ji}function Bc(e,t,i,a,l,s){l=Uc(l),a.context===null?a.context=l:a.pendingContext=l,a=Qt(t),a.payload={element:i},s=s===void 0?null:s,s!==null&&(a.callback=s),i=Zt(e,a,t),i!==null&&(tt(i,e,t),La(i,e,t))}function Lc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<t?i:t}}function Yu(e,t){Lc(e,t),(e=e.alternate)&&Lc(e,t)}function qc(e){if(e.tag===13){var t=Ki(e,67108864);t!==null&&tt(t,e,67108864),Yu(e,67108864)}}var Cs=!0;function Tp(e,t,i,a){var l=w.T;w.T=null;var s=N.p;try{N.p=2,Vu(e,t,i,a)}finally{N.p=s,w.T=l}}function Ap(e,t,i,a){var l=w.T;w.T=null;var s=N.p;try{N.p=8,Vu(e,t,i,a)}finally{N.p=s,w.T=l}}function Vu(e,t,i,a){if(Cs){var l=Xu(a);if(l===null)Ou(e,t,a,Ns,i),Vc(e,a);else if(Cp(l,e,t,i,a))a.stopPropagation();else if(Vc(e,a),t&4&&-1<Mp.indexOf(e)){for(;l!==null;){var s=ji(l);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var n=yi(s.pendingLanes);if(n!==0){var u=s;for(u.pendingLanes|=2,u.entangledLanes|=2;n;){var r=1<<31-Ze(n);u.entanglements[1]|=r,n&=~r}Ft(s),(ie&6)===0&&(cs=St()+500,sl(0))}}break;case 13:u=Ki(s,2),u!==null&&tt(u,s,2),ms(),Yu(s,2)}if(s=Xu(a),s===null&&Ou(e,t,a,Ns,i),s===l)break;l=s}l!==null&&a.stopPropagation()}else Ou(e,t,a,null,i)}}function Xu(e){return e=Js(e),Qu(e)}var Ns=null;function Qu(e){if(Ns=null,e=Hi(e),e!==null){var t=Y(e);if(t===null)e=null;else{var i=t.tag;if(i===13){if(e=V(t),e!==null)return e;e=null}else if(i===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ns=e,null}function Yc(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(cf()){case ar:return 2;case lr:return 8;case bl:case ff:return 32;case sr:return 268435456;default:return 32}default:return 32}}var Zu=!1,ui=null,ri=null,di=null,ml=new Map,pl=new Map,oi=[],Mp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Vc(e,t){switch(e){case"focusin":case"focusout":ui=null;break;case"dragenter":case"dragleave":ri=null;break;case"mouseover":case"mouseout":di=null;break;case"pointerover":case"pointerout":ml.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":pl.delete(t.pointerId)}}function hl(e,t,i,a,l,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:i,eventSystemFlags:a,nativeEvent:s,targetContainers:[l]},t!==null&&(t=ji(t),t!==null&&qc(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Cp(e,t,i,a,l){switch(t){case"focusin":return ui=hl(ui,e,t,i,a,l),!0;case"dragenter":return ri=hl(ri,e,t,i,a,l),!0;case"mouseover":return di=hl(di,e,t,i,a,l),!0;case"pointerover":var s=l.pointerId;return ml.set(s,hl(ml.get(s)||null,e,t,i,a,l)),!0;case"gotpointercapture":return s=l.pointerId,pl.set(s,hl(pl.get(s)||null,e,t,i,a,l)),!0}return!1}function Xc(e){var t=Hi(e.target);if(t!==null){var i=Y(t);if(i!==null){if(t=i.tag,t===13){if(t=V(i),t!==null){e.blockedOn=t,bf(e.priority,function(){if(i.tag===13){var a=et();a=_s(a);var l=Ki(i,a);l!==null&&tt(l,i,a),Yu(i,a)}});return}}else if(t===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Es(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var i=Xu(e.nativeEvent);if(i===null){i=e.nativeEvent;var a=new i.constructor(i.type,i);Ks=a,i.target.dispatchEvent(a),Ks=null}else return t=ji(i),t!==null&&qc(t),e.blockedOn=i,!1;t.shift()}return!0}function Qc(e,t,i){Es(e)&&i.delete(t)}function Np(){Zu=!1,ui!==null&&Es(ui)&&(ui=null),ri!==null&&Es(ri)&&(ri=null),di!==null&&Es(di)&&(di=null),ml.forEach(Qc),pl.forEach(Qc)}function xs(e,t){e.blockedOn===t&&(e.blockedOn=null,Zu||(Zu=!0,T.unstable_scheduleCallback(T.unstable_NormalPriority,Np)))}var Ds=null;function Zc(e){Ds!==e&&(Ds=e,T.unstable_scheduleCallback(T.unstable_NormalPriority,function(){Ds===e&&(Ds=null);for(var t=0;t<e.length;t+=3){var i=e[t],a=e[t+1],l=e[t+2];if(typeof a!="function"){if(Qu(a||i)===null)continue;break}var s=ji(i);s!==null&&(e.splice(t,3),t-=3,Qn(s,{pending:!0,data:l,method:i.method,action:a},a,l))}}))}function yl(e){function t(r){return xs(r,e)}ui!==null&&xs(ui,e),ri!==null&&xs(ri,e),di!==null&&xs(di,e),ml.forEach(t),pl.forEach(t);for(var i=0;i<oi.length;i++){var a=oi[i];a.blockedOn===e&&(a.blockedOn=null)}for(;0<oi.length&&(i=oi[0],i.blockedOn===null);)Xc(i),i.blockedOn===null&&oi.shift();if(i=(e.ownerDocument||e).$$reactFormReplay,i!=null)for(a=0;a<i.length;a+=3){var l=i[a],s=i[a+1],n=l[Ge]||null;if(typeof s=="function")n||Zc(i);else if(n){var u=null;if(s&&s.hasAttribute("formAction")){if(l=s,n=s[Ge]||null)u=n.formAction;else if(Qu(l)!==null)continue}else u=n.action;typeof u=="function"?i[a+1]=u:(i.splice(a,3),a-=3),Zc(i)}}}function Ku(e){this._internalRoot=e}Os.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(p(409));var i=t.current,a=et();Bc(i,a,e,t,null,null)},Os.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Bc(e.current,2,null,e,null,null),ms(),t[ki]=null}};function Os(e){this._internalRoot=e}Os.prototype.unstable_scheduleHydration=function(e){if(e){var t=or();e={blockedOn:null,target:e,priority:t};for(var i=0;i<oi.length&&t!==0&&t<oi[i].priority;i++);oi.splice(i,0,e),i===0&&Xc(e)}};var Kc=G.version;if(Kc!=="19.1.0")throw Error(p(527,Kc,"19.1.0"));N.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(p(188)):(e=Object.keys(e).join(","),Error(p(268,e)));return e=C(t),e=e!==null?v(e):null,e=e===null?null:e.stateNode,e};var Ep={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:w,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var zs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zs.isDisabled&&zs.supportsFiber)try{ba=zs.inject(Ep),Qe=zs}catch{}}return gl.createRoot=function(e,t){if(!_(e))throw Error(p(299));var i=!1,a="",l=co,s=fo,n=mo,u=null;return t!=null&&(t.unstable_strictMode===!0&&(i=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(l=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(n=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(u=t.unstable_transitionCallbacks)),t=_c(e,1,!1,null,null,i,a,l,s,n,u,null),e[ki]=t.current,Du(e),new Ku(t)},gl.hydrateRoot=function(e,t,i){if(!_(e))throw Error(p(299));var a=!1,l="",s=co,n=fo,u=mo,r=null,m=null;return i!=null&&(i.unstable_strictMode===!0&&(a=!0),i.identifierPrefix!==void 0&&(l=i.identifierPrefix),i.onUncaughtError!==void 0&&(s=i.onUncaughtError),i.onCaughtError!==void 0&&(n=i.onCaughtError),i.onRecoverableError!==void 0&&(u=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(r=i.unstable_transitionCallbacks),i.formState!==void 0&&(m=i.formState)),t=_c(e,1,!0,t,i??null,a,l,s,n,u,r,m),t.context=Uc(null),i=t.current,a=et(),a=_s(a),l=Qt(a),l.callback=null,Zt(i,l,a),i=a,t.current.lanes=i,Fa(t,i),Ft(t),e[ki]=t.current,Du(e),new Os(t)},gl.version="19.1.0",gl}var nf;function _p(){if(nf)return Iu.exports;nf=1;function T(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(T)}catch(G){console.error(G)}}return T(),Iu.exports=Gp(),Iu.exports}var Up=_p();const fi={title:"Awesome Useful Websites",description:"探索互联网上的隐藏宝石，发现各种有用的在线工具和资源！",categories:[{name:"工具",originalName:"Tools",id:"tools",subcategories:[{name:"白板工具",originalName:"White Board",id:"white-board",websites:[{name:"TypeHere",url:"https://typehere.co/",description:"只能输入文字的空白网站。",isPaid:!1,isStudentFriendly:!1},{name:"PixelPaper",url:"https://pixelpaper.io/",description:"永久免费的数字白板，无需注册，可嵌入SaaS产品。",isPaid:!1,isStudentFriendly:!1},{name:"Excalidraw",url:"https://excalidraw.com/",description:"可以绘制手绘风格图表的白板。",isPaid:!1,isStudentFriendly:!1},{name:"Excalideck",url:"https://excalideck.com/",description:"基于Excalidraw创建手绘风格幻灯片的应用程序。",isPaid:!1,isStudentFriendly:!1},{name:"Blank Page",url:"https://blank.page/",description:"显示空白白页的简单网页。",isPaid:!1,isStudentFriendly:!1},{name:"Kid Pix",url:"https://kidpix.app/",description:"为儿童设计的位图绘图程序，提供有趣、用户友好的界面用于创意和交互式数字艺术作品。",isPaid:!1,isStudentFriendly:!1},{name:"Krita",url:"https://krita.org/en/",description:"为艺术家设计的免费开源数字绘画软件，提供插图、概念艺术和纹理绘画的高级工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"思维导图笔记",originalName:"Mind Map / Note Taking",id:"mind-map---note-taking",websites:[{name:"Relanote",url:"https://relanote.com/",description:"将您的笔记相互链接，形成思维网络。",isPaid:!1,isStudentFriendly:!1},{name:"Bubbl.us",url:"https://bubbl.us/",description:"在线思维导图。",isPaid:!1,isStudentFriendly:!1},{name:"MindMup",url:"https://www.mindmup.com/",description:"免费在线思维导图。",isPaid:!1,isStudentFriendly:!1},{name:"Anotepad",url:"https://anotepad.com/",description:"在线记事本。无需登录。可将笔记下载为PDF或Word文档。",isPaid:!1,isStudentFriendly:!1},{name:"Notes.io",url:"https://notes.io/",description:"基于Web的笔记应用程序。",isPaid:!1,isStudentFriendly:!1}]},{name:"图表制作",originalName:"Diagrams",id:"diagrams",websites:[{name:"Creately",url:"https://creately.com/",description:"用于头脑风暴、规划、执行和获取知识的数据连接可视化工作空间。",isPaid:!1,isStudentFriendly:!1},{name:"draw.io",url:"https://www.drawio.com/",description:"用于构建图表应用程序的开源、安全优先技术栈。可用于[网页使用](https://app.diagrams.net/?src=about)。",isPaid:!1,isStudentFriendly:!1},{name:"OrgPad",url:"https://orgpad.com/?ref=producthunt",description:"交互式在线思维导图。",isPaid:!1,isStudentFriendly:!1},{name:"Lucidchart",url:"https://www.lucidchart.com/pages/",description:"一个图表和流程图应用程序，将团队聚集在一起做出更好的决策并构建未来。",isPaid:!1,isStudentFriendly:!1},{name:"Learn Anything",url:"https://learn-anything.xyz/",description:"组织世界知识、探索连接并策划学习路径的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"文本处理",originalName:"Texts",id:"texts",websites:[{name:"Word Counter",url:"https://freecodetools.org/word-counter/",description:"字数统计工具。",isPaid:!1,isStudentFriendly:!1},{name:"Text Faces",url:"https://textfac.es/",description:"编写Unicode表情符号。",isPaid:!1,isStudentFriendly:!1},{name:"Title Case",url:"https://titlecase.com/",description:"将文本转换为各种大小写格式。",isPaid:!1,isStudentFriendly:!1},{name:"Fancy Text Generator",url:"https://lingojam.com/FancyTextGenerator",description:"将文本转换为各种字体样式。",isPaid:!1,isStudentFriendly:!1},{name:"Calligraphr",url:"https://www.calligraphr.com/en/",description:"将您的手写字体或书法转换为字体文件。",isPaid:!1,isStudentFriendly:!1},{name:"Dongerlist",url:"https://dongerlist.com/",description:"由Unicode字符组成的文本表情符号集合。",isPaid:!1,isStudentFriendly:!1},{name:"ASCII-art Tutorial",url:"https://stonestoryrpg.com/ascii_tutorial.html",description:"ASCII艺术教程。",isPaid:!1,isStudentFriendly:!1},{name:"Emoji Combos",url:"https://emojicombos.com/",description:"表情符号组合和序列集合。",isPaid:!1,isStudentFriendly:!1},{name:"ASCII World",url:"https://www.asciiworld.com/",description:"以ASCII艺术和教程为特色的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Rentry",url:"https://rentry.co/FMHY",description:"在线协作markdown编辑器。",isPaid:!1,isStudentFriendly:!1}]},{name:"浏览器自动化",originalName:"Automating browser",id:"automating-browser",websites:[{name:"Automa",url:"https://chrome.google.com/webstore/detail/automa/infppggnoaenmfagbfknfkancpbljcca?ref=producthunt",description:"用于自动化浏览器操作的Chrome扩展。",isPaid:!1,isStudentFriendly:!1},{name:"Browse.AI",url:"https://www.browse.ai/",description:"从任何网站提取、设置点击和监控数据的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Tango",url:"https://www.tango.us/",description:"用截图创建分步文档、操作手册和产品指南的工具。",isPaid:!1,isStudentFriendly:!1},{name:"BookmarkOS",url:"https://bookmarkos.com/bookmark-manager-finder",description:"允许您筛选各种书签管理器。",isPaid:!1,isStudentFriendly:!1}]},{name:"比较工具",originalName:"Comparison",id:"comparison",websites:[{name:"Social Media Messaging Apps Comparison",url:"https://docs.google.com/spreadsheets/d/1-UlA4-tslROBDS9IqHalWVztqZo7uxlCeKPQ-8uoFOU/edit#gid=0",description:"社交媒体消息应用的详细比较和分析。",isPaid:!1,isStudentFriendly:!1},{name:"DxOMark",url:"https://www.dxomark.com/category/smartphone-reviews/",description:"对智能手机、传感器、镜头和扬声器进行科学测试和数据分析。",isPaid:!1,isStudentFriendly:!1},{name:"TechSpecs Compare Phones",url:"https://techspecs.io/vs/",description:"比较手机规格参数。",isPaid:!1,isStudentFriendly:!1},{name:"TechSpecs",url:"https://techspecs.io/",description:"消费电子产品的搜索引擎。",isPaid:!1,isStudentFriendly:!1},{name:"Kimovil",url:"https://www.kimovil.com/en/",description:"比较智能手机和平板电脑的规格和价格。",isPaid:!1,isStudentFriendly:!1},{name:"DiffChecker",url:"https://www.diffchecker.com/",description:"比较文本、图像、PDF等文件以找出差异。",isPaid:!1,isStudentFriendly:!1},{name:"CodingFont",url:"https://www.codingfont.com/",description:"通过游戏化体验比较和寻找编程字体。",isPaid:!1,isStudentFriendly:!1},{name:"This vs That",url:"https://thisvsthat.io/",description:"输入两个事物来比较它们。",isPaid:!1,isStudentFriendly:!1},{name:"Secure Messaging Apps Comparison",url:"https://www.securemessagingapps.com/",description:"安全消息应用的比较平台。",isPaid:!1,isStudentFriendly:!1},{name:"RTINGS",url:"https://www.rtings.com/",description:"提供音视频设备的深度评测和比较，包括电视、显示器、耳机和音响，含详细测试和评级。",isPaid:!1,isStudentFriendly:!1}]},{name:"文件管理",originalName:"File",id:"file",websites:[{name:"Wormhole",url:"https://wormhole.app/",description:"通过端到端加密分享文件的平台，链接会自动过期。",isPaid:!1,isStudentFriendly:!1},{name:"Keybase",url:"https://keybase.io/",description:"端到端加密的安全消息和文件共享平台。",isPaid:!1,isStudentFriendly:!1},{name:"MediaFire",url:"https://www.mediafire.com/",description:"文件存储和共享平台（提供订阅选项）。",isPaid:!1,isStudentFriendly:!1},{name:"Zippyshare",url:"https://www.zippyshare.com/",description:"无需注册、无下载限制、免费且无限磁盘空间的文件分享服务。",isPaid:!1,isStudentFriendly:!1}]},{name:"格式转换",originalName:"Converter / Conversion",id:"converter---conversion",websites:[{name:"PDF2DOC",url:"https://pdf2doc.com/",description:"免费在线PDF转DOC转换器。",isPaid:!1,isStudentFriendly:!1},{name:"Online-Convert",url:"https://www.online-convert.com/",description:"在线转换不同格式的媒体文件。",isPaid:!1,isStudentFriendly:!1},{name:"JPG to PNG",url:"https://jpg.to-png.com/",description:"各种格式的免费文件转换工具。",isPaid:!1,isStudentFriendly:!1},{name:"Conversion-Tool",url:"https://www.conversion-tool.com/",description:"提供广泛的免费在线转换工具。",isPaid:!1,isStudentFriendly:!1},{name:"Zamzar",url:"https://www.zamzar.com/",description:"转换1100多种格式的文档、图像、视频和音频。",isPaid:!1,isStudentFriendly:!1},{name:"Web2PDFConvert",url:"https://www.web2pdfconvert.com/",description:"将网页或HTML转换为PDF或图像格式。",isPaid:!1,isStudentFriendly:!1},{name:"SmallPDF",url:"https://smallpdf.com/",description:"21种免费PDF转换、压缩和编辑工具。",isPaid:!1,isStudentFriendly:!1},{name:"Corrupt-a-File",url:"https://corrupt-a-file.net/",description:"在线损坏任何文件（使用风险自负）。",isPaid:!1,isStudentFriendly:!1},{name:"CloudConvert",url:"https://cloudconvert.com/",description:"支持200多种格式的在线文件转换器。",isPaid:!1,isStudentFriendly:!1},{name:"OnlineOCR",url:"https://www.onlineocr.net/",description:"带OCR支持的图片转文字转换器。",isPaid:!1,isStudentFriendly:!1},{name:"PDF Candy",url:"https://pdfcandy.com/",description:"处理PDF文件的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"PDFescape",url:"https://www.pdfescape.com/",description:"免费在线PDF编辑器和表单填写工具。",isPaid:!1,isStudentFriendly:!1},{name:"PrintIt",url:"https://printit.work/about",description:"将网页打印为PDF的服务，提供各种自定义选项。",isPaid:!1,isStudentFriendly:!1}]},{name:"单位转换",originalName:"Unit Conversion",id:"unit-conversion",websites:[{name:"UnitConverters",url:"https://www.unitconverters.net/",description:"转换各种单位的在线平台。",isPaid:!1,isStudentFriendly:!1},{name:"OnlineConversion",url:"https://onlineconversion.com/",description:"几乎可以将任何东西转换为任何其他东西的工具，包含数千个单位和数百万种转换。",isPaid:!1,isStudentFriendly:!1},{name:"Text to Binary Conversion",url:"https://www.online-toolz.com/tools/text-binary-convertor.php",description:"文本转二进制的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"CSSUnitConverter",url:"https://cssunitconverter.com/",description:"在PX、EM、REM、PT、英寸、厘米等单位之间转换，适用于网页和印刷设计。",isPaid:!1,isStudentFriendly:!1}]},{name:"视觉工具",originalName:"Visual",id:"visual",websites:[{name:"Unscreen",url:"https://www.unscreen.com/",description:"自动免费移除视频背景。",isPaid:!1,isStudentFriendly:!1},{name:"Remove.bg",url:"https://www.remove.bg/",description:"自动免费移除图像背景。",isPaid:!1,isStudentFriendly:!1},{name:"Foco Clipping",url:"https://www.fococlipping.com/",description:"移除图像背景。",isPaid:!1,isStudentFriendly:!1},{name:"Designify",url:"https://www.designify.com/",description:"通过自动移除背景、增强颜色、调整智能阴影等功能创建AI驱动的设计。",isPaid:!0,isStudentFriendly:!1},{name:"PfpMaker",url:"https://pfpmaker.com/",description:"从任何照片制作头像。",isPaid:!1,isStudentFriendly:!1},{name:"JPEG-Optimizer",url:"https://jpeg-optimizer.com/",description:"免费的在线数字照片和图像调整和压缩工具。",isPaid:!1,isStudentFriendly:!1},{name:"Extract.pics",url:"https://extract.pics/",description:"使用虚拟浏览器从任何公共网站提取图像。",isPaid:!1,isStudentFriendly:!1},{name:"Generated.Photos",url:"https://generated.photos/",description:"独特、无忧（AI生成）、免费下载的模特照片。",isPaid:!1,isStudentFriendly:!1},{name:"Zoom.it",url:"https://zoom.it/",description:"创建高分辨率、可缩放的图像。",isPaid:!1,isStudentFriendly:!1},{name:"VectorMagic",url:"https://vectormagic.com/",description:"将位图（JPG、PNG、GIF）转换为矢量图（PDF、SVG、EPS）。",isPaid:!1,isStudentFriendly:!1},{name:"Screenshot.Guru",url:"https://screenshot.guru/",description:"对网站和推文进行高分辨率屏幕截图。",isPaid:!1,isStudentFriendly:!1},{name:"Stolen Camera Finder",url:"https://www.stolencamerafinder.com/",description:"使用照片中存储的序列号在网上搜索用同一台相机拍摄的其他照片。",isPaid:!1,isStudentFriendly:!1},{name:"Ribbet",url:"https://www.ribbet.com/",description:"照片编辑工具。",isPaid:!1,isStudentFriendly:!1},{name:"Crossfade.io",url:"https://crossfade.io/",description:"从您喜爱的网站制作基于网络的视频混剪。",isPaid:!1,isStudentFriendly:!1},{name:"GoProHeroes",url:"https://goproheroes.com/",description:"网络上的GoPro视频。",isPaid:!1,isStudentFriendly:!1},{name:"Synthesia",url:"https://www.synthesia.io/",description:"在几分钟内从文本创建视频的AI视频创作平台。",isPaid:!0,isStudentFriendly:!1},{name:"ClipDrop",url:"https://clipdrop.co/",description:"在AI驱动下几秒钟内创建令人惊叹的视觉效果。",isPaid:!1,isStudentFriendly:!1},{name:"Reface",url:"https://hey.reface.ai/",description:"创建换脸视频，由AI驱动的移动应用。",isPaid:!1,isStudentFriendly:!1},{name:"PhotoSonic",url:"https://photosonic.writesonic.com/",description:"用像素描绘您梦想的AI，DALL-E的另一个版本。",isPaid:!1,isStudentFriendly:!1},{name:"Shottr",url:"https://shottr.cc/",description:"小巧快速的macOS截图工具，具有注释、滚动截图和云上传功能。",isPaid:!1,isStudentFriendly:!1},{name:"3D GIF Maker",url:"https://www.3dgifmaker.com/",description:"轻松从您的图像创建3D GIF。",isPaid:!1,isStudentFriendly:!1},{name:"EZGIF",url:"https://ezgif.com/",description:"用于创建和编辑GIF的在线GIF制作器和图像编辑器。",isPaid:!1,isStudentFriendly:!1},{name:"PimEyes",url:"https://pimeyes.com/en",description:"面部识别搜索引擎和反向图像搜索，用于查找包含特定人员的图像。",isPaid:!1,isStudentFriendly:!1},{name:"Visual Illusions",url:"https://sites.socsci.uci.edu/~ddhoff/illusions.html",description:"视觉错觉和演示的集合。",isPaid:!1,isStudentFriendly:!1},{name:"ByClickDownloader",url:"https://www.byclickdownloader.com/",description:"使用他们的软件以HD、MP3、MP4、AVI和其他格式备份各种网站的视频。",isPaid:!1,isStudentFriendly:!1},{name:"Reanimate",url:"https://reanimate.github.io/",description:"使用SVG和Haskell构建声明式动画。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"5000Best Tools",url:"https://5000best.com/tools/",description:"5000个工具。",isPaid:!1,isStudentFriendly:!1},{name:"10015.io",url:"https://10015.io/",description:"免费的多合一工具箱，用于各种任务。",isPaid:!1,isStudentFriendly:!1},{name:"UnTools",url:"https://untools.co/",description:"思维工具和框架集合。",isPaid:!1,isStudentFriendly:!1},{name:"TimeTravel Memento",url:"https://timetravel.mementoweb.org/",description:"在Internet Archive、Archive-It、大英图书馆、archive.today、GitHub等地方查找备忘录。",isPaid:!1,isStudentFriendly:!1},{name:"discu.eu",url:"https://discu.eu/",description:"通过每周新闻通讯、社交和机器人、浏览器扩展、书签工具跟上您关心的话题。",isPaid:!1,isStudentFriendly:!1},{name:"PromoWizard",url:"https://promowizard.softr.app/",description:"无需在YouTube上观看数小时内容即可获取促销代码。",isPaid:!1,isStudentFriendly:!1},{name:"Everybodywiki",url:"https://en.everybodywiki.com/Everybodywiki:Welcome",description:"从维基百科中拯救已删除的文章和被拒绝的草稿，支持多种语言，并欢迎新文章。",isPaid:!1,isStudentFriendly:!1},{name:"Lunar",url:"https://lunar.fyi/",description:"控制显示器的多功能应用程序。",isPaid:!1,isStudentFriendly:!1},{name:"GetHuman",url:"https://gethuman.com/",description:"更快地与知名公司的代表通话并获得更好的帮助。",isPaid:!1,isStudentFriendly:!1},{name:"S-ings Scratchpad",url:"https://www.s-ings.com/scratchpad/",description:"为快速笔记、计算和非正式写作设计的在线草稿本工具。",isPaid:!1,isStudentFriendly:!1},{name:"UFreeTools",url:"https://www.ufreetools.com/",description:"您的在线免费工具包。",isPaid:!1,isStudentFriendly:!1}]},{name:"动手制作",originalName:"DIY",id:"diy",subcategories:[],websites:[{name:"WikiHow",url:"https://www.wikihow.com/Main-Page",description:"创建和分享操作指南的协作平台。",isPaid:!1,isStudentFriendly:!1},{name:"ManualsLib",url:"https://www.manualslib.com/",description:"用户手册和指南的在线存储库。",isPaid:!1,isStudentFriendly:!1},{name:"This to That",url:"https://thistothat.com/",description:"学习如何将不同材料粘合在一起。",isPaid:!1,isStudentFriendly:!1},{name:"HowStuffWorks",url:"https://www.howstuffworks.com/",description:"通过深入的解释和文章探索事物的工作原理。",isPaid:!1,isStudentFriendly:!1},{name:"WonderHowTo",url:"https://www.wonderhowto.com/",description:"通过教学视频和分步指南学习几乎任何事情。",isPaid:!1,isStudentFriendly:!1},{name:"Dummies",url:"https://www.dummies.com/",description:"一系列教学/参考书籍。",isPaid:!1,isStudentFriendly:!1},{name:"DoItYourself",url:"https://www.doityourself.com/",description:"DIY项目和家庭装修的资源。",isPaid:!1,isStudentFriendly:!1},{name:"JScreenFix",url:"https://www.jscreenfix.com/",description:"用于修复缺陷像素的像素修复算法，对卡住的像素特别有效。无需安装，且免费。",isPaid:!1,isStudentFriendly:!1},{name:"Donkey Car",url:"https://www.donkeycar.com/",description:"小型汽车的开源DIY自动驾驶平台。它将遥控车与树莓派结合，由Python（tornado、keras、tensorflow、opencv等）驱动。",isPaid:!1,isStudentFriendly:!1},{name:"Instructables",url:"https://www.instructables.com/",description:"发现和分享DIY项目的平台。",isPaid:!1,isStudentFriendly:!1},{name:"iFixit",url:"https://www.ifixit.com/",description:"为各种电子产品、家电和其他产品提供免费维修指南和手册，由社区贡献，赋予用户自己修理物品的能力。",isPaid:!1,isStudentFriendly:!1},{name:"Fix It Club",url:"https://fixitclub.com/",description:"通过有用的指南节省家庭维修费用。",isPaid:!1,isStudentFriendly:!1},{name:"BookCrossing",url:"https://bookcrossing.com/",description:'将您的书籍"放归野外"供陌生人发现，或对另一个BookCrossing成员进行"受控释放"，并通过来自世界各地的日记条目跟踪它们的旅程。',isPaid:!1,isStudentFriendly:!1},{name:"Dimensions",url:"https://www.dimensions.com/",description:"为各个类别提供尺寸和测量视觉参考设计的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Repair Clinic",url:"https://www.repairclinic.com/",description:"北美历史最悠久的正品家电、暖通空调和户外动力设备零件来源，提供专家建议、操作指导资源和DIY维修支持。受到专业人士和房主信赖，该网站提供高质量的OEM零件和指导，帮助用户成功完成维修。",isPaid:!1,isStudentFriendly:!1}]},{name:"文化",originalName:"Culture",id:"culture",subcategories:[],websites:[{name:"Cultural Atlas",url:"https://culturalatlas.sbs.com.au/",description:"提供文化背景综合信息的教育资源。",isPaid:!1,isStudentFriendly:!1},{name:"QuoteMaster",url:"https://www.quotemaster.org/",description:"拥有98,683个类别和1,488,431条引语的平台。",isPaid:!1,isStudentFriendly:!1},{name:"FactSlides",url:"https://www.factslides.com/",description:"提供1001个关于各种主题的事实，并附有来源。",isPaid:!1,isStudentFriendly:!1},{name:"Starkey Comics",url:"https://starkeycomics.com/",description:"展示关于文化和语言的彩色图像和帖子的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Unusual Wikipedia Articles",url:"https://en.wikipedia.org/wiki/Wikipedia:Unusual_articles",description:"不寻常维基百科文章的汇编。",isPaid:!1,isStudentFriendly:!1},{name:"List of Common Misconceptions",url:"https://en.wikipedia.org/wiki/List_of_common_misconceptions",description:"列出常见误解的维基百科页面。",isPaid:!1,isStudentFriendly:!1},{name:"Behind the Name",url:"https://www.behindthename.com/",description:"提供名字的词源和历史。",isPaid:!1,isStudentFriendly:!1},{name:"Behind the Surname",url:"https://surnames.behindthename.com/",description:"提供姓氏的词源和历史。",isPaid:!1,isStudentFriendly:!1},{name:"Nameberry",url:"https://nameberry.com/",description:"专家婴儿取名平台，包括流行名字、独特名字、女孩名字、男孩名字和性别中性名字。",isPaid:!1,isStudentFriendly:!1},{name:"Library of Juggling",url:"https://libraryofjuggling.com/",description:"将所有流行（以及可能不那么流行）的杂技技巧整理在一个有组织的地方的在线资源。",isPaid:!1,isStudentFriendly:!1},{name:"Toaster Central",url:"https://toastercentral.com/",description:"工作中的古董烤面包机收藏。",isPaid:!1,isStudentFriendly:!1},{name:"All About Berlin",url:"https://allaboutberlin.com/",description:"为计划在柏林定居的个人提供指南和信息的平台。包括获得签证、找工作、租房等详细信息。",isPaid:!1,isStudentFriendly:!1},{name:"Unita",url:"https://unita.co/",description:"发现、比较和评论30多个类别中最佳社区、智囊团和在线群组的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Escape Room Tips",url:"https://escaperoomtips.com/",description:"密室逃脱的技巧、诀窍和谜题",isPaid:!1,isStudentFriendly:!1}]},{name:"语言学习",originalName:"Language",id:"language",subcategories:[{name:"语法",originalName:"Grammar",id:"grammar",websites:[{name:"GrammarBook",url:"https://www.grammarbook.com/english_rules.asp",description:"英语语法规则的综合指南。",isPaid:!1,isStudentFriendly:!1},{name:"The Punctuation Guide",url:"https://www.thepunctuationguide.com/index.html",description:"提供美式标点符号规则指南的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Progressive Punctuation",url:"https://progressivepunctuation.com/",description:"非标准标点符号的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Purdue OWL",url:"https://owl.purdue.edu/site_map.html",description:"普渡大学在线写作实验室，提供写作、语法和引用格式的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Towson University Online Writing Support",url:"https://webapps.towson.edu/ows/index.asp",description:"在线写作支持和语法资源。",isPaid:!1,isStudentFriendly:!1},{name:"Grammar Monster",url:"https://www.grammar-monster.com/index.html",description:"提供免费英语语法课程和测试的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Fraze It",url:"https://fraze.it/",description:"拥有超过1亿个句子的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"词汇释义",originalName:"Words & Meanings",id:"words---meanings",websites:[{name:"Educalingo",url:"https://educalingo.com/en/dic-en",description:"查找单词的同义词、用法、趋势、统计等信息的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Fine Dictionary",url:"https://www.finedictionary.com/",description:"关于单词的综合信息。",isPaid:!1,isStudentFriendly:!1},{name:"Crown Academy English",url:"https://www.crownacademyenglish.com/articles/",description:"提供各种英语语言概念的简单、简洁和清晰解释。",isPaid:!1,isStudentFriendly:!1},{name:"Ask Difference",url:"https://www.askdifference.com/",description:"提供两个相似主题之间简短而简洁的差异。",isPaid:!1,isStudentFriendly:!1},{name:"Key Differences",url:"https://keydifferences.com/",description:"专注于呈现差异和比较的网站。",isPaid:!1,isStudentFriendly:!1},{name:"DifferenceBetween.info",url:"https://www.differencebetween.info/",description:"提供不同概念之间的描述性分析和比较。",isPaid:!1,isStudentFriendly:!1},{name:"Wayne State University's List of Words",url:"https://wordwarriors.wayne.edu/list",description:"韦恩州立大学策划的值得更广泛使用的单词汇编。",isPaid:!1,isStudentFriendly:!1},{name:"All Acronyms",url:"https://www.allacronyms.com/",description:"社区驱动的缩略词和简称词典。",isPaid:!1,isStudentFriendly:!1},{name:"How to Professionally Say",url:"https://howtoprofessionallysay.akashrajpurohit.com/",description:"日常专业交流指南，帮助您以专业语调应对各种情况。",isPaid:!1,isStudentFriendly:!1},{name:"Business English Resources",url:"https://www.businessenglishresources.com/",description:"用于提高商务英语技能的免费资源集合。",isPaid:!1,isStudentFriendly:!1},{name:"Digital Glossary",url:"https://www.digital-glossary.com/",description:"为数字环境提供土耳其语、英语和德语术语词汇。",isPaid:!1,isStudentFriendly:!1},{name:"Bilim Terimleri",url:"https://terimler.org/",description:"为各种术语提供解释和定义的土耳其语平台。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"YouGlish",url:"https://youglish.com/",description:"使用YouTube提高您的英语发音。",isPaid:!1,isStudentFriendly:!1},{name:"Voscreen",url:"https://www.voscreen.com/",description:"提供英语句子视频片段的平台；通过选择释义句子来测试您的理解能力。",isPaid:!1,isStudentFriendly:!1},{name:"News in Levels",url:"https://www.newsinlevels.com/",description:"为英语学习者量身定制的世界新闻。",isPaid:!1,isStudentFriendly:!1},{name:"Pink Trombone",url:"https://dood.al/pinktrombone/",description:"使用动画语音盒对人类口腔及其声音的交互式模拟。",isPaid:!1,isStudentFriendly:!1},{name:"Japanese Wiki Corpus",url:"https://www.japanese-wiki-corpus.org/",description:"从维基百科京都文章的日英双语语料库生成的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Latin Phrases",url:"https://latin-phrases.co.uk/",description:"查找拉丁短语翻译的参考资料。",isPaid:!1,isStudentFriendly:!1},{name:"Prismatext",url:"https://prismatext.com/",description:"将最有用的外语单词和短语融入您最喜爱的小说和故事中。",isPaid:!1,isStudentFriendly:!1},{name:"Ponly",url:"https://ponly.com/about/",description:"包含有趣幽默内容和笑话的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Tongue-Twister",url:"https://tongue-twister.net/",description:"世界最大的绕口令集合，包含118种语言的3660个条目",isPaid:!1,isStudentFriendly:!1},{name:"OLAC (Open Language Archives Community)",url:"http://olac.ldc.upenn.edu/",description:"致力于创建语言资源全球虚拟图书馆的国际机构和个人网络，专注于数字档案实践并提供可互操作的语言数据访问存储库。",isPaid:!1,isStudentFriendly:!1}]},{name:"旅行",originalName:"Travel",id:"travel",subcategories:[{name:"环球旅行",originalName:"Globetrotting",id:"globetrotting",websites:[{name:"Random Street View",url:"https://randomstreetview.com/",description:"足不出户探索世界各地的街道。",isPaid:!1,isStudentFriendly:!1},{name:"Virtual Vacation",url:"https://virtualvacation.us/",description:"在家中舒适地进行环球虚拟旅行。",isPaid:!1,isStudentFriendly:!1},{name:"MapCrunch",url:"https://www.mapcrunch.com/",description:"通过传送到随机位置，通过Google街景体验世界。",isPaid:!1,isStudentFriendly:!1}]},{name:"时间工具",originalName:"Time",id:"time",websites:[{name:"Every Time Zone",url:"https://everytimezone.com/",description:"不同国家的可视化时区比较。",isPaid:!1,isStudentFriendly:!1},{name:"Time and Date",url:"https://www.timeanddate.com/",description:"提供日历、时钟和各种时间相关信息。",isPaid:!1,isStudentFriendly:!1},{name:"Time.is",url:"https://time.is/",description:"以51种语言显示任何时区的精确官方原子钟时间，覆盖超过700万个地点。",isPaid:!1,isStudentFriendly:!1}]},{name:"航班",originalName:"Flight",id:"flight",websites:[{name:"SeatGuru",url:"https://seatguru.com/",description:"探索1,278架飞机的座位图找到您的座位。",isPaid:!1,isStudentFriendly:!1},{name:"Flightradar24",url:"https://www.flightradar24.com/43,24.35/7",description:"全球航班跟踪服务，提供世界各地数千架飞机的实时信息。",isPaid:!1,isStudentFriendly:!1},{name:"Skyscanner",url:"https://www.skyscanner.co.in/",description:"航班搜索引擎，允许用户按日期、价格和预算搜索航班。",isPaid:!1,isStudentFriendly:!1}]},{name:"天气",originalName:"Weather",id:"weather",websites:[{name:"Hint.fm Wind Map",url:"https://hint.fm/wind/",description:"美国上空风流的精美轨迹。",isPaid:!1,isStudentFriendly:!1},{name:"Windy",url:"https://www.windy.com/",description:"任何地点的综合天气信息。",isPaid:!1,isStudentFriendly:!1},{name:"Zoom Earth",url:"https://zoom.earth/",description:"世界的实时可视化，跟踪热带风暴、飓风、恶劣天气、野火等。",isPaid:!1,isStudentFriendly:!1},{name:"Earth Nullschool",url:"https://earth.nullschool.net/",description:"超级计算机预测的全球天气状况可视化，每三小时更新一次。",isPaid:!1,isStudentFriendly:!1},{name:"OpenWeatherMap",url:"https://openweathermap.org/",description:"以快速优雅的方式提供天气预报、新闻播报和历史天气数据的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Radiosondy",url:"https://radiosondy.info/",description:"跟踪气象无线电探空仪，提供当前和过去探空仪飞行的数据库，包括发射点、类型、最后帧、航向、速度、高度和频率等信息。（注：无线电探空仪是通常由气象气球携带进入大气层的电池供电遥测仪器。）",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Airheart - Travel Restrictions",url:"https://airheart.com/travel-restrictions/united-states-vaccinated",description:"了解您的旅行限制和要求，包括COVID-19限制。",isPaid:!1,isStudentFriendly:!1},{name:"Country Code",url:"https://countrycode.org/",description:"打电话到世界任何地方的指南，提供国际拨号的国家代码。",isPaid:!1,isStudentFriendly:!1},{name:"Passport Index",url:"https://www.passportindex.org/",description:"探索来自世界各地的护照信息，包括排名和详细信息。",isPaid:!1,isStudentFriendly:!1},{name:"Countries Been",url:"https://www.countriesbeen.com/",description:"跟踪和列出您访问过的国家的移动应用，提供各种功能。",isPaid:!1,isStudentFriendly:!1},{name:"Couchsurfing",url:"https://www.couchsurfing.com/",description:"与免费分享家园和体验的全球社区联系。",isPaid:!1,isStudentFriendly:!1},{name:"Puffin Maps",url:"https://www.puffinmaps.com/",description:"无广告的一体化旅行规划器，帮助您组织旅行计划。",isPaid:!1,isStudentFriendly:!1},{name:"AllTrails",url:"https://www.alltrails.com/",description:"探索包含30万条步道的数据库，附有户外爱好者的评论和照片。",isPaid:!1,isStudentFriendly:!1},{name:"Wanderprep",url:"https://www.wanderprep.com/",description:"之前提供装备、应用和旅行技巧建议，让旅程更智能。",isPaid:!1,isStudentFriendly:!1},{name:"Looria",url:"https://looria.com/",description:"查找诚实产品信息的可信平台。",isPaid:!1,isStudentFriendly:!1},{name:"Trip Destination App (iOS)",url:"https://apps.apple.com/us/app/id1580599572",description:"用于搜索和规划旅行目的地的免费iPhone应用。",isPaid:!1,isStudentFriendly:!1},{name:"Freecycle",url:"https://www.freecycle.org/",description:"人们在当地社区免费赠送和获取物品的网络。",isPaid:!1,isStudentFriendly:!1},{name:"Roadtrippers",url:"https://roadtrippers.com/",description:"规划您的路线并使用逐步导航探索公路旅行中的各种景点。",isPaid:!1,isStudentFriendly:!1},{name:"Mountain Project",url:"https://www.mountainproject.com/",description:"免费的、众包的世界攀岩目的地指南。",isPaid:!1,isStudentFriendly:!1},{name:"Welcome to My Garden",url:"https://welcometomygarden.org/",description:"为慢旅行者在私人花园中提供免费露营点的非营利网络。",isPaid:!1,isStudentFriendly:!1},{name:"Warmshowers",url:"https://www.warmshowers.org/",description:"自行车旅行者和在旅途中支持他们的主人的社区。",isPaid:!1,isStudentFriendly:!1},{name:"Slowby",url:"https://www.slowby.travel/",description:"提供精心策划的慢旅行行程的平台，获得独特的旅行体验。",isPaid:!1,isStudentFriendly:!1}]},{name:"健康",originalName:"Health",id:"health",subcategories:[{name:"空气质量",originalName:"Air Quality",id:"air-quality",websites:[{name:"Air Quality Index (European Environment Agency)",url:"http://airindex.eea.europa.eu/",description:"提供欧洲实时空气质量数据，具有可视化空气污染水平及其对公共健康影响的交互式地图。",isPaid:!1,isStudentFriendly:!1},{name:"Berkeley Earth",url:"http://berkeleyearth.org/",description:"专注于提供准确和全面的空气质量和气候数据的非营利组织，提供全球环境数据可视化工具。",isPaid:!1,isStudentFriendly:!1},{name:"World Air Quality Index",url:"https://waqi.info/",description:"提供来自世界各地的实时空气质量信息，提供交互式地图和污染水平及其对健康影响的详细数据。",isPaid:!1,isStudentFriendly:!1},{name:"IQAir",url:"https://www.iqair.com/",description:"运营世界上最大的免费实时空气质量监测平台，为个人、研究人员和政府提供关键数据，以监测和解决空气污染问题，最终帮助保护全球公共健康。",isPaid:!1,isStudentFriendly:!1}]},{name:"美食",originalName:"Food",id:"food",websites:[{name:"GoBento",url:"https://www.gobento.com/",description:"专注于改善高风险成员健康和福祉的参与平台，特别是那些面临食品不安全问题的成员。",isPaid:!1,isStudentFriendly:!1},{name:"MyFridgeFood",url:"https://myfridgefood.com/",description:"允许用户勾选他们拥有的食材来寻找可以用这些食材制作的食谱的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Just the Recipe",url:"https://www.justtherecipe.com/",description:"提供来自任何食谱网站的直接说明，无广告和弹窗的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Two Peas and Their Pod",url:"https://www.twopeasandtheirpod.com/",description:"提供各种食谱的食谱网站。",isPaid:!1,isStudentFriendly:!1},{name:"HelloFresh",url:"https://www.hellofresh.com/",description:"餐食套装配送服务，用户可以选择食谱，HelloFresh将食材直接送到他们家门口。",isPaid:!1,isStudentFriendly:!1}]},{name:"庭院护理",originalName:"Lawn/Yard care",id:"lawn-yard-care",websites:[{name:"Healthy Yards",url:"https://healthyyards.org",description:"提供环保和可持续草坪护理实践的资源和技巧，重点关注对当地野生动物和环境的益处。",isPaid:!1,isStudentFriendly:!1},{name:"Homegrown National Park",url:"https://homegrownnationalpark.org",description:"提供关于通过种植本土物种创建野生动物走廊和促进生物多样性的信息。",isPaid:!1,isStudentFriendly:!1},{name:"Butterfly Conservation",url:"https://butterfly-conservation.org",description:"分享蝴蝶和飞蛾保护的详细信息，包括物种保护和参与机会。",isPaid:!1,isStudentFriendly:!1},{name:"Xerces Society",url:"https://xerces.org",description:"提供传粉媒介和无脊椎动物保护指导，包括栖息地恢复和可持续实践。",isPaid:!1,isStudentFriendly:!1},{name:"Beyond Pesticides",url:"https://beyondpesticides.org",description:"提供有机和可持续实践的资源，以减少农药使用并保护公众健康和环境。",isPaid:!1,isStudentFriendly:!1},{name:"National Pesticide Information Center",url:"https://npic.orst.edu",description:"为消费者和健康专业人士提供基于科学的农药使用信息、安全指南和事实表。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"MuscleWiki",url:"https://musclewiki.com/",description:"了解您的身体和肌肉的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Just a Minute",url:"https://jinay.dev/just-a-minute/",description:"在一分钟内测试您的时间感知能力（对时间流逝的感觉）。",isPaid:!1,isStudentFriendly:!1},{name:"FutureMe",url:"https://www.futureme.org/",description:"给未来的自己写信的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Strobe.Cool",url:"https://strobe.cool/",description:"使用频闪效果创建视觉刺激。",isPaid:!1,isStudentFriendly:!1},{name:"UnTools",url:"https://untools.co/",description:"思维工具和框架的集合，帮助解决问题、决策制定和理解系统。",isPaid:!1,isStudentFriendly:!1},{name:"Puzzle Loop",url:"https://www.puzzle-loop.com/",description:"提供规则简单但解决方案具有挑战性的逻辑谜题的平台。",isPaid:!1,isStudentFriendly:!1},{name:"What Should You Do with Your Life?",url:"https://guzey.com/personal/what-should-you-do-with-your-life/",description:"提供人生决策方向和建议的文章。",isPaid:!1,isStudentFriendly:!1},{name:"InnerBody",url:"https://www.innerbody.com/",description:"研究健康产品、服务等的评论和研究。",isPaid:!1,isStudentFriendly:!1},{name:"Sleep Calculator",url:"https://sleepcalculator.com/",description:"帮助用户根据期望的起床时间确定最佳就寝时间的工具，优化睡眠周期以获得更好的休息和警觉性。",isPaid:!1,isStudentFriendly:!1},{name:"SimpleLab",url:"https://gosimplelab.com/",description:"使用基于云的平台在美国提供快速、可靠的环境测试，用于高效的采样、测试和数据管理。",isPaid:!1,isStudentFriendly:!1}]},{name:"音乐音频",originalName:"Music / Audio",id:"music---audio",subcategories:[{name:"音乐发现",originalName:"Find Music",id:"find-music",websites:[{name:"Lalal.ai",url:"https://www.lalal.ai/",description:"从任何音频中提取人声、伴奏和各种乐器。",isPaid:!1,isStudentFriendly:!1},{name:"Audd.io",url:"https://audd.io/",description:"从声音或流媒体中识别音乐或正在播放的内容。",isPaid:!1,isStudentFriendly:!1},{name:"Music-Map",url:"https://www.music-map.com/",description:"根据您的偏好发现相似的音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Commercial Tunage",url:"https://www.commercialtunage.com/",description:"识别广告中播放的歌曲。",isPaid:!1,isStudentFriendly:!1}]},{name:"免费音乐",originalName:"Free Music",id:"free-music",websites:[{name:"Pretzel Rocks",url:"https://www.pretzel.rocks/",description:"为Twitch和YouTube提供流媒体安全音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Incompetech",url:"https://incompetech.com/",description:"免版税音乐集合。",isPaid:!1,isStudentFriendly:!1},{name:"Chosic",url:"https://www.chosic.com/",description:"免费背景音乐，可用于商业和非商业用途。",isPaid:!1,isStudentFriendly:!1}]},{name:"混音",originalName:"Mix Sounds",id:"mix-sounds",websites:[{name:"Hidden Life Radio",url:"https://hiddenliferadio.com/",description:"由马萨诸塞州剑桥市树木生物数据生成的音乐直播流。",isPaid:!1,isStudentFriendly:!1},{name:"Noisli",url:"https://www.noisli.com/",description:"创建和收听背景声音，以提高专注力和生产力，或放松身心。",isPaid:!1,isStudentFriendly:!1},{name:"Soundrop",url:"https://naim30.github.io/soundrop/",description:"交互式音乐播放器，您可以创建生成音乐的美丽图案。",isPaid:!1,isStudentFriendly:!1},{name:"SoundLove",url:"https://www.producthunt.com/posts/soundlove",description:"帮助您根据心情发现和创建播放列表的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Rainy Mood",url:"https://rainymood.com/",description:"享受舒缓的雨声，用于放松、睡眠和学习。",isPaid:!1,isStudentFriendly:!1},{name:"I Miss the Office",url:"https://imisstheoffice.eu/",description:"办公室噪音生成器，提供现代办公室生活的环境声音，帮助在家工作时重现办公室氛围。",isPaid:!1,isStudentFriendly:!1}]},{name:"音乐理论",originalName:"Music Theory",id:"music-theory",websites:[{name:"Teoria",url:"https://www.teoria.com/",description:"",isPaid:!1,isStudentFriendly:!1},{name:"MusicTheory.net",url:"https://www.musictheory.net/",description:"",isPaid:!1,isStudentFriendly:!1},{name:"All About Music Theory",url:"https://www.allaboutmusictheory.com/",description:"钢琴键盘、音乐记谱法、大调音阶",isPaid:!1,isStudentFriendly:!1},{name:"Studio Guru - Note Frequency Chart",url:"https://studioguru.co/producer-tools/note-frequency-chart/",description:"",isPaid:!1,isStudentFriendly:!1}]},{name:"押韵",originalName:"Rhyme",id:"rhyme",websites:[{name:"RhymeZone",url:"https://www.rhymezone.com/",description:"查找押韵词、同义词、形容词等。",isPaid:!1,isStudentFriendly:!1},{name:"Rhymer",url:"https://rhymer.com/",description:"免费在线押韵词典。",isPaid:!1,isStudentFriendly:!1}]},{name:"Spotify工具",originalName:"Spotify",id:"spotify",websites:[{name:"Chosic",url:"https://www.chosic.com/spotify-playlist-analyzer/",description:"通过分析您的播放列表发现新音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Pudding",url:"https://pudding.cool/2020/12/judge-my-spotify/",description:"训练来评估音乐品味的A.I.。",isPaid:!1,isStudentFriendly:!1},{name:"Discoverify Music",url:"https://www.discoverifymusic.com/login",description:"根据您的品味发现新音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Spottr",url:"https://spottr.vercel.app/login",description:"查看您的Spotify统计数据。",isPaid:!1,isStudentFriendly:!1},{name:"Playlist Mutator",url:"https://playlistmutator.com/",description:"在React中变异现有播放列表。",isPaid:!1,isStudentFriendly:!1},{name:"TuneMyMusic",url:"https://www.tunemymusic.com/",description:"在音乐服务之间传输播放列表。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"KHInsider",url:"https://downloads.khinsider.com/",description:"提供MP3和无损格式的视频和PC游戏原声音乐下载。",isPaid:!1,isStudentFriendly:!1},{name:"Online Tone Generator",url:"https://onlinetonegenerator.com/",description:"生成音调，您也可以以WAV格式下载它们。",isPaid:!1,isStudentFriendly:!1},{name:"Tello Music Charts",url:"https://music.tello.app/",description:"查找世界各国的最新音乐排行榜。",isPaid:!1,isStudentFriendly:!1},{name:"Music Lab",url:"https://musiclab.chromeexperiments.com/Experiments",description:"通过有趣的动手实验使音乐学习更加容易。",isPaid:!1,isStudentFriendly:!1},{name:"Rap4Ever",url:"https://www.rap4ever.org/",description:"探索说唱歌曲、歌词、混音带、专辑、艺术家等。",isPaid:!1,isStudentFriendly:!1},{name:"Every Noise",url:"https://everynoise.com/",description:"算法生成的音乐流派空间散点图的持续尝试。",isPaid:!1,isStudentFriendly:!1},{name:"MP3Cut",url:"https://mp3cut.net/",description:"在线修剪或切割任何音频文件。",isPaid:!1,isStudentFriendly:!1},{name:"MP3Gain",url:"https://mp3gain.flowsoft7.com/",description:"增加、减少和标准化MP3音频文件的音量水平（每个文件60M大小限制）。",isPaid:!1,isStudentFriendly:!1},{name:"Magic Playlist",url:"https://create.magicplaylist.co/#/?_k=m5jobg",description:"输入您最喜欢的歌曲并创建完美的播放列表。",isPaid:!1,isStudentFriendly:!1},{name:"Moises AI",url:"https://moises.ai/",description:"以任何调性、任何速度与您最喜欢的艺术家一起演奏。",isPaid:!1,isStudentFriendly:!1},{name:"Drumeo",url:"https://www.drumeo.com/",description:"与世界最好的老师学习打鼓。",isPaid:!1,isStudentFriendly:!1},{name:"SoundLove",url:"https://soundlove.se/",description:"不寻常的合成算法，为音乐增添随机性和不可预测性。",isPaid:!1,isStudentFriendly:!1},{name:"Audionautix",url:"https://audionautix.com/",description:"由Jason Shaw作曲和制作的音乐，免费下载和使用（甚至可用于商业目的）。",isPaid:!1,isStudentFriendly:!1},{name:"Typatone",url:"https://typatone.com/",description:"通过在键盘上打字生成您自己的音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Incredibox",url:"https://www.incredibox.com/",description:"音乐应用程序，让您在快乐的打击乐手团队帮助下创建自己的音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Sampurr",url:"https://www.sampurr.com/",description:"从网络上采样音频。",isPaid:!1,isStudentFriendly:!1},{name:"Audiocheck",url:"https://www.audiocheck.net/",description:"在线测试您的音频设备。",isPaid:!1,isStudentFriendly:!1},{name:"Mixlr",url:"https://mixlr.com/",description:"在线分享高质量实时音频。使用任何音源广播，邀请人们收听，并实时聊天（仅收听免费）。",isPaid:!0,isStudentFriendly:!1},{name:"Learn Choral Music",url:"https://www.learnchoralmusic.co.uk/",description:"John的MIDI文件集合，包含以声音为重点的知名合唱作品，可免费下载。",isPaid:!1,isStudentFriendly:!1},{name:"Bandura Festival",url:"https://bandura.ukrzen.in.ua/en#lvivbandurfest",description:"在线班杜拉琴，一种传统的乌克兰乐器。",isPaid:!1,isStudentFriendly:!1},{name:"AllMusic",url:"https://www.allmusic.com/",description:"提供关于专辑、艺术家、歌曲和乐队的全面深入信息，为音乐爱好者提供宝贵资源。",isPaid:!1,isStudentFriendly:!1},{name:"ASMR Microphones",url:"https://asmrmicrophones.com",description:"提供各种ASMR麦克风的评测、比较和专家意见，帮助用户选择最合适的设备。",isPaid:!1,isStudentFriendly:!1}]},{name:"影视娱乐",originalName:"Movies and Series",id:"movies-and-series",subcategories:[{name:"动漫",originalName:"Anime",id:"anime",websites:[{name:"Reddit Top Anime Streaming Sites",url:"https://reddit.com/r/streamingAnime/wiki/topsites/",description:"Reddit上列出顶级动漫流媒体网站的Wiki页面。",isPaid:!1,isStudentFriendly:!1},{name:"Reddit Legal Anime Streams",url:"https://reddit.com/r/anime/wiki/legal_streams/",description:"提供Reddit上合法动漫流媒体网站列表的Wiki页面。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Kanopy",url:"https://www.kanopy.com/en/",description:"在图书馆或大学支持下免费提供数千部电影的流媒体平台。",isPaid:!1,isStudentFriendly:!0},{name:"Movie Map",url:"https://www.movie-map.com/",description:"根据您的偏好找到相似的电影。",isPaid:!1,isStudentFriendly:!1},{name:"Reddit Movie Suggestions",url:"https://www.reddit.com/r/MovieSuggestions/wiki/faq",description:"各种类型的电影推荐列表。",isPaid:!1,isStudentFriendly:!1},{name:"A Good Movie to Watch",url:"https://agoodmovietowatch.com/",description:"精心挑选的高评分电影和电视剧。",isPaid:!1,isStudentFriendly:!1},{name:"Tubi TV",url:"https://tubitv.com/home",description:"提供各种电影和电视剧的免费流媒体服务。",isPaid:!1,isStudentFriendly:!1},{name:"Tiii.me",url:"https://tiii.me/",description:"计算您观看电视剧的总时间。",isPaid:!1,isStudentFriendly:!1},{name:"JustWatch",url:"https://www.justwatch.com/",description:"查找和流播电影与电视剧的指导平台。",isPaid:!1,isStudentFriendly:!1},{name:"Movie Settings Database",url:"https://www.moviesettingsdatabase.com/",description:"按场景整理的超过30,000部电影和电视剧的有序集合。",isPaid:!1,isStudentFriendly:!1},{name:"Reelgood Netflix Roulette",url:"https://reelgood.com/roulette/netflix",description:"随机推荐Netflix上可观看的电影或电视剧。",isPaid:!1,isStudentFriendly:!1},{name:"Reelgood",url:"https://reelgood.com/",description:"浏览、搜索并观看来自150多个服务的电视剧和电影，包括Netflix、Hulu、HBO、Disney+、Prime Video等。",isPaid:!1,isStudentFriendly:!1},{name:"Movie Sounds",url:"https://movie-sounds.org/",description:"提供短音频片段和特效的免费电影引言档案。",isPaid:!1,isStudentFriendly:!1},{name:"Tunefind",url:"https://www.tunefind.com/",description:"发现您喜爱的电视剧和电影中的音乐。",isPaid:!1,isStudentFriendly:!1},{name:"IMSDB",url:"https://imsdb.com/",description:"网络上最大的电影剧本集合。",isPaid:!1,isStudentFriendly:!1},{name:"Physics in Film and TV",url:"https://physicsinfilmandtv.wordpress.com/",description:"探索电影和电视中物理概念呈现的博客。",isPaid:!1,isStudentFriendly:!1},{name:"RareFilm",url:"https://rarefilm.net/",description:"珍稀和老电影平台。",isPaid:!1,isStudentFriendly:!1},{name:"I Have No TV",url:"https://ihavenotv.com/",description:"观看免费在线纪录片。",isPaid:!1,isStudentFriendly:!1},{name:"WCostream",url:"https://m.wcostream.com/",description:"免费卡通和动漫系列流播。",isPaid:!1,isStudentFriendly:!1},{name:"Watch Documentaries",url:"https://watchdocumentaries.com/",description:"提供各种主题纪录片集合的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Senses of Cinema",url:"https://www.sensesofcinema.com/",description:"最早的在线电影期刊之一，以其与电影研究和行业趋势相关的专业高质量内容而闻名。",isPaid:!1,isStudentFriendly:!1}]},{name:"媒体",originalName:"Media",id:"media",subcategories:[{name:"X/Twitter工具",originalName:"X / Twitter",id:"x---twitter",websites:[{name:"TweetDeck",url:"https://tweetdeck.twitter.com/",description:"Twitter仪表板应用程序，用于Twitter账户的管理、跟踪和组织。",isPaid:!1,isStudentFriendly:!1},{name:"Twitter Video Downloader",url:"https://twittervideodownloader.com",description:"在线工具，直接将任何Twitter视频下载到您的手机或电脑。",isPaid:!1,isStudentFriendly:!1},{name:"ThreadReaderApp",url:"https://threadreaderapp.com/",description:"帮助您将Twitter线程保存为PDF的平台，让阅读和分享变得更加容易。",isPaid:!1,isStudentFriendly:!1},{name:"Tweepsmap",url:"https://tweepsmap.com/",description:"AI驱动的Twitter分析和管理工具。",isPaid:!1,isStudentFriendly:!1},{name:"Shadowban Checker",url:"https://shadowban.yuzurisa.com/",description:"检查用户名是否在Twitter上被影子封禁的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Twitter Name Generator",url:"https://twitternamegenerator.com/",description:"免费在线工具，为Twitter生成漂亮的昵称。",isPaid:!1,isStudentFriendly:!1},{name:"Thread Hunt",url:"https://threadhunt.xyz/",description:"发现优质Twitter线程的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Small World",url:"https://smallworld.kiwi/signin",description:"利用您Twitter个人资料中的位置信息查找附近朋友。",isPaid:!1,isStudentFriendly:!1},{name:"Murmel",url:"https://murmel.social/top",description:"策划来自Twitter宇宙的最新发人深省的故事。",isPaid:!1,isStudentFriendly:!1},{name:"Chirpty",url:"https://chirpty.com/",description:"创建您自己的Twitter互动圈。",isPaid:!1,isStudentFriendly:!1},{name:"Capture My Tweet",url:"https://capturemytweet.in/",description:"免费将您的推文转换为精美图片。",isPaid:!1,isStudentFriendly:!1},{name:"Twitter Card Generator",url:"https://freecodetools.org/twitter-card-generator/",description:"生成Twitter卡片的工具，免费为推文附加丰富内容。",isPaid:!1,isStudentFriendly:!1},{name:"Rattibha",url:"https://rattibha.com/",description:"按类别和作者策划Twitter线程，提供时间、语言和排序选项。",isPaid:!1,isStudentFriendly:!1},{name:"Musk Messages",url:"https://muskmessages.com/",description:"编译和分类Elon Musk在Twitter上直接消息的平台，方便访问他的想法和声明。",isPaid:!1,isStudentFriendly:!1}]},{name:"Reddit工具",originalName:"Reddit",id:"reddit",websites:[{name:"RedditList",url:"https://redditlist.com/",description:"按各种主题分类的子版块综合列表。",isPaid:!1,isStudentFriendly:!1},{name:"Redsim",url:"https://anvaka.github.io/redsim/",description:"根据您的兴趣查找相似的子版块。",isPaid:!1,isStudentFriendly:!1},{name:"Reveddit",url:"https://www.reveddit.com/about/",description:"揭示Reddit被删除的内容。您可以按用户名、子版块(r/)、链接或域名搜索。",isPaid:!1,isStudentFriendly:!1},{name:"Spacebar Counter - Reddit List",url:"https://www.spacebarcounter.net/reddit-list",description:"100多个子版块的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Unreadit",url:"https://unreadit.com/",description:"Reddit驱动的每周新闻通讯，从各种子版块策划内容。",isPaid:!1,isStudentFriendly:!1},{name:"What Is This Thing",url:"https://whatisthisthing.vercel.app/",description:"汇总来自r/whatisthisthing子版块的帖子和答案。",isPaid:!1,isStudentFriendly:!1},{name:"Gummy Search",url:"https://gummysearch.com/",description:"在Reddit上探索痛点、内容想法，发现人们急于付费的内容。",isPaid:!1,isStudentFriendly:!1},{name:"RedditSearch.io",url:"https://redditsearch.io/",description:"高级Reddit搜索引擎，允许您过滤和自定义搜索。",isPaid:!1,isStudentFriendly:!1},{name:"Better Reddit Search",url:"https://betterredditsearch.web.app/",description:"通过改进的功能和特性增强您的Reddit搜索体验。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Radio Garden",url:"https://radio.garden/",description:"探索以绿点形式在Google Earth地图上显示的实时广播电台，只需一键即可收听任何一个电台。",isPaid:!1,isStudentFriendly:!1},{name:"Radiooooo",url:"https://radiooooo.com/",description:'"音乐时光机"，您可以探索不同时代和地点的音乐。',isPaid:!1,isStudentFriendly:!1},{name:"Lightyear.fm",url:"https://www.lightyear.fm/",description:"展示广播信号以光速从地球传播的距离。",isPaid:!1,isStudentFriendly:!1},{name:"TasteDive",url:"https://tastedive.com/",description:"基于您的偏好发现音乐、书籍、电影等的平台。",isPaid:!1,isStudentFriendly:!1},{name:"PIDGI Wiki",url:"https://www.pidgi.net/wiki/Main_Page",description:"社区驱动的视频游戏媒体数据库，包括艺术作品、宣传材料、标志等。",isPaid:!1,isStudentFriendly:!1},{name:"Kassellabs",url:"https://kassellabs.io/",description:"在著名电影和电视剧片头中创建您自己的文本。",isPaid:!1,isStudentFriendly:!1},{name:"USTVGO",url:"https://ustvgo.tv/",description:"免费在线直播电视频道。",isPaid:!1,isStudentFriendly:!1},{name:"All You Can Read",url:"https://www.allyoucanread.com/",description:"互联网上最大的杂志和报纸数据库，收录了来自世界各地约25,000份出版物。",isPaid:!1,isStudentFriendly:!1},{name:"LexiCap",url:"https://karpathy.ai/lexicap/",description:"Lex Fridman播客节目的文字记录。",isPaid:!1,isStudentFriendly:!1},{name:"Brett Hall's TokCast Transcripts",url:"https://www.aniketvartak.com/html/hall-index.html",description:"Brett Hall's TokCast播客的文字记录。",isPaid:!1,isStudentFriendly:!1},{name:"Thumbly",url:"https://thumbly.ai/",description:"将您的脚本转换为引人注目的缩略图，可以增加您的YouTube观看量。",isPaid:!1,isStudentFriendly:!1}]},{name:"经济",originalName:"Economy",id:"economy",subcategories:[],websites:[{name:"Investopedia",url:"https://www.investopedia.com/",description:"为投资者提供金融教育、新闻和研究的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Money",url:"https://money.com/",description:"个人理财和金融新闻的综合资源。",isPaid:!1,isStudentFriendly:!1},{name:"The Balance Money",url:"https://www.thebalancemoney.com/",description:"金融教育和资源。",isPaid:!1,isStudentFriendly:!1},{name:"CNN Fear and Greed Index",url:"https://edition.cnn.com/markets/fear-and-greed",description:"了解当前推动市场的情绪。",isPaid:!1,isStudentFriendly:!1},{name:"Where's Willy",url:"https://www.whereswilly.com/",description:"致力于在全球范围内追踪加拿大纸币的国际非营利志愿项目。",isPaid:!1,isStudentFriendly:!1},{name:"Where's George",url:"https://www.wheresgeorge.com/",description:`类似于"Where's Willy"的项目，用于在全球追踪美国纸币。`,isPaid:!1,isStudentFriendly:!1},{name:"EuroBillTracker",url:"https://en.eurobilltracker.com/",description:"致力于在全球范围内追踪欧元纸币的国际非营利志愿项目。",isPaid:!1,isStudentFriendly:!1},{name:"TradingView",url:"https://www.tradingview.com/",description:"全球超过3000万交易员和投资者使用的平台和社交网络，用于发现全球市场机会。",isPaid:!1,isStudentFriendly:!1},{name:"LendingTree",url:"https://www.lendingtree.com/",description:"通过寻找贷款而非制造贷款来为您省钱的市场平台。",isPaid:!1,isStudentFriendly:!1},{name:"Masterworks",url:"https://www.masterworks.com/",description:"投资蓝筹艺术品的专属社区。",isPaid:!1,isStudentFriendly:!1},{name:"EquityBee",url:"https://equitybee.com/",description:"通过连接全球投资者网络，为初创公司员工提供行使股票期权所需资金。",isPaid:!1,isStudentFriendly:!1},{name:"EquityZen",url:"https://equityzen.com/",description:"允许您通过EquityZen基金在二级市场投资或出售股份。",isPaid:!1,isStudentFriendly:!1},{name:"WTF Happened in 1971",url:"https://wtfhappenedin1971.com/",description:"探索和突出1971年发生的各种经济、社会和金融事件的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Ergodicity Economics",url:"https://ergodicityeconomics.com/",description:"提供遍历性经济学和相关概念见解的网站。",isPaid:!1,isStudentFriendly:!1}]},{name:"商业",originalName:"Business",id:"business",subcategories:[{name:"金融",originalName:"Finance",id:"finance",websites:[{name:"FIGR",url:"https://www.figr.app/",description:"Google Docs + 计算器 = 个人理财",isPaid:!1,isStudentFriendly:!1},{name:"LiveFortunately",url:"https://app.livefortunately.com/",description:"为更好的财务未来制定计划。创建完整的财务计划，充分利用您的资金。",isPaid:!1,isStudentFriendly:!1},{name:"KeeperTax",url:"https://www.keepertax.com/ask-an-ai-accountant-2-0",description:"AI会计师，帮助您获得帮助和计算税务相关问题。",isPaid:!1,isStudentFriendly:!1}]},{name:"专利",originalName:"Patents",id:"patents",websites:[{name:"Espacenet",url:"https://worldwide.espacenet.com/",description:"免费访问超过1.3亿份专利文件。",isPaid:!1,isStudentFriendly:!1},{name:"Google Patents",url:"https://patents.google.com/",description:"搜索并阅读来自世界各地的专利全文。",isPaid:!1,isStudentFriendly:!1},{name:"WIPO Patentscope",url:"https://patentscope.wipo.int/search/en/search.jsf",description:"搜索1.05亿份专利文件，包括430万份已发布的国际专利申请(PCT)。",isPaid:!1,isStudentFriendly:!1},{name:"Patently Apple",url:"https://www.patentlyapple.com/patents-applications/",description:"探索苹果公司的专利申请。",isPaid:!1,isStudentFriendly:!1},{name:"USPTO Report",url:"https://uspto.report/",description:"提供美国专利商标局专利相关信息和报告的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"营销",originalName:"Marketing",id:"marketing",websites:[{name:"Telega",url:"https://telega.io/",description:"在Telegram中寻找目标受众并启动有效的广告活动。",isPaid:!1,isStudentFriendly:!1},{name:"IGMassDMS",url:"https://igmassdms.com/",description:"在不使用您账户的情况下向目标受众发送Instagram直接消息。",isPaid:!0,isStudentFriendly:!1},{name:"Product Management Tools",url:"https://rohitverma.gumroad.com/l/PM-tools",description:"产品管理工具集合，售价5美元。",isPaid:!0,isStudentFriendly:!1},{name:"Yes Promo",url:"https://yespromo.me/",description:"100多个成功的Reddit自我推广帖子的免费数据库。",isPaid:!1,isStudentFriendly:!1},{name:"Marketing for Startups E-Book",url:"https://www.welovenocode.com/marketingforstartups",description:"免费电子书，包含47种获得客户和实现指数级增长的实用策略。",isPaid:!1,isStudentFriendly:!1},{name:"ArrayList",url:"https://arraylist.org/",description:"云端列表数据库，用于存储表单提交、电子邮件订阅表单或进行AJAX/REST调用来存储数值。",isPaid:!1,isStudentFriendly:!1},{name:"SuperMeme AI",url:"https://www.supermeme.ai/",description:"使用AI生成110多种语言的原创表情包。",isPaid:!0,isStudentFriendly:!1},{name:"Apollo",url:"https://www.apollo.io/",description:"销售智能和参与平台，搜索、接触并转化超过6000万家公司的2.5亿多个联系人。",isPaid:!1,isStudentFriendly:!1},{name:"Vocus.io",url:"https://vocus.io/",description:"个性化电子邮件活动，跟踪和自动化电子邮件跟进，安排约会，与您的CRM同步，并与您的团队协作。",isPaid:!1,isStudentFriendly:!1}]},{name:"社交媒体",originalName:"Social Media",id:"social-media",websites:[{name:"Instagram Caption Maker",url:"https://apps4lifehost.com/Instagram/CaptionMaker.html",description:"IG标题的简洁美观换行符。",isPaid:!1,isStudentFriendly:!1},{name:"Social Sizes",url:"https://socialsizes.io/",description:"社交媒体的图片和视频尺寸。",isPaid:!1,isStudentFriendly:!1},{name:"Ocoya",url:"https://www.ocoya.net/",description:"创建、自动生成和安排内容的平台。",isPaid:!0,isStudentFriendly:!1},{name:"Imgupscaler",url:"https://imgupscaler.com/?ref=producthunt",description:"基于AI的PNG/JPG图像放大器。",isPaid:!1,isStudentFriendly:!1},{name:"Namechk",url:"https://namechk.com/",description:"在几秒钟内检查用户名或域名的可用性。30个域名和90多个社交媒体账户。",isPaid:!1,isStudentFriendly:!1},{name:"Inflact",url:"https://inflact.com/downloader/instagram/video/",description:"将Instagram上的原始高质量视频保存到您的设备。",isPaid:!1,isStudentFriendly:!1},{name:"TikTok Video Downloader",url:"https://ttvdl.com/",description:"下载TikTok视频。",isPaid:!1,isStudentFriendly:!1},{name:"SWAPD",url:"https://swapd.co/",description:"提供虚拟物品和服务买卖交易的中介服务，提供安全平台，连接用户与庞大的买家、卖家网络和数字市场中的机会。",isPaid:!1,isStudentFriendly:!1}]},{name:"趋势分析",originalName:"Trends",id:"trends",websites:[{name:"Google Trends",url:"https://trends.google.com/trends/?geo=US",description:"基于Google搜索探索热门话题和见解。",isPaid:!1,isStudentFriendly:!1},{name:"Google Trends - Visualize",url:"https://trends.google.com/trends/hottrends/visualize",description:"可视化和探索Google上的最热门趋势。",isPaid:!1,isStudentFriendly:!1},{name:"Statista",url:"https://www.statista.com/",description:"统计数据和可视化平台，提供各行业和主题的见解。",isPaid:!1,isStudentFriendly:!1},{name:"Google Books Ngram Viewer",url:"https://books.google.com/ngrams",description:"图形化显示所选年份书籍语料库中短语出现频率。",isPaid:!1,isStudentFriendly:!1}]},{name:"会议工具",originalName:"Meetings",id:"meetings",websites:[{name:"When2meet",url:"https://www.when2meet.com/",description:"安排团体会议的免费服务。允许用户创建和参与可用性调查，找到团体聚会的最佳时间。",isPaid:!1,isStudentFriendly:!1},{name:"Sessions",url:"https://sessions.us/",description:"旨在增强会议体验的基于Web的会议工具。旨在改善整体会议体验，免费提供。",isPaid:!1,isStudentFriendly:!1},{name:"Form to Chatbot",url:"https://formtochatbot.com/",description:"将Google表单转换为聊天机器人形式的互动对话。此工具有助于基于表单回复创建引人入胜的动态交互。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Crunchbase",url:"https://www.crunchbase.com/",description:"发现创新公司、初创企业和商业世界关键人物的平台。提供关于公司、投资和行业趋势的综合数据和见解。",isPaid:!1,isStudentFriendly:!1},{name:"Business Model Toolbox",url:"https://bmtoolbox.net/",description:"学习各种商业概念和模式的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Gumroad",url:"https://gumroad.com/",description:"创作者直接向客户销售数字产品的电子商务平台。",isPaid:!1,isStudentFriendly:!1},{name:"Humble Bundle",url:"https://www.humblebundle.com/",description:"销售游戏、电子书、软件和数字内容的平台，致力于支持慈善事业，同时以实惠价格提供优质内容。",isPaid:!1,isStudentFriendly:!1},{name:"Google Takeout",url:"https://takeout.google.com/",description:"导出您所有Google数据的副本。",isPaid:!1,isStudentFriendly:!1},{name:"Honey",url:"https://www.joinhoney.com/",description:"在全球超过30,000个网站上自动搜索优惠券的浏览器扩展。",isPaid:!1,isStudentFriendly:!1},{name:"BotHelp",url:"https://bothelp.io/widget",description:"为网站提供免费聊天按钮小部件。",isPaid:!1,isStudentFriendly:!1},{name:"CertificateClaim",url:"https://www.certificateclaim.com/",description:"创建和发送各种类型证书的数字服务。",isPaid:!1,isStudentFriendly:!1},{name:"Respresso",url:"https://respresso.io/",description:"管理应用本地化文本、图像、颜色、字体等的工具，只需一键即可自动传送到您的项目中。",isPaid:!0,isStudentFriendly:!1},{name:"Bonanza",url:"https://www.bonanza.com/",description:"让企业家基于回头客建立可持续业务的在线市场。",isPaid:!1,isStudentFriendly:!1},{name:"Pipl",url:"https://pipl.com/",description:"通过搜索Pipl的全球身份信息索引，使用电子邮件地址、社交用户名或电话号码识别、搜索和验证员工，减少客户摩擦，打击欺诈，节省审查和研究时间。",isPaid:!1,isStudentFriendly:!1}]},{name:"工作求职",originalName:"Jobs",id:"jobs",subcategories:[{name:"远程工作",originalName:"Remote Jobs",id:"remote-jobs",websites:[{name:"Remote OK",url:"https://remoteok.com/",description:"远程职位招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Remotive",url:"https://remotive.com/",description:"连接远程公司与优秀专业人士的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Remote.co",url:"https://remote.co/",description:"寻找远程工作机会的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Remote Leaf",url:"https://remoteleaf.com/",description:"专注于科技行业远程机会的招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Remote Leads",url:"https://remoteleads.io/",description:"在软件开发领域连接公司与远程人才的平台。",isPaid:!1,isStudentFriendly:!1},{name:"RemoteBear",url:"https://remotebear.io/",description:"科技和设计远程职位招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"RemoteBase",url:"https://remotebase.com/",description:"连接远程工作者与提供远程职位公司的平台。",isPaid:!1,isStudentFriendly:!1},{name:"JustRemote",url:"https://justremote.co/",description:"远程工作机会招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"JS Remotely",url:"https://jsremotely.com/",description:"专门针对远程JavaScript职位的招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Jobspresso",url:"https://jobspresso.co/",description:"科技、营销等领域远程工作的策划招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Just Join",url:"https://justjoin.it/",description:"IT远程职位招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"FlexJobs",url:"https://flexjobs.com/",description:"灵活和远程工作机会招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"We Work Remotely",url:"https://weworkremotely.com/",description:"各行业远程职位招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Daily Remote",url:"https://dailyremote.com/",description:"每日更新的远程工作机会招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"AngelList Candidates",url:"https://angel.co/candidates/overview",description:"连接初创公司与潜在候选人的平台，包括远程职位。",isPaid:!1,isStudentFriendly:!1},{name:"Hired",url:"https://hired.com/",description:"连接技术人才与创新公司的平台。",isPaid:!1,isStudentFriendly:!1},{name:"PowerToFly",url:"https://powertofly.com/",description:"专注于科技行业女性远程机会的招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"SkipTheDrive",url:"https://skipthedrive.com/",description:"各行业远程职位招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Authentic Jobs",url:"https://authenticjobs.com/",description:"创意和技术专业人士的招聘板，包括远程职位。",isPaid:!1,isStudentFriendly:!1},{name:"Working Nomads",url:"https://workingnomads.co/",description:"各行业远程职位招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Europe Remotely",url:"https://europeremotely.com/",description:"专门针对欧洲远程职位的招聘板。",isPaid:!1,isStudentFriendly:!1},{name:"Virtual Vocations",url:"https://virtualvocations.com/",description:"远程办公和远程职位招聘板。",isPaid:!1,isStudentFriendly:!1}]},{name:"自由职业",originalName:"Freelancing",id:"freelancing",websites:[{name:"Remote Starter Kit",url:"https://www.remotestarterkit.com/",description:"远程团队工具和流程的终极清单。",isPaid:!1,isStudentFriendly:!1},{name:"Fiverr",url:"https://www.fiverr.com/",description:"为您的业务寻找完美的自由职业服务。",isPaid:!1,isStudentFriendly:!1},{name:"Upwork",url:"https://www.upwork.com/",description:"雇佣自由职业者并在线获得自由职业工作。",isPaid:!1,isStudentFriendly:!1}]},{name:"简历作品集",originalName:"Portfolio / CV / Resume",id:"portfolio---cv---resume",websites:[{name:"University of Nebraska Omaha - Job & Internship Resources",url:"https://www.unomaha.edu/student-life/achievement/academic-and-career-development-center/career-development/jobs-and-internships/job-internship-resources.php",description:"内布拉斯加大学奥马哈分校学术和职业发展中心提供的简历和求职信资源。",isPaid:!1,isStudentFriendly:!1},{name:"VisualCV",url:"https://www.visualcv.com/resume-samples/",description:"500多个专业简历样本集合。",isPaid:!1,isStudentFriendly:!1},{name:"SuperPortfolio",url:"https://superportfolio.co/",description:"在线作品集制作器。",isPaid:!1,isStudentFriendly:!1},{name:"Referd.ai",url:"https://www.referd.ai/resume-scanner",description:"免费简历扫描器。",isPaid:!1,isStudentFriendly:!1},{name:"RxResu.me",url:"https://rxresu.me/",description:"免费开源简历构建器。",isPaid:!1,isStudentFriendly:!1},{name:"GoodCV",url:"https://www.goodcv.com/",description:"无需Photoshop或AI技术，几分钟内创建专业简历/CV。",isPaid:!1,isStudentFriendly:!1},{name:"JSON Resume",url:"https://jsonresume.io/",description:"根据规范上传您的JSON简历并进行精美渲染。",isPaid:!1,isStudentFriendly:!1},{name:"CVmkr",url:"https://cvmkr.com/",description:"免费创建、维护、发布和分享您的简历。",isPaid:!1,isStudentFriendly:!1},{name:"Novoresume",url:"https://novoresume.com/",description:"在线简历构建器。",isPaid:!1,isStudentFriendly:!1},{name:"HelloTechRecruiters",url:"https://hellotechrecruiters.com/",description:"为技术招聘人员量身定制。",isPaid:!1,isStudentFriendly:!1},{name:"FlowCV",url:"https://flowcv.com/",description:"AI增强的简历构建器、求职信、工作跟踪器、电子邮件签名、个人网站。",isPaid:!1,isStudentFriendly:!1},{name:"Signature Maker",url:"https://signature-maker.net/",description:"创建手写数字签名。",isPaid:!1,isStudentFriendly:!1}]},{name:"职业发展",originalName:"Careers",id:"careers",websites:[{name:"Roadmap.sh",url:"https://roadmap.sh/",description:"提供各种开发领域的路线图。",isPaid:!1,isStudentFriendly:!1},{name:"WTF Should I Do With My Life",url:"https://www.wtfshouldidowithmylife.com/",description:"探索和了解不同职业的资源。",isPaid:!1,isStudentFriendly:!1},{name:"path-to-polymath.notion",url:"https://path-to-polymathy.notion.site/path-to-polymathy/Path-To-Polymathy-d7586429b7ce4db1889fc539822b9670",description:"整理了所有学科和领域的安排，以及构成它们的具体主题。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Y Combinator Jobs",url:"https://www.ycombinator.com/jobs",description:"发现Y Combinator策划的最佳初创公司工作机会。",isPaid:!1,isStudentFriendly:!1},{name:"Coroflot",url:"https://www.coroflot.com/discover",description:"专门为设计师量身定制的求职平台。",isPaid:!1,isStudentFriendly:!1},{name:"Cool Startup Jobs",url:"https://www.coolstartupjobs.com/",description:"探索成长型初创公司的工作机会，给您的股票期权一个机会。",isPaid:!1,isStudentFriendly:!1},{name:"Anon Friendly",url:"https://anonfriendly.com/",description:"寻找尊重您匿名愿望的工作。",isPaid:!1,isStudentFriendly:!1},{name:"Prompt Engineering Jobs",url:"https://prompt-engineering-jobs.com",description:"探索Prompt的工程工作机会。",isPaid:!1,isStudentFriendly:!1},{name:"KeyValues",url:"https://www.keyvalues.com/",description:"寻找与您价值观一致的工程团队。",isPaid:!1,isStudentFriendly:!1},{name:"About.me",url:"https://about.me/",description:"自由职业者和企业家扩大受众和吸引客户的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Rejected.us",url:"https://rejected.us/",description:"阅读和分享工作被拒的故事。",isPaid:!1,isStudentFriendly:!1},{name:"Tech Interview Handbook",url:"https://www.techinterviewhandbook.org/",description:"免费策划的面试准备材料。",isPaid:!1,isStudentFriendly:!1}]},{name:"创业",originalName:"Startups",id:"startups",subcategories:[{name:"失败案例",originalName:"Failures",id:"failures",websites:[{name:"Failory - Google Failures",url:"https://www.failory.com/google/",description:"从Google的100多个失败案例中学习，以构建盈利业务和扩展被收购的公司。",isPaid:!1,isStudentFriendly:!1},{name:"Killed by Google",url:"https://killedbygoogle.com/",description:"Google推出并终止的项目。",isPaid:!1,isStudentFriendly:!1}]},{name:"创意发现",originalName:"Finding Ideas",id:"finding-ideas",websites:[{name:"Random Startup Website Generator",url:"https://tiffzhang.com/startup/",description:"随机初创公司网站生成器。",isPaid:!1,isStudentFriendly:!1},{name:"AnswerSocrates",url:"https://answersocrates.com/",description:"免费发现人们在Google上询问的几乎任何主题的问题。",isPaid:!1,isStudentFriendly:!1},{name:"IdeasAI",url:"https://ideasai.com/",description:"由OpenAI的GPT-3生成的想法。",isPaid:!1,isStudentFriendly:!1},{name:"AnswerThePublic",url:"https://answerthepublic.com/",description:"发现人们在热门搜索引擎中询问的问题。",isPaid:!1,isStudentFriendly:!1},{name:"List of Emerging Technologies",url:"https://en.wikipedia.org/wiki/List_of_emerging_technologies",description:"维基百科新兴技术列表。",isPaid:!1,isStudentFriendly:!1},{name:"UniCorner",url:"https://unicorner.news/",description:"每周一早晨为您的收件箱提供新兴初创公司2分钟概览的通讯。",isPaid:!1,isStudentFriendly:!1},{name:"DemandHunt",url:"https://demandhunt.com/",description:"发现和投票支持新初创公司的平台。",isPaid:!0,isStudentFriendly:!1}]},{name:"连接工具",originalName:"Connectivity",id:"connectivity",websites:[{name:"Integromat",url:"https://www.integromat.com/en?pc=referralbonus",description:"几次点击即可连接应用程序并自动化工作流程。",isPaid:!1,isStudentFriendly:!1},{name:"IFTTT",url:"https://ifttt.com/",description:"快速轻松地自动化您最喜欢的应用程序和设备，使它们以新的强大方式协同工作。",isPaid:!1,isStudentFriendly:!1},{name:"Franz",url:"https://meetfranz.com/",description:"在一个平台中管理您的所有消息应用程序，如WhatsApp、Facebook Messenger、Slack、Telegram等。",isPaid:!1,isStudentFriendly:!1},{name:"Google Remote Desktop",url:"https://remotedesktop.google.com/",description:"远程连接您的家庭或工作电脑，或与他人共享您的屏幕。",isPaid:!1,isStudentFriendly:!1},{name:"RecWide",url:"https://www.recwide.com/",description:"屏幕和网络摄像头录制器。免费，在线（无需下载）。",isPaid:!1,isStudentFriendly:!1},{name:"1,000 True Fans",url:"https://kk.org/thetechnium/1000-true-fans/",description:"培养1,000个忠实粉丝以实现可持续创意成功的概念。",isPaid:!1,isStudentFriendly:!1},{name:"Alias",url:"https://alias.co/",description:"与最好的保持同步。",isPaid:!1,isStudentFriendly:!1},{name:"LittleSis",url:"https://littlesis.org/",description:"商业和政府高层人物关系的免费数据库。",isPaid:!1,isStudentFriendly:!1}]},{name:"设计",originalName:"Design",id:"design",websites:[{name:"Social Image Maker",url:"https://socialimagemaker.io/",description:"轻松创建社交媒体图片的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Open Source Design Resources",url:"https://opensourcedesign.net/resources/",description:"展示提供开放许可图标、字体、图片、工具和其他设计资源的网站和平台。",isPaid:!1,isStudentFriendly:!1},{name:"Transparent Textures",url:"https://transparenttextures.com/",description:"为设计项目提供透明纹理的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Collection of $0 Design Tools",url:"https://www.producthunt.com/e/0-design-tools",description:"帮助项目创建的免费设计工具集合。",isPaid:!1,isStudentFriendly:!1},{name:"Easel.ly",url:"https://www.easel.ly/",description:"可视化各种类型信息的设计工具。",isPaid:!1,isStudentFriendly:!1},{name:"Material Design",url:"https://m3.material.io/",description:"Google为构建Android、iOS、Flutter和网页高质量数字体验而设计的设计系统。",isPaid:!1,isStudentFriendly:!1},{name:"Rasterizer.io",url:"https://rasterizer.io/",description:"创建动态图像的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Jitter.Video",url:"https://jitter.video/",description:"网页上的简单动画工具。",isPaid:!0,isStudentFriendly:!1},{name:"Polotno Studio",url:"https://studio.polotno.com/",description:"无需注册和广告即可创建图形设计的网页应用程序，Canva的免费替代品。",isPaid:!1,isStudentFriendly:!1},{name:"BitBof",url:"https://bitbof.com/",description:"免费绘画和素描应用程序。",isPaid:!1,isStudentFriendly:!1},{name:"Sumo",url:"https://sumo.app/",description:"绘图工具和图像编辑器。",isPaid:!1,isStudentFriendly:!1},{name:"Random Design Stuff",url:"https://randomdesignstuff.com/",description:"浏览为设计师精心挑选的网站，既有用的也有无用的。",isPaid:!1,isStudentFriendly:!1},{name:"Discover NFT Club",url:"https://www.discovernft.club/",description:"发现最新的NFT项目。",isPaid:!1,isStudentFriendly:!1},{name:"Haikei",url:"https://app.haikei.app/",description:"基于网络的设计工具，为各种目的生成独特的SVG设计资产。",isPaid:!1,isStudentFriendly:!1},{name:"Spline",url:"https://spline.design/",description:"创建3D场景、编辑材质和建模3D对象，控制设计工作的结果。",isPaid:!1,isStudentFriendly:!1},{name:"Visiwig",url:"https://www.visiwig.com/",description:"仅通过点击和粘贴创建图形。",isPaid:!1,isStudentFriendly:!1},{name:"FreeType",url:"https://freetype.org/",description:"用C语言编写的字体渲染软件库，能够为大多数矢量和位图字体格式产生高质量输出，设计为小巧、高效、高度可定制和便携。",isPaid:!1,isStudentFriendly:!1}]},{name:"颜色工具",originalName:"Colors",id:"colors",websites:[{name:"HexColor",url:"https://hexcolor.co/",description:"提供各种免费颜色工具。",isPaid:!1,isStudentFriendly:!1},{name:"Adobe Color Wheel",url:"https://color.adobe.com/create/color-wheel",description:"可用于生成调色板的色轮。",isPaid:!1,isStudentFriendly:!1},{name:"SchemeColor",url:"https://www.schemecolor.com/",description:"允许您下载配色方案。",isPaid:!1,isStudentFriendly:!1},{name:"Mobile Palette Generator",url:"https://mobilepalette.colorion.co/",description:"生成移动端调色板的工具。",isPaid:!1,isStudentFriendly:!1},{name:"DeGraeve Color Palette Generator",url:"https://www.degraeve.com/color-palette/",description:"生成调色板。",isPaid:!1,isStudentFriendly:!1},{name:"0to255",url:"https://0to255.com/",description:"基于任何颜色帮助查找更亮和更暗颜色的颜色工具。",isPaid:!1,isStudentFriendly:!1},{name:"ColorHexa",url:"https://www.colorhexa.com/",description:"提供任何颜色的信息并生成匹配的调色板。",isPaid:!1,isStudentFriendly:!1},{name:"Color Hunt",url:"https://colorhunt.co/",description:"发现精心挑选的调色板。",isPaid:!1,isStudentFriendly:!1},{name:"Coolors",url:"https://coolors.co/",description:"配色方案生成器和调色板创建工具，允许用户为设计项目探索、创建和分享颜色组合。",isPaid:!1,isStudentFriendly:!1},{name:"Colors.lol",url:"https://colors.lol/",description:"提供简单界面生成调色板的网站，具有保存和导出选项，用于数字设计。",isPaid:!1,isStudentFriendly:!1}]},{name:"字体",originalName:"Fonts",id:"fonts",websites:[{name:"Font Squirrel",url:"https://www.fontsquirrel.com/",description:"免费字体乌托邦。",isPaid:!1,isStudentFriendly:!1},{name:"Font Discovery",url:"https://fontdiscovery.typogram.co/",description:"为创作者、创始人、制作者提供的每周设计、字体和颜色创意新闻通讯。",isPaid:!1,isStudentFriendly:!1},{name:"MyFonts",url:"https://www.myfonts.com/",description:"超过130,000种可用字体，且数量还在增长。",isPaid:!1,isStudentFriendly:!1},{name:"Google Fonts",url:"https://fonts.google.com/",description:"Google的免费开源字体集合。",isPaid:!1,isStudentFriendly:!1},{name:"DaFont",url:"https://www.dafont.com/",description:"提供免费字体下载的热门网站，具有各种风格和用途的分类，包括装饰性、手写和无衬线字体。",isPaid:!1,isStudentFriendly:!1},{name:"UCL Fonts Project",url:"http://vecg.cs.ucl.ac.uk/Projects/projects_fonts/projects_fonts.html",description:"专注于字体流形的研究项目，提供其工作成果和交互式2D字体流形演示。",isPaid:!1,isStudentFriendly:!1}]},{name:"图标",originalName:"Icons / Icon Packs",id:"icons---icon-packs",websites:[{name:"The Noun Project",url:"https://thenounproject.com/",description:"提供大量免费图标和库存照片的平台，可用于各种项目和设计。",isPaid:!1,isStudentFriendly:!1},{name:"IconPacks",url:"https://www.iconpacks.net/",description:"提供各种图标包供个人和商业使用的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Doodlicons on Notion",url:"https://www.notion.so/Doodlicons-519314a92ed3474093a10e44946bbb72",description:"Notion上项目线框图的涂鸦图标。",isPaid:!1,isStudentFriendly:!1},{name:"Illustration Kit",url:"https://illustrationkit.com/",description:"个人和商业项目免费矢量插图集合。",isPaid:!1,isStudentFriendly:!1},{name:"FontAwesome",url:"https://fontawesome.com/",description:"互联网图标库和工具包，被设计师、开发者和内容创作者广泛使用。",isPaid:!1,isStudentFriendly:!1},{name:"Iconshock",url:"https://www.iconshock.com/freeicons/",description:"提供来自各种开源集合图标的平台，有10万个图标可免费下载。",isPaid:!1,isStudentFriendly:!1},{name:"3DIcons",url:"https://3dicons.co/",description:"在CC0许可下可免费商业和个人使用的3D图标集合。",isPaid:!1,isStudentFriendly:!1},{name:"Fontello",url:"https://fontello.com/",description:"创建自定义图标字体的图标字体生成器。",isPaid:!1,isStudentFriendly:!1},{name:"HealthIcons",url:"https://healthicons.org/",description:"各种用例的免费开源健康图标。",isPaid:!1,isStudentFriendly:!1},{name:"TablerIcons",url:"https://tablericons.com/",description:"开源免费SVG图标，高度可定制，商业使用无需署名。",isPaid:!1,isStudentFriendly:!1},{name:"David Li",url:"https://david.li/",description:"基于粒子的3D模拟和渲染平台。",isPaid:!1,isStudentFriendly:!1},{name:"IcoMoon",url:"https://icomoon.io/",description:"创建和管理图标字体的平台。",isPaid:!1,isStudentFriendly:!1},{name:"UTF8Icons",url:"https://www.utf8icons.com/",description:"UTF-8标准中Unicode符号的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Free Isometric Illustrations",url:"https://passionhacks.com/free-isometric-illustrations/",description:"各种项目的免费等距插图集合。",isPaid:!1,isStudentFriendly:!1},{name:"Illlustrations",url:"https://illlustrations.co/",description:"创意项目的开源插图套件。",isPaid:!1,isStudentFriendly:!1},{name:"PixelBazaar",url:"https://www.pixelbazaar.com",description:"为有特色的品牌提供有态度的图标。",isPaid:!1,isStudentFriendly:!1},{name:"Iconfinder",url:"https://www.iconfinder.com/",description:"提供图标、插图、3D插图、设计师和免费图标的平台。",isPaid:!0,isStudentFriendly:!1},{name:"Iconz Design",url:"https://iconz.design/",description:"223个图标的高级3D库。",isPaid:!0,isStudentFriendly:!1},{name:"HugeIcons.pro",url:"https://hugeicons.pro/",description:"提供超过25,000个图标的平台，有5种独特风格，分布在57个热门类别中。",isPaid:!0,isStudentFriendly:!1}]},{name:"素材图片",originalName:"Stock Images",id:"stock-images",websites:[{name:"The Stocks",url:"https://thestocks.im/",description:"聚合器，提供来自各种来源的免费库存照片、视频和音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Pexels",url:"https://www.pexels.com/",description:"提供高质量库存照片和视频免费下载和使用的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Unsplash",url:"https://unsplash.com/",description:"为创意项目提供大量高分辨率免版税图片的网站。",isPaid:!1,isStudentFriendly:!1},{name:"FreeImages",url:"https://www.freeimages.com/",description:"提供多样化免费库存照片供个人或商业使用的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Pixabay",url:"https://pixabay.com/",description:"社区驱动的平台，贡献者分享高质量库存图片、视频和音乐。",isPaid:!1,isStudentFriendly:!1},{name:"PNG Guru",url:"https://www.pngguru.in/",description:"免费PNG图片、背景和模板的来源。",isPaid:!1,isStudentFriendly:!1},{name:"Pond5",url:"https://www.pond5.com/free",description:"提供免费库存视频、照片和音乐资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Critter Pics",url:"https://www.critter.pics/",description:"为创意项目提供小动物图片的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Stock Up",url:"https://stockup.sitebuilderreport.com/",description:"索引来自31个不同免费库存照片网站的35,356张照片，便于访问。",isPaid:!1,isStudentFriendly:!1},{name:"Shutterstock",url:"https://www.shutterstock.com/",description:"提供大量免版税图片、视频、矢量、插图和音乐库的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Zoom.nl",url:"https://zoom.nl/",description:"荷兰最大的摄影社区，为摄影爱好者提供资源和平台。",isPaid:!1,isStudentFriendly:!1},{name:"Depositphotos",url:"https://depositphotos.com/",description:"拥有2.32亿文件的平台，包括免版税图片、视频、矢量、插图和音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Skuawk",url:"https://skuawk.com/",description:"各种创意项目的公共领域照片来源。",isPaid:!1,isStudentFriendly:!1},{name:"All-Free-Download",url:"https://all-free-download.com/",description:"提供可在个人或商业项目中免费使用的图形艺术的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"壁纸",originalName:"Wallpapers",id:"wallpapers",websites:[{name:"Wallpapers.com",url:"https://wallpapers.com/",description:"提供不同主题和风格壁纸的网站。",isPaid:!1,isStudentFriendly:!1},{name:"WallpaperCave",url:"https://wallpapercave.com/",description:"提供各种类别高质量壁纸集合的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Wallhaven",url:"https://wallhaven.cc/",description:"拥有大量高分辨率壁纸集合的壁纸社区。",isPaid:!1,isStudentFriendly:!1},{name:"MovieMania",url:"https://www.moviemania.io/phone",description:"手机无文字高分辨率电影壁纸数据库。",isPaid:!1,isStudentFriendly:!1},{name:"WallpaperTip",url:"https://www.wallpapertip.com/",description:"上传和发现免费高清壁纸的平台。",isPaid:!1,isStudentFriendly:!1},{name:"SimpleDesktops",url:"https://simpledesktops.com/browse/",description:"桌面背景的极简壁纸集合。",isPaid:!1,isStudentFriendly:!1},{name:"WallpaperFlare",url:"https://www.wallpaperflare.com/search?wallpaper=vertical",description:"提供高分辨率垂直壁纸的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Positron Dream",url:"https://www.positrondream.com/wallpapers-all",description:"可下载的抽象壁纸集合。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Startup Growth Calculator",url:"https://growth.tlb.org/",description:"计算您的初创公司增长所需的资金。",isPaid:!1,isStudentFriendly:!1},{name:"Startup Equity Calculator",url:"https://capbase.com/startup-equity-calculator/",description:"基于不同变量为您的初创公司分配股权。",isPaid:!1,isStudentFriendly:!1},{name:"Fifty Years Progress Map",url:"https://progress.fiftyyears.com/",description:"突出显示低初创投资的大型市场。提供按竞争力排名的大规模市场列表，包括市场规模和过去10年A轮初创公司的总投资。为每个市场计算竞争比率。",isPaid:!1,isStudentFriendly:!1},{name:"Museum of Websites",url:"https://www.kapwing.com/museum-of-websites",description:"展示著名互联网公司如何随时间变化的画廊。",isPaid:!1,isStudentFriendly:!1},{name:"Goal Examples",url:"https://hypercontext.com/goal-examples",description:"为技术领域每个角色精心策划的目标示例列表。",isPaid:!1,isStudentFriendly:!1},{name:"Startup Resources",url:"https://www.feedough.com/startup-resources/",description:"为初创公司分类的资源集合。",isPaid:!1,isStudentFriendly:!1},{name:"500+ Free Tools For Startups",url:"https://docs.google.com/spreadsheets/d/1s6-hGBh0_tqa-jd23fsdYuwbmS8UPmElPqaH-Rnoa_A/htmlview",description:"初创公司免费工具的综合列表。",isPaid:!1,isStudentFriendly:!1},{name:"Founder Resources",url:"https://www.founderresources.io/",description:"为初创公司提供的免费策划资源、模板和工具。",isPaid:!1,isStudentFriendly:!1},{name:"100+ Resources on GPT-3",url:"https://harishgarg.gumroad.com/l/wiSvc?ref=producthunt",description:"100多个GPT-3资源的策划列表。",isPaid:!1,isStudentFriendly:!1},{name:"100+ Resources for Building a Successful Startup",url:"https://cerdeira.notion.site/b3b5f44d37cf4843b3fcd2f300354467?v=8f3458522f4542d8896ebb3720c14b2d",description:"构建成功初创公司的资源汇编。",isPaid:!1,isStudentFriendly:!1},{name:"2,500 Accelerators Incubators",url:"https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fattachments.convertkitcdnn2.com%2F587796%2F16ab0442-1232-4a49-956c-acafd6df4189%2F2%2C500%2520Accelerators%2520Incubators.xlsx&wdOrigin=BROWSELINK",description:"2,500个加速器和孵化器的列表。",isPaid:!1,isStudentFriendly:!1},{name:"The Complete Unicorn List",url:"https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fattachments.convertkitcdnn2.com%2F587796%2F476863f4-bda0-4132-8bd0-d25728513cfd%2FThe%2520Complete%2520Unicorn%2520List.xlsx&wdOrigin=BROWSELINK",description:"1,016个独角兽公司（估值超过10亿美元的私人公司）的综合列表。",isPaid:!1,isStudentFriendly:!1},{name:"GitHub Email Hunter",url:"https://chrome.google.com/webstore/detail/github-email-hunter/ppcegaekdbgcgbapfdcjbhednhmgcjnk",description:"一键查找GitHub用户和仓库的电子邮件地址。",isPaid:!1,isStudentFriendly:!1},{name:"The Angel Philosopher",url:"https://theangelphilosopher.com/",description:"Naval智慧、知识和思想的汇编。",isPaid:!1,isStudentFriendly:!1},{name:"Under Glass",url:"https://underglass.io/",description:"对世界最佳数字产品的分析。",isPaid:!1,isStudentFriendly:!1},{name:"The Perfect Pitch Deck",url:"https://attachments.convertkitcdnn2.com/587796/1e723803-ab50-4a61-9b3a-f347aa436408/The%20Perfect%20Pitch%20Deck.pdf",description:"从分析350多个初创公司路演稿中得出的经验教训。",isPaid:!1,isStudentFriendly:!1},{name:"Old Computers Museum",url:"https://oldcomputers.net/",description:"探索拥有150件展品的老式和复古计算机博物馆。",isPaid:!1,isStudentFriendly:!1},{name:"Startups List",url:"https://www.startups-list.com/",description:"不同地方最佳初创公司的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Pessimists Archive",url:"https://pessimistsarchive.org/",description:"唤醒我们对新技术、想法和趋势常常引起的歇斯底里、技术恐惧症和道德恐慌的集体记忆的项目。",isPaid:!1,isStudentFriendly:!1}]},{name:"艺术",originalName:"Art",id:"art",subcategories:[{name:"摄影",originalName:"Photography",id:"photography",websites:[{name:"Cambridge in Colour",url:"https://www.cambridgeincolour.com/",description:"摄影师学习社区",isPaid:!1,isStudentFriendly:!1},{name:"Exposure Guide",url:"https://www.exposureguide.com/",description:"摄影技巧、技术和教程",isPaid:!1,isStudentFriendly:!1}]},{name:"艺术社区",originalName:"Art Communities",id:"art-communities",websites:[{name:"Ello",url:"https://ello.co/discover",description:"",isPaid:!1,isStudentFriendly:!1},{name:"Behance",url:"https://www.behance.net/",description:"",isPaid:!1,isStudentFriendly:!1},{name:"ArtStation",url:"https://www.artstation.com/",description:"",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"WeavesSilk",url:"https://weavesilk.com/",description:"使用Silk创建美丽流动的艺术作品。",isPaid:!1,isStudentFriendly:!1},{name:"Google Arts & Culture",url:"https://artsandculture.google.com/",description:"将世界艺术和文化在线呈现给每个人的平台。",isPaid:!1,isStudentFriendly:!1},{name:"ArtGraphica",url:"https://www.artgraphica.net/",description:"免费绘画、素描和绘画技巧的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Tattoos Wizard",url:"https://tattooswizard.com/",description:"寻找您附近的纹身艺术家和工作室。",isPaid:!1,isStudentFriendly:!1},{name:"The Art Institute of Chicago Collection",url:"https://www.artic.edu/collection",description:"探索博物馆收藏的数千件艺术品。",isPaid:!1,isStudentFriendly:!1},{name:"ZoomQuilt",url:"https://zoomquilt.org/",description:"协作无限缩放绘画。",isPaid:!1,isStudentFriendly:!1},{name:"Invaluable",url:"https://www.invaluable.com/",description:"世界顶级在线拍卖平台，每日添加数千件拍品。Invaluable为随时随地发现和获得卓越艺术品和物品提供便利。",isPaid:!1,isStudentFriendly:!1},{name:"50 Watts",url:"https://50watts.com/",description:"来自世界各地奇异精彩视觉短篇作品的档案",isPaid:!1,isStudentFriendly:!1}]},{name:"学术",originalName:"Academia",id:"academia",subcategories:[{name:"学习",originalName:"Studying",id:"studying",websites:[{name:"Bartleby",url:"https://www.bartleby.com/",description:"搜索教科书、作业问题的逐步解释等内容的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Chegg",url:"https://www.chegg.com/",description:"提供24/7课程帮助的服务，包括教科书解答和专家问答。",isPaid:!0,isStudentFriendly:!1},{name:"Chegg Flashcards",url:"https://www.chegg.com/flashcards",description:"使用学生和专家为各种课程创建的闪卡学习。",isPaid:!1,isStudentFriendly:!0}]},{name:"计算器",originalName:"Calculators",id:"calculators",websites:[{name:"Calc Resource",url:"https://calcresource.com/index.html",description:"提供各种计算器和数学计算资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"eFunda",url:"https://www.efunda.com/home.cfm",description:"提供计算器、公式以及材料和工艺信息的网站。",isPaid:!1,isStudentFriendly:!1},{name:"LCM Calculator",url:"https://www.calculator.net/lcm-calculator.html",description:"查找最小公倍数的计算器。",isPaid:!1,isStudentFriendly:!1},{name:"GCF Calculator",url:"https://www.calculator.net/gcf-calculator.html?numberinputs=9%2C+57%2C+72&x=75&y=22",description:"查找最大公因数的计算器。",isPaid:!1,isStudentFriendly:!1},{name:"CalculatorSoup",url:"https://www.calculatorsoup.com/",description:"为不同数学目的提供各种计算器的在线平台。",isPaid:!1,isStudentFriendly:!1},{name:"RapidTables",url:"https://www.rapidtables.com/",description:"提供计算器和表格集合供快速参考的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Linear Algebra Calculator",url:"https://www.emathhelp.net/en/linear-algebra-calculator/?u=3%2C1%2C4&v=-2%2C0%2C5&action=cross+product",description:"线性代数计算器，如叉积。",isPaid:!1,isStudentFriendly:!1},{name:"Wikipedia: List of Physical Quantities",url:"https://en.wikipedia.org/wiki/List_of_physical_quantities",description:"列出各种物理量的Wikipedia页面。",isPaid:!1,isStudentFriendly:!1},{name:"eMathHelp Linear Algebra Calculator",url:"https://www.emathhelp.net/en/calculators/linear-algebra/",description:"线性代数计算的在线计算器。",isPaid:!1,isStudentFriendly:!1},{name:"Online Math School",url:"https://onlinemschool.com/math/assistance/",description:"提供数学帮助以及各种计算器和资源的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"在线课程",originalName:"MOOC (Massive Open Online Courses)",id:"mooc--massive-open-online-courses-",websites:[{name:"InfoCobuild - Audio Video Courses",url:"http://www.infocobuild.com/education/audio-video-courses/",description:"来自世界各地学院和大学免费音频/视频学术课程讲座的集合。按学术科目进行良好分类，包括生物学、化学、计算机科学、经济学、电子和电气工程、历史、文学、材料科学、数学、物理学和心理学。",isPaid:!1,isStudentFriendly:!1},{name:"MIT OpenCourseWare",url:"https://ocw.mit.edu/",description:"MIT的倡议，提供对广泛课程材料的免费开放访问。",isPaid:!1,isStudentFriendly:!1},{name:"Coursera",url:"https://www.coursera.org/",description:"在线学习平台，提供来自世界各地大学和组织的课程、证书和学位项目。",isPaid:!1,isStudentFriendly:!1},{name:"Wikiversity",url:"https://en.wikiversity.org/wiki/Wikiversity:Main_Page",description:"维基媒体基金会项目，提供免费的教育内容和资源。",isPaid:!1,isStudentFriendly:!1},{name:"Udemy",url:"https://www.udemy.com/",description:"平台提供由专家教授的各种主题的大量在线课程。",isPaid:!1,isStudentFriendly:!1},{name:"Open Culture",url:"https://www.openculture.com/",description:"提供免费文化和教育媒体的网站，包括课程、教科书和有声读物。",isPaid:!1,isStudentFriendly:!1},{name:"edX",url:"https://www.edx.org/",description:"在线学习平台，提供来自世界各地大学和机构的课程、证书和学位。",isPaid:!1,isStudentFriendly:!1},{name:"Udacity",url:"https://www.udacity.com/",description:"专注于技术相关课程和与行业领导者合作设计的纳米学位项目的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Stanford Online",url:"https://online.stanford.edu/",description:"斯坦福大学的在线学习平台，提供各种课程和项目。",isPaid:!1,isStudentFriendly:!1},{name:"OpenLearn",url:"https://www.open.edu/openlearn/",description:"开放大学的倡议，提供对课程材料和教育资源的免费访问。",isPaid:!1,isStudentFriendly:!1},{name:"Open Learning Initiative (OLI)",url:"https://oli.cmu.edu/",description:"卡内基梅隆大学的平台，提供公开可用的课程和资源。",isPaid:!1,isStudentFriendly:!1},{name:"MITx",url:"https://www.mitx.org/",description:"MIT的平台，提供专注于前沿研究的在线课程和项目。",isPaid:!1,isStudentFriendly:!1},{name:"Open Yale Courses",url:"https://oyc.yale.edu/",description:"耶鲁大学的倡议，提供由杰出教师教授的入门课程的免费访问。",isPaid:!1,isStudentFriendly:!1},{name:"Alison",url:"https://alison.com/",description:"平台提供各种主题的免费在线课程和文凭。",isPaid:!1,isStudentFriendly:!1},{name:"Academic Earth",url:"https://academicearth.org/",description:"聚合来自世界顶级大学在线课程的网站。",isPaid:!1,isStudentFriendly:!1},{name:"NPTEL",url:"https://nptel.ac.in/",description:"国家技术增强学习计划，提供工程和科学在线课程。",isPaid:!1,isStudentFriendly:!1},{name:"University of the People",url:"https://www.uopeople.edu/",description:"在线大学，提供免学费的认证学位项目。",isPaid:!1,isStudentFriendly:!1},{name:"Canvas Network",url:"https://www.canvas.net/",description:"使用Canvas学习管理系统提供来自各种机构的在线课程和项目的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Isaac Newton Institute for Mathematical Sciences",url:"https://www.newton.ac.uk/",description:"研究所网站，提供数学科学资源和项目的访问。",isPaid:!1,isStudentFriendly:!1},{name:"Saylor Academy",url:"https://www.saylor.org/",description:"非营利倡议，提供免费的自定进度在线课程。",isPaid:!1,isStudentFriendly:!1},{name:"Connexions",url:"https://cnx.org/",description:"提供各种学科领域开放教育资源和教科书的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Directory of Open Access Journals (DOAJ)",url:"https://doaj.org/",description:"在线目录，提供对高质量开放获取科学和学术期刊的访问。",isPaid:!1,isStudentFriendly:!1},{name:"Learning on the Internet",url:"https://www.learningontheinternet.com/?ref=producthunt",description:"展示来自各种来源的精选教育内容和资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Class Central",url:"https://www.classcentral.com/",description:"在线课程的搜索引擎和评论平台，聚合来自各种提供商的课程。",isPaid:!1,isStudentFriendly:!1},{name:"OCW SNU",url:"https://ocw.snu.ac.kr/",description:"首尔国立大学的开放课件。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"TLDR This",url:"https://tldrthis.com/",description:"将任何文本总结为简洁易懂内容的平台，帮助用户克服信息过载。",isPaid:!1,isStudentFriendly:!1},{name:"Archive.org General Index",url:"https://archive.org/details/GeneralIndex",description:"提供访问超过1.07亿篇期刊文章的综合索引。",isPaid:!1,isStudentFriendly:!1},{name:"Homework Help Global",url:"https://www.homeworkhelpglobal.com/",description:"提供专业和定制论文写作服务的在线平台。",isPaid:!1,isStudentFriendly:!1},{name:"Dartmouth Academic Careers",url:"https://sites.dartmouth.edu/nyhan/academic-careers/",description:"提供学术职业见解的资源。",isPaid:!1,isStudentFriendly:!1},{name:"CNX",url:"https://cnx.org/",description:"查看和分享免费教育材料的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Reach Out Michigan Tutorials",url:"https://www.reachoutmichigan.org/learn/tutorials.html#math",description:"涵盖各种主题的在线教程和参考资料集合。",isPaid:!1,isStudentFriendly:!1},{name:"Open Text BC",url:"https://opentextbc.ca/",description:"简单的图书制作软件，允许用户发布教科书、学术专著、教学大纲、小说和非小说书籍、白皮书等多种格式。",isPaid:!1,isStudentFriendly:!1},{name:"Modern for Wikipedia",url:"https://chrome.google.com/webstore/detail/modern-for-wikipedia/emdkdnnopdnajipoapepbeeiemahbjcn",description:"通过现代化可定制设计增强Wikipedia体验的Chrome扩展。",isPaid:!1,isStudentFriendly:!1},{name:"Wikiwand - Wikipedia Modern",url:"https://chrome.google.com/webstore/detail/wikiwand-wikipedia-modern/emffkefkbkpkgpdeeooapgaicgmcbolj",description:"优化Wikipedia内容以改善阅读体验的Chrome扩展。",isPaid:!1,isStudentFriendly:!1},{name:"List of Academic Databases and Search Engines",url:"https://en.wikipedia.org/wiki/List_of_academic_databases_and_search_engines",description:"列出学术数据库和搜索引擎的Wikipedia页面。",isPaid:!1,isStudentFriendly:!1},{name:"Scribbr APA Citation Generator",url:"https://www.scribbr.com/citation/generator/apa/",description:"提供准确APA引文的平台，经专家验证，受数百万人信任。",isPaid:!1,isStudentFriendly:!1},{name:"Bridges: About Institutions, Histories, and Artifacts",url:"https://temple.manifoldapp.org/projects/bridges",description:"关于美国学院和大学生活的机构、历史和文物的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Tropy",url:"https://tropy.org/",description:"通过将照片转化为物品来组织研究的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Linda Hall Library Catalog",url:"https://catalog.lindahall.org/discovery/search?vid=01LINDAHALL_INST:LHL",description:"Linda Hall图书馆目录，允许您搜索书籍、期刊、会议论文集、技术报告和标准以及其他材料，专注于科学、工程和技术。",isPaid:!1,isStudentFriendly:!1},{name:"Project Abstracts",url:"https://projectabstracts.com/",description:"各个领域学术小项目和毕业项目的项目摘要和下载集合。",isPaid:!1,isStudentFriendly:!1},{name:"SCIRP Open Access Journal",url:"https://www.scirp.org/journal/OpenAccess",description:"提供各种科学学科开放获取学术期刊的平台，促进免费获取研究。",isPaid:!1,isStudentFriendly:!1},{name:"DOI.org",url:"https://www.doi.org/",description:"数字对象标识符(DOI)的官方网站，提供数字资源（包括学术论文和数据集）的持久标识和访问系统。",isPaid:!1,isStudentFriendly:!1}]},{name:"科学",originalName:"Science",id:"science",subcategories:[{name:"传记",originalName:"Biographies",id:"biographies",websites:[{name:"Mathematics History",url:"https://mathshistory.st-andrews.ac.uk/",description:"包含3000多位数学家传记和2000多页论文及支持材料的免费在线资源。",isPaid:!1,isStudentFriendly:!1},{name:"Web of Stories",url:"https://www.webofstories.com/",description:"聆听我们时代一些伟大人物的生活故事。",isPaid:!1,isStudentFriendly:!1},{name:"Letters of Note",url:"https://lettersofnote.com/",description:"历史上最迷人的信件。",isPaid:!1,isStudentFriendly:!1},{name:"Organism Earth Library",url:"https://www.organism.earth/library/",description:"生物地球图书馆。",isPaid:!1,isStudentFriendly:!1},{name:"Darwin Project",url:"https://www.darwinproject.ac.uk/",description:"进化科学家查尔斯·达尔文（1809-1882）写的信件。",isPaid:!1,isStudentFriendly:!1},{name:"Darwin Online",url:"https://darwin-online.org.uk/",description:"世界上最大最广泛使用的查尔斯·达尔文资源。",isPaid:!1,isStudentFriendly:!1},{name:"Newton Project",url:"https://www.newtonproject.ox.ac.uk/",description:"艾萨克·牛顿爵士（1642-1727）所有著作的在线版本。",isPaid:!1,isStudentFriendly:!1},{name:"Bethe",url:"https://bethe.cornell.edu/index.html",description:"汉斯·贝特的个人和历史观点。",isPaid:!1,isStudentFriendly:!1},{name:"Open Source Shakespeare",url:"https://www.opensourceshakespeare.org/",description:"开源莎士比亚。",isPaid:!1,isStudentFriendly:!1},{name:"Leonardo da Vinci",url:"https://www.leonardodavinci.net/",description:"列奥纳多·达·芬奇，他的生活和艺术作品。",isPaid:!1,isStudentFriendly:!1},{name:"Our Karl Popper",url:"https://ourkarlpopper.net/",description:"卡尔·波普尔如何改变了我们的生活（来自五大洲的证词）。",isPaid:!1,isStudentFriendly:!1},{name:"Varlam Shalamov",url:"https://shalamov.ru/en/",description:"瓦尔拉姆·沙拉莫夫的著作和历史背景，这位俄国作家以其关于苏联劳改营监禁的短篇小说系列而闻名。",isPaid:!1,isStudentFriendly:!1},{name:"Samuel Beckett On-Line Resources",url:"https://www.samuel-beckett.net/",description:"塞缪尔·贝克特在线资源和链接页面。（塞缪尔·贝克特的《[等待戈多](https://www.samuel-beckett.net/Waiting_for_Godot_Part1.html)》）",isPaid:!1,isStudentFriendly:!1},{name:"Philip K. Dick",url:"https://philipdick.com/",description:"致力于科幻作家菲利普·K·迪克（1928-82）的生活和作品。",isPaid:!1,isStudentFriendly:!1},{name:"Alan Turing Digital Archive",url:"https://turingarchive.kings.cam.ac.uk/",description:"这个数字档案包含图灵的许多信件、谈话记录、照片和未发表的论文。",isPaid:!1,isStudentFriendly:!1},{name:"Turing.org.uk",url:"https://www.turing.org.uk/",description:"致力于数学家和计算机科学家艾伦·图灵的生活和工作的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Sherlock Holmes Series",url:"https://sherlock-holm.es/",description:"阿瑟·柯南·道尔爵士的夏洛克·福尔摩斯系列（公有领域，免版权）。",isPaid:!1,isStudentFriendly:!1},{name:"Feynman Lectures Online",url:"https://www.feynmanlectures.caltech.edu/",description:"理查德·费曼的在线讲座。",isPaid:!1,isStudentFriendly:!1},{name:"Leibniz Translations",url:"https://www.leibniz-translations.com/index2.php",description:"提供哲学家和数学家戈特弗里德·威廉·莱布尼茨作品翻译的资源。",isPaid:!1,isStudentFriendly:!1},{name:"David Hume",url:"https://davidhume.org/",description:"致力于苏格兰哲学家大卫·休谟的平台。准确、有用地呈现休谟几乎所有著作。",isPaid:!1,isStudentFriendly:!1},{name:"Fooled by Randomness",url:"https://fooledbyrandomness.com/",description:"纳西姆·尼古拉斯·塔勒布的主页。",isPaid:!1,isStudentFriendly:!1},{name:"Prabook",url:"https://prabook.com/web/home.html/",description:"提供著名人物传记信息的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Famous Mathematicians",url:"https://famous-mathematicians.org/",description:"展示著名数学家信息和传记的网站。",isPaid:!1,isStudentFriendly:!1}]},{name:"书籍文章",originalName:"Books, Articles, Texts",id:"books--articles--texts",websites:[{name:"Archive.org/texts",url:"https://archive.org/details/texts",description:"提供对大量数字内容集合免费通用访问的数字图书馆。",isPaid:!1,isStudentFriendly:!1},{name:"Open Library",url:"https://openlibrary.org/",description:"包含图书元数据的通用目录，可访问广泛的数字图书。",isPaid:!1,isStudentFriendly:!1},{name:"OpenStax",url:"https://openstax.org/",description:"为教育目的提供免费灵活的教科书和资源。",isPaid:!1,isStudentFriendly:!1},{name:"Project Gutenberg",url:"https://www.gutenberg.org/",description:"提供超过60,000本免费电子书（包括许多经典作品）的数字图书馆。",isPaid:!1,isStudentFriendly:!1},{name:"Wikibooks",url:"https://en.wikibooks.org/wiki/Main_Page",description:"任何人都可以编辑的开放内容教科书集合。",isPaid:!1,isStudentFriendly:!1},{name:"Wikisource",url:"https://wikisource.org/wiki/Main_Page",description:"允许协作改进其内容的免费图书馆。",isPaid:!1,isStudentFriendly:!1},{name:"MIT Classics",url:"https://classics.mit.edu/",description:"从59位不同作者的441部经典文学作品列表中选择。",isPaid:!1,isStudentFriendly:!1},{name:"Goodreads Free eBooks",url:"https://www.goodreads.com/ebooks?sort=readable",description:"Goodreads上提供的免费图书。",isPaid:!1,isStudentFriendly:!1},{name:"Lit2Go",url:"https://etc.usf.edu/lit2go/",description:"免费在线故事和诗歌有声读物集合（Mp3格式）。",isPaid:!1,isStudentFriendly:!1},{name:"Booksc",url:"https://booksc.org/",description:"世界最大的科学文章存储库，包含7000万+免费文章。",isPaid:!1,isStudentFriendly:!1},{name:"IntechOpen",url:"https://www.intechopen.com/books",description:"阅读、分享和下载超过5,800本同行评审的开放获取图书。",isPaid:!1,isStudentFriendly:!1},{name:"FreeTechBooks",url:"https://www.freetechbooks.com/",description:"免费/开放获取在线计算机科学图书、教科书和讲义的数据库。",isPaid:!1,isStudentFriendly:!1},{name:"FreeComputerBooks",url:"https://freecomputerbooks.com/",description:"提供免费计算机科学和编程图书集合的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Manning",url:"https://www.manning.com/",description:"编程图书出版商。",isPaid:!1,isStudentFriendly:!1},{name:"Holy Books",url:"https://holybooks.com/",description:"下载免费PDF格式的精神文本电子书。",isPaid:!1,isStudentFriendly:!1},{name:"Librivox",url:"https://librivox.org/",description:"提供来自世界各地志愿者朗读的免费公有领域有声读物。",isPaid:!1,isStudentFriendly:!1},{name:"Online Books Page",url:"https://onlinebooks.library.upenn.edu/",description:"网络上超过300万本免费图书的列表。",isPaid:!1,isStudentFriendly:!1},{name:"Audible",url:"https://www.audible.com/",description:"提供优质音频故事讲述的平台，拥有广泛的有声读物选择。",isPaid:!1,isStudentFriendly:!1},{name:"VCU Transcendentalism",url:"https://archive.vcu.edu/english/engweb/transcendentalism/",description:"关于先验主义文本的教育超文本空间，链接到其他互联网空间。",isPaid:!1,isStudentFriendly:!1},{name:"Library of Short Stories",url:"https://www.libraryofshortstories.com/",description:"在线图书馆，包含超过1000个经典短篇小说，可供阅读和下载。",isPaid:!1,isStudentFriendly:!1},{name:"SlideShare",url:"https://www.slideshare.net/",description:"分享演示文稿和文档的在线平台。",isPaid:!1,isStudentFriendly:!1},{name:"Free-eBooks.net",url:"https://www.free-ebooks.net/",description:"发现数百个虚构和非虚构类别中的数千位新作者。",isPaid:!1,isStudentFriendly:!1},{name:"University of Pennsylvania Digital Library",url:"https://digital.library.upenn.edu/books/",description:"提供图书集合访问的数字图书馆。",isPaid:!1,isStudentFriendly:!1},{name:"Feedbooks Public Domain",url:"https://www.feedbooks.com/catalog/public_domain",description:"免费提供公有领域图书目录的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Authorama",url:"https://www.authorama.com/",description:"提供不同作者公有领域图书集合的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Google Play - Top Selling Free Books",url:"https://play.google.com/store/books/collection/topselling_free?clp=ChcKFQoPdG9wc2VsbGluZ19mcmVlEAcYAQ%3D%3D:S:ANO1ljKuey8&gsr=ChkKFwoVCg90b3BzZWxsaW5nX2ZyZWUQBxgB:S:ANO1ljIbX7M",description:"Google Play上最畅销免费图书集合。",isPaid:!1,isStudentFriendly:!1},{name:"Taoism.net",url:"https://taoism.net/",description:"探索道教的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Early Modern Texts",url:"https://earlymoderntexts.com/",description:"提供早期现代哲学文本现代英语译本的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Online Library of Liberty",url:"https://oll.libertyfund.org/",description:"涉及自由核心问题的学术作品精选集合。",isPaid:!1,isStudentFriendly:!1},{name:"Online Books Library",url:"https://onlinebooks.library.upenn.edu/",description:"宾夕法尼亚大学的在线图书馆，提供对网络上300万本免费图书的访问。",isPaid:!1,isStudentFriendly:!1},{name:"Elegant eBooks",url:"https://www.ibiblio.org/ebooks/",description:"以时尚版本查找优秀的虚构和非虚构经典作品。本网站上几乎所有电子书都来自公有领域图书。",isPaid:!1,isStudentFriendly:!1},{name:"22 Free Data Science Books",url:"https://www.wzchen.com/data-science-books",description:"精选的免费优质数据科学图书汇编，帮助探索数据科学职业道路的人士。每本书的最后更新日期包含在括号中。",isPaid:!1,isStudentFriendly:!1},{name:"Chest of Books",url:"https://chestofbooks.com/",description:"免费在线图书馆，提供各种主题的大量图书集合，包括科学、技术、艺术和文学。",isPaid:!1,isStudentFriendly:!1},{name:"Stephen Wolfram: A New Kind of Science",url:"https://www.wolframscience.com/nks/",description:"史蒂芬·沃尔夫拉姆《一种新的科学》图书的在线版本，提供目录和细胞自动机与复杂性相关材料的访问。",isPaid:!1,isStudentFriendly:!1}]},{name:"书籍推荐",originalName:"Book Recommendations and Summaries",id:"book-recommendations-and-summaries",websites:[{name:"Read Next",url:"https://read-next.com/",description:"来自科学家、投资者、企业家、名人和作者等各个领域名人推荐的3,000+本图书集合。",isPaid:!1,isStudentFriendly:!1},{name:"Goodbooks",url:"https://www.goodbooks.io/",description:"提供来自世界各地成功有趣人士的8,500+本图书推荐。",isPaid:!1,isStudentFriendly:!1},{name:"Most Recommended Books",url:"https://mostrecommendedbooks.com/",description:"策展500+专家、600+列表、500+图书系列，提供100%验证的图书推荐。",isPaid:!1,isStudentFriendly:!1},{name:"Books Chatter",url:"https://bookschatter.com/",description:"从人们的推文中寻找图书推荐，显示相关推文。",isPaid:!1,isStudentFriendly:!1},{name:"Leafmarks",url:"https://www.leafmarks.com/",description:"探索来自著名作者、顶级CEO、传奇投资者和喜爱名人的图书推荐。",isPaid:!1,isStudentFriendly:!1},{name:"Bookstash",url:"https://bookstash.io/",description:"展示名人推荐的顶级图书，3分钟或更短时间内总结。",isPaid:!1,isStudentFriendly:!1},{name:"Abakcus",url:"https://abakcus.com/books/",description:"关于数学和一些科学的图书集合。",isPaid:!1,isStudentFriendly:!1},{name:"BookBub",url:"https://www.bookbub.com/welcome",description:"根据您的偏好获得个性化图书推荐。",isPaid:!1,isStudentFriendly:!1},{name:"Hacker News Books",url:"https://hackernewsbooks.com/",description:"每周策展Hacker News上提到的最佳图书。",isPaid:!1,isStudentFriendly:!1},{name:"Goodreads",url:"https://www.goodreads.com/",description:"世界最大的读者和图书推荐网站。",isPaid:!1,isStudentFriendly:!1},{name:"What Should I Read Next?",url:"https://www.whatshouldireadnext.com/",description:"输入您喜欢的图书，网站将分析我们庞大的真实读者最爱图书数据库，为您提供图书推荐和下一步阅读建议。",isPaid:!1,isStudentFriendly:!1},{name:"Blas",url:"https://blas.com/",description:"Blas Moros总结的超过400本图书。",isPaid:!1,isStudentFriendly:!1},{name:"Google Books - Talk to Books",url:"https://books.google.com/talktobooks/",description:"允许用户使用自然语言与图书对话的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"地图数据",originalName:"Maps and Data",id:"maps-and-data",websites:[{name:"Our World in Data",url:"https://ourworldindata.org/covid-vaccinations",description:"按国家分类的COVID-19疫苗接种数据。",isPaid:!1,isStudentFriendly:!1},{name:"WebSDR",url:"https://websdr.org/",description:"连接到互联网的软件定义无线电接收器，允许众多听众同时收听和调谐，捕获来自地球的当前信号。",isPaid:!1,isStudentFriendly:!1},{name:"GSM Security Map",url:"https://gsmmap.org/",description:"比较移动网络在GSM安全方面保护能力的地图。",isPaid:!1,isStudentFriendly:!1},{name:"International Campuses",url:"https://cbert.org/resources-data/intl-campus/",description:"国际校园列表。",isPaid:!1,isStudentFriendly:!1},{name:"United States International College Campuses on Google Maps",url:"https://www.google.com/maps/d/u/0/viewer?mid=1ckm_TSM8mbCrnhA8COpBNG-qJqTR2IW3&ll=43.664774893391936%2C24.56718262638249&z=5",description:"展示美国国际大学校园的地图。",isPaid:!1,isStudentFriendly:!1},{name:"Submarine Cable Map",url:"https://www.submarinecablemap.com/",description:"展示世界海底电缆系统的交互式地图。",isPaid:!1,isStudentFriendly:!1},{name:"Observable",url:"https://observablehq.com/",description:"协作探索、分析和解释数据的平台。",isPaid:!1,isStudentFriendly:!1},{name:"FixPhrase",url:"https://fixphrase.com/",description:"仅用四个词定位地球上任何地方，适用于处理几个词比一长串数字更方便的情况。",isPaid:!1,isStudentFriendly:!1},{name:"Common Crawl",url:"https://commoncrawl.org/",description:"任何人都可以访问和分析的开放网络爬取数据存储库。",isPaid:!1,isStudentFriendly:!1},{name:"Mount Everest 3D Map",url:"https://mount-everest3d.com/3d-map/",description:"珠穆朗玛峰的交互式3D地图。",isPaid:!1,isStudentFriendly:!1},{name:"Visualize Value Archive",url:"https://archivve.visualizevalue.com/",description:"Visualize Value的视觉内容档案。",isPaid:!1,isStudentFriendly:!1},{name:"Fliist",url:"https://fliist.com/en",description:"创建和分享您最喜爱列表的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Search Engine Map",url:"https://www.searchenginemap.com/",description:"流行搜索引擎的视觉表示。",isPaid:!1,isStudentFriendly:!1},{name:"Friendly Dubinsky",url:"https://friendly-dubinsky-cb22fe.netlify.app/",description:"不同地方的地图。",isPaid:!1,isStudentFriendly:!1},{name:"Real-Time SpaceX Starlink Satellite Tracker",url:"https://www.starlinkmap.org/",description:"提供SpaceX星链卫星实时跟踪器的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Album of Computational Fluid Motion",url:"https://album-of-cfm.com/",description:"展示计算流体运动图像的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Visualization of Every Job Title in the World",url:"https://duarteocarmo.com/blog/every-job-world",description:"将世界上每个职位名称分类为10个类别的交互式可视化。",isPaid:!1,isStudentFriendly:!1},{name:"ObservableHQ",url:"https://observablehq.com/@tophtucker/examples-of-bitemporal-charts",description:"双时态图表示例",isPaid:!1,isStudentFriendly:!1},{name:"The Uncensored Library",url:"https://uncensoredlibrary.com/en",description:"由无国界记者发布、BlockWorks、DDB Berlin和MediaMonks创建的Minecraft服务器和地图，旨在规避新闻自由受限国家的审查制度",isPaid:!1,isStudentFriendly:!1},{name:"Lighthouse Map",url:"https://geodienst.github.io/lighthousemap/",description:"显示世界各地灯塔位置的交互式地图，为每个地点提供地理空间信息和历史。",isPaid:!1,isStudentFriendly:!1}]},{name:"Arxiv",originalName:"Arxiv",id:"arxiv",websites:[{name:"ArxivXplorer",url:"https://arxivxplorer.com/",description:"专为探索arXiv科学论文而设计的搜索工具，允许用户查找和筛选各学科的研究文章。",isPaid:!1,isStudentFriendly:!1},{name:"Interactive Data Map of ArXiv Machine Learning Papers",url:"https://datamapplot.readthedocs.io/en/latest/auto_examples/plot_interactive_arxiv_ml.html",description:"显示ArXiv机器学习部分论文的交互式数据地图。",isPaid:!1,isStudentFriendly:!1},{name:"ArXiv Machine Learning Landscape",url:"https://lmcinnes.github.io/datamapplot_examples/ArXiv_data_map_example.html",description:"展示ArXiv机器学习研究景观的可视化。",isPaid:!1,isStudentFriendly:!1},{name:"DataMapPlot Examples",url:"https://lmcinnes.github.io/datamapplot_examples/arXiv/",description:"使用DataMapPlot库在流形上可视化数据的示例和演示集合，专注于arXiv数据集。",isPaid:!1,isStudentFriendly:!1}]},{name:"信息图表",originalName:"Infographic",id:"infographic",websites:[{name:"Information is Beautiful",url:"https://informationisbeautiful.net/",description:"专注于基于事实和数据决策的平台，通过视觉吸引的图形呈现信息。",isPaid:!1,isStudentFriendly:!1},{name:"Visual Capitalist",url:"https://www.visualcapitalist.com/",description:"数据驱动的视觉内容来源，涵盖市场、技术、能源和全球经济等各种主题。",isPaid:!1,isStudentFriendly:!1},{name:"Infographic Journal",url:"https://infographicjournal.com/",description:"涵盖广泛主题的信息图表档案。",isPaid:!1,isStudentFriendly:!1},{name:"D3.js",url:"https://d3js.org/",description:"基于数据操作文档的JavaScript库，能够创建动态和交互式数据可视化。",isPaid:!1,isStudentFriendly:!1},{name:"Deniz Cem On Duygu Portfolio",url:"https://www.denizcemonduygu.com/",description:"Deniz Cem On Duygu的信息图表个人作品集。",isPaid:!1,isStudentFriendly:!1},{name:"Data Visualization Catalogue",url:"https://datavizcatalogue.com/index.html",description:"提供各种类型数据可视化信息的目录。",isPaid:!1,isStudentFriendly:!1},{name:"Tableau Public",url:"https://public.tableau.com/app/discover",description:"免费平台，可在线探索、创建和公开分享数据可视化。",isPaid:!1,isStudentFriendly:!1},{name:"Will Robots Take My Job?",url:"https://willrobotstakemyjob.com/",description:"估算各种职业的自动化风险、增长、工资等。",isPaid:!1,isStudentFriendly:!1},{name:"Preceden",url:"https://www.preceden.com/",description:"在线时间线和路线图制作工具。",isPaid:!1,isStudentFriendly:!1},{name:"Jason Davies",url:"https://www.jasondavies.com/",description:"Jason Davies的个人网站，展示他的项目和可视化作品。",isPaid:!1,isStudentFriendly:!1}]},{name:"哲学",originalName:"Philosophy",id:"philosophy",websites:[{name:"Desolhar Philo",url:"https://www.desolhar-philo.com/",description:"关于哲学的笔记。",isPaid:!1,isStudentFriendly:!1},{name:"VisualizingSEP",url:"https://www.visualizingsep.com/",description:"探索斯坦福哲学百科全书的交互式可视化和搜索引擎。",isPaid:!1,isStudentFriendly:!1},{name:"Hakob's Sandbox",url:"https://hakobsandbox.openetext.utoronto.ca/",description:"关于科学史和科学哲学入门的在线图书。",isPaid:!1,isStudentFriendly:!1},{name:"Philosophy A Level",url:"https://philosophyalevel.com/",description:"A-Level哲学学习资源。",isPaid:!1,isStudentFriendly:!1},{name:"Deniz Cemon Duygu - History of Philosophy",url:"https://www.denizcemonduygu.com/philo/browse/",description:"总结和可视化的哲学史。",isPaid:!1,isStudentFriendly:!1},{name:"Interactive timeline of philosophical ideas",url:"https://www.denizcemonduygu.com/portfolio/the-history-of-philosophy/",description:". - 重要哲学家及其思想的视觉表示和探索。",isPaid:!1,isStudentFriendly:!1},{name:"Beyng",url:"https://www.beyng.com/",description:"致力于马丁·海德格尔哲学的资源，提供他的作品和思想的英文译本和讨论。",isPaid:!1,isStudentFriendly:!1},{name:"Greg Egan's Official Website",url:"https://www.gregegan.net/",description:"科幻作家和计算机程序员Greg Egan的作品、哲学和项目信息。",isPaid:!1,isStudentFriendly:!1},{name:"Ayn Rand Institute Courses",url:"https://courses.aynrand.org/",description:"提供安·兰德哲学免费课程的教育平台，包括客观主义及其在生活和社会中的应用。",isPaid:!1,isStudentFriendly:!1}]},{name:"社会科学",originalName:"Social Sciences",id:"social-sciences",websites:[{name:"Temple Manifold - All Projects",url:"https://temple.manifoldapp.org/projects/all",description:"各种社会科学的优秀资源集合。",isPaid:!1,isStudentFriendly:!1}]},{name:"历史",originalName:"History",id:"history",websites:[{name:"Histography",url:"https://histography.io/",description:"跨越140亿年历史（从大爆炸到2015年）的交互式时间线。",isPaid:!1,isStudentFriendly:!1},{name:"Unenumerated Blog",url:"https://unenumerated.blogspot.com/",description:"涵盖各种历史主题的全面详细博客。",isPaid:!1,isStudentFriendly:!1},{name:"Human Origins - Human Family Tree",url:"https://humanorigins.si.edu/evidence/human-family-tree",description:"探索人类进化史的交互式人类家谱。",isPaid:!1,isStudentFriendly:!1},{name:"OldEra Timeline",url:"https://timeline.oldera.org/",description:"允许您在可缩放界面上查看整个历史的交互式时间线。",isPaid:!1,isStudentFriendly:!1},{name:"Nuclear Secrecy Blog",url:"https://blog.nuclearsecrecy.com/",description:"关于核机密的历史信息，深入探讨核武器的发展和影响。",isPaid:!1,isStudentFriendly:!1},{name:"The Ascent of Humanity",url:"https://ascentofhumanity.com/",description:"探索分离时代、重聚时代的概念，以及塑造人类历史转型的危机汇聚。",isPaid:!1,isStudentFriendly:!1},{name:"Today in Science History",url:"https://todayinsci.com/",description:"提供特定日期历史事件的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Khipu Field Guide",url:"https://khipufieldguide.com/guidebook/Introduction.html",description:"提供奇普（古印加结绳记录系统）信息的指南手册。",isPaid:!1,isStudentFriendly:!1},{name:"Hellenistic History",url:"https://www.hellenistichistory.com/",description:"希腊化历史研究资源，涵盖从亚历山大大帝时代到罗马帝国的政治、文化和社会发展。",isPaid:!1,isStudentFriendly:!1},{name:"Perseus Digital Library",url:"https://www.perseus.tufts.edu/hopper/",description:"提供大量古典文本、图像和资源，用于研究古希腊和古罗马文化。",isPaid:!1,isStudentFriendly:!1},{name:"Futility Closet",url:"https://www.futilitycloset.com/",description:"历史、文学、语言、艺术、哲学和数学中有趣奇事的集合，旨在帮助您尽可能愉快地消磨时间。",isPaid:!1,isStudentFriendly:!1}]},{name:"地球科学",originalName:"Geoscience",id:"geoscience",websites:[{name:"River Runner Global",url:"https://river-runner-global.samlearner.com/",description:"提供从世界任何地点的雨滴到其终点路径的可视化。",isPaid:!1,isStudentFriendly:!1},{name:"Celestrak Satellite Visualization",url:"https://celestrak.com/cesium/orbit-viz.php?tle=/pub/TLE/catalog.txt&satcat=/pub/satcat.txt&referenceFrame=1",description:"提供卫星可视化工具。",isPaid:!1,isStudentFriendly:!1},{name:"Mars Now",url:"https://mars.nasa.gov/explore/mars-now/",description:"显示火星卫星的当前位置，为火星探索提供见解。",isPaid:!1,isStudentFriendly:!1},{name:"Physical Geology",url:"https://temple.manifoldapp.org/projects/physical-geology",description:"涵盖物理地质学的基本主题。",isPaid:!1,isStudentFriendly:!1},{name:"Flood Site",url:"https://floodsite.net/juniorfloodsite/",description:"关于洪水的教育资源。",isPaid:!1,isStudentFriendly:!1},{name:"NEMO - Nucleus for European Modeling of the Ocean",url:"https://www.nemo-ocean.eu/",description:"由欧洲联盟开发的平台，提供与多种空间和时间尺度海洋建模相关的信息和资源。",isPaid:!1,isStudentFriendly:!1},{name:"USGS Earthquake Map",url:"https://earthquake.usgs.gov/earthquakes/map/?extent=25.56227,4.08691&extent=45.08904,70.00488&list=false",description:"美国地质调查局显示最近地震活动的地图。",isPaid:!1,isStudentFriendly:!1},{name:"LatLong.net",url:"https://www.latlong.net/",description:"在地图上查找任何位置纬度和经度坐标的工具，提供地理坐标和相关数据。",isPaid:!1,isStudentFriendly:!1},{name:"Latitude.to",url:"https://latitude.to/",description:"允许用户查找世界任何地址或位置的GPS坐标，具有详细的地图功能。",isPaid:!1,isStudentFriendly:!1}]},{name:"生物学",originalName:"Biology",id:"biology",websites:[{name:"Sectional Anatomy",url:"https://www.sectional-anatomy.org/",description:"放射学横断面解剖学的免费在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Deep Sea",url:"https://neal.fun/deep-sea/",description:"探索海洋深处的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Ask Nature",url:"https://asknature.org/",description:"生物学启发的策略、创新或教育资源。",isPaid:!1,isStudentFriendly:!1},{name:"We Are Hosts for Memes",url:"https://wearehostsformemes.com/",description:"您已接触到元迷因。",isPaid:!1,isStudentFriendly:!1},{name:"Birdwatching Zone",url:"https://birdwatching.zone/",description:"专门用于观鸟的网站，记录了超过300种鸟类，为爱好者提供识别技巧和资源。",isPaid:!1,isStudentFriendly:!1},{name:"OneZoom",url:"https://www.onezoom.org/",description:"生命之树，展示地球上所有生命如何连接。探索220万个物种之间的关系，查看超过10万张图像，了解物种如何从共同祖先进化而来。通过树状结构缩放，发现地球上生命的多样性。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Reproducibility Institute",url:"https://reproducibilityinstitute.org/w/",description:"提供学术论文、速查表和付费课程种子的项目。",isPaid:!1,isStudentFriendly:!1},{name:"EBSCO",url:"https://www.ebsco.com/",description:"研究数据库、电子期刊、杂志订阅、电子书和发现服务的提供商。",isPaid:!1,isStudentFriendly:!1},{name:"Zooniverse",url:"https://www.zooniverse.org/",description:"参与真实研究，拥有超过50个活跃的在线公民科学项目。",isPaid:!1,isStudentFriendly:!1},{name:"Experiment",url:"https://experiment.com/",description:"帮助资助下一波科学研究。",isPaid:!1,isStudentFriendly:!1},{name:"Closer to Truth",url:"https://www.closertotruth.com/",description:"Robert Lawrence Kuhn探索宇宙的基本问题。",isPaid:!1,isStudentFriendly:!1},{name:"Nature Scitable",url:"https://www.nature.com/scitable/",description:"科学概述图书馆。定制您自己的电子书，创建在线课堂，贡献和分享内容，并与同事网络连接。",isPaid:!1,isStudentFriendly:!1},{name:"Vinaire",url:"https://vinaire.me/",description:"物理和数学课程。",isPaid:!1,isStudentFriendly:!1},{name:"VisualPDE",url:"https://visualpde.com/",description:"探索科学和数学的交互式平台，提供波浪、病毒和反应扩散模式等主题的模拟。",isPaid:!1,isStudentFriendly:!1},{name:"Whole Earth",url:"https://wholeearth.info/",description:"Whole Earth出版物的近乎完整档案，这是Stewart Brand和POINT基金会从1968年到2002年出版的一系列期刊和杂志，为学术、教育和研究目的提供。",isPaid:!1,isStudentFriendly:!1},{name:"Sketchplanations",url:"https://sketchplanations.com/categories/science",description:"科学概念的简单草图和视觉解释集合，旨在使复杂主题更易理解。",isPaid:!1,isStudentFriendly:!1}]},{name:"物理",originalName:"Physics",id:"physics",subcategories:[{name:"量子物理",originalName:"Quantum",id:"quantum",websites:[{name:"Webb Telescope",url:"https://webbtelescope.org/",description:"詹姆斯·韦伯太空望远镜的官方网站。",isPaid:!1,isStudentFriendly:!1}]},{name:"量子游戏",originalName:"Quantum Games",id:"quantum-games",websites:[{name:"Virtual Lab by Quantum Flytrap",url:"https://quantumflytrap.com/lab",description:"模拟多粒子量子系统的光学实验，支持量子密钥分发和纠缠探索。",isPaid:!1,isStudentFriendly:!1},{name:"Hello Quantum",url:"https://quantum-computing.ibm.com/lab",description:"通过交互式益智游戏介绍量子电路和门，具有简化量子概念的图形界面。",isPaid:!1,isStudentFriendly:!1},{name:"Particle in a Box",url:"https://learnqm.gatech.edu",description:"通过对比经典物理和量子物理的2D单人平台游戏演示量子叠加和能级。",isPaid:!1,isStudentFriendly:!1},{name:"Psi and Delta",url:"https://learnqm.gatech.edu",description:"通过专注于叠加和量子概率的合作游戏鼓励量子力学的协作学习。",isPaid:!1,isStudentFriendly:!1},{name:"QPlayLearn",url:"https://qplaylearn.com",description:"提供交互式工具、视频和游戏进行多层次量子物理教育，满足不同学习者的需求。",isPaid:!1,isStudentFriendly:!1},{name:"Quantum Odyssey",url:"https://quarksinteractive.com",description:"通过游戏化界面可视化量子算法开发，辅助量子计算逻辑和状态演化教育。",isPaid:!1,isStudentFriendly:!1},{name:"Quantum Moves 2",url:"https://www.scienceathome.org/games/quantum-moves-2",description:"让公民科学家参与优化量子实验，解决量子优化中的现实挑战。",isPaid:!1,isStudentFriendly:!1},{name:"The Virtual Quantum Optics Laboratory",url:"https://www.vqol.org/",description:"支持量子光学实验的设计和仿真，为教育目的连接经典力学和量子力学。",isPaid:!1,isStudentFriendly:!1},{name:"Arxiv Paper - Quantum Games and Interactive Tools for Quantum Technologies Outreach and Education",url:"https://arxiv.org/abs/2202.07756",description:"关于使用游戏和交互工具让公众理解量子技术的全面讨论。",isPaid:!1,isStudentFriendly:!1}]},{name:"天文学",originalName:"Astronomy",id:"astronomy",websites:[{name:"Fear of Physics",url:"https://www.fearofphysics.com/",description:'提供免费的"天文学101"课程。',isPaid:!1,isStudentFriendly:!1},{name:"Galileo's Applets",url:"https://galileo.phys.virginia.edu/classes/109N/more_stuff/Applets/home.html",description:"提供关于早期月球观测和各种运动相关主题的小程序。",isPaid:!1,isStudentFriendly:!1},{name:"100,000 Stars",url:"https://stars.chromeexperiments.com/",description:"展示十万颗附近恒星的可视化，提供沉浸式体验。",isPaid:!1,isStudentFriendly:!1},{name:"The Million Earth Solar System",url:"https://planetplanet.net/2018/06/01/the-million-earth-solar-system/",description:"探索拥有一百万颗类地行星的太阳系概念。",isPaid:!1,isStudentFriendly:!1},{name:"Space Telescope Live",url:"https://spacetelescopelive.org/webb",description:"实时访问詹姆斯·韦伯太空望远镜的数据和观测结果，让用户探索太空研究和发现。",isPaid:!1,isStudentFriendly:!1},{name:"Telescope Optics",url:"https://www.telescope-optics.net/",description:"为业余望远镜制造者提供的资源，详细介绍望远镜光学系统的设计和构造。",isPaid:!1,isStudentFriendly:!1},{name:"Orbital Basics",url:"https://t-neumann.github.io/space/OrbitalBasics/",description:"解释轨道力学基本概念的教育资源，专为太空科学初学者设计。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"The Theoretical Minimum",url:"https://theoreticalminimum.com/home",description:"由世界知名物理学家伦纳德·苏斯金德教授的斯坦福大学继续教育课程系列。",isPaid:!1,isStudentFriendly:!1},{name:"Theoretical Physics - Cambridge",url:"https://www.damtp.cam.ac.uk/user/tong/index.html",description:"来自剑桥大学的理论物理资源和信息。",isPaid:!1,isStudentFriendly:!1},{name:"University of Virginia Classical and Modern Physics II",url:"https://galileo.phys.virginia.edu/classes/632.ral5q.summer06/lectures.html",description:"弗吉尼亚大学经典与现代物理学II课程的讲义和材料。",isPaid:!1,isStudentFriendly:!1},{name:"Mueller Group - Cornell",url:"https://muellergroup.lassp.cornell.edu/index.html",description:"康奈尔大学原子和固体物理实验室。",isPaid:!1,isStudentFriendly:!1},{name:"Ptable",url:"https://ptable.com/?lang=en",description:"提供元素及其性质信息的交互式可定制周期表。",isPaid:!1,isStudentFriendly:!1},{name:"National Institute of Standards and Technology – Fundamental Physical Constants",url:"https://www.nist.gov/pml/fundamental-physical-constants",description:"提供权威的物理常数数据，为研究和技术应用提供基本测量数据。",isPaid:!1,isStudentFriendly:!1},{name:"Mechanics Map",url:"https://mechanicsmap.psu.edu/index.html",description:"来自宾夕法尼亚州立大学的交互式资源，用于探索机械系统和理解基本力学原理。",isPaid:!1,isStudentFriendly:!1}]},{name:"数学",originalName:"Mathematics",id:"mathematics",subcategories:[{name:"数学艺术",originalName:"Math + Art",id:"math---art",websites:[{name:"Paul Nylander's website",url:"https://bugman123.com/",description:"保罗·尼兰德最喜欢的爱好和兴趣，特别是科学和艺术",isPaid:!1,isStudentFriendly:!1},{name:"NYC DOE CS4All",url:"https://nycdoe-cs4all.github.io/",description:"使用p5.js进行计算媒体介绍",isPaid:!1,isStudentFriendly:!1},{name:"Texample",url:"https://texample.net/",description:"LaTeX示例和社区",isPaid:!1,isStudentFriendly:!1},{name:"Snowflakes Project",url:"https://www.dynamicmath.xyz/collective-math-art/",description:"集体数学艺术的雪花项目",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Open Logic Text",url:"https://openlogicproject.org/about/",description:"开源、模块化、协作编写的形式（元）逻辑和形式方法教学材料集合。",isPaid:!1,isStudentFriendly:!1},{name:"OEIS - The On-Line Encyclopedia of Integer Sequences",url:"https://oeis.org/",description:"提供综合整数序列数据库的平台。",isPaid:!1,isStudentFriendly:!1},{name:"First Principles of Mathematics",url:"https://pdodds.w3.uvm.edu/",description:"涵盖各种数学主题第一原理的资源。",isPaid:!1,isStudentFriendly:!1},{name:"38 Best Math Websites for Students",url:"https://blog.symbaloo.com/webmixes/11/best-math-websites",description:"为学生推荐的38个数学网站汇编。",isPaid:!1,isStudentFriendly:!1},{name:"Math Hints",url:"https://mathhints.com/",description:"为各种数学主题提供简单解释和示例的免费网站。",isPaid:!1,isStudentFriendly:!1},{name:"Better Explained",url:"https://betterexplained.com/",description:"专注于理解数学概念而非死记硬背的平台，提供虚数和指数等主题的清晰课程。",isPaid:!1,isStudentFriendly:!1},{name:"Mathway",url:"https://www.mathway.com/",description:"免费数学问题求解器。",isPaid:!1,isStudentFriendly:!1},{name:"ChiliMath",url:"https://www.chilimath.com/",description:"数学主题的在线资源。",isPaid:!1,isStudentFriendly:!1},{name:"Visual and Interactive Introduction to Complex Analysis",url:"https://complex-analysis.com/",description:"提供复分析可视化和交互式介绍的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Matrices as Tensor Network Diagrams",url:"https://www.math3ma.com/blog/matrices-as-tensor-network-diagrams",description:"讨论矩阵表示为张量网络图的文章。",isPaid:!1,isStudentFriendly:!1},{name:"What is Category Theory Anyway?",url:"https://www.math3ma.com/blog/what-is-category-theory-anyway",description:"用范畴论探索数学事物的宏观体系。",isPaid:!1,isStudentFriendly:!1},{name:"Algebra Practice Problems",url:"https://www.algebrapracticeproblems.com/",description:"提供代数练习题和清晰解释的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Matrixology",url:"https://pdodds.w3.uvm.edu/teaching/courses/2016-08UVM-122/",description:"矩阵学课程资源。",isPaid:!1,isStudentFriendly:!1},{name:"Math Courses at NITK",url:"https://sam.nitk.ac.in/courses-taught.html",description:"卡纳塔克邦国家理工学院（NITK）数学课程列表。",isPaid:!1,isStudentFriendly:!1},{name:"Easy Mathematical Tricks from Counting Through Calculus",url:"https://mathhints.com/",description:"从计数到微积分的简易数学技巧集合。",isPaid:!1,isStudentFriendly:!1},{name:"Math Salamanders",url:"https://www.math-salamanders.com/",description:"为儿童或学生提供有用计算器（如反向百分比计算器）和数学工作表的平台。",isPaid:!1,isStudentFriendly:!1},{name:"3Blue1Brown Non-Videos",url:"https://some.3b1b.co/non-videos",description:"3Blue1Brown的SoME非视频内容获奖作品。",isPaid:!1,isStudentFriendly:!1},{name:"Mathigon – The Mathematical Playground",url:"https://mathigon.org/",description:"提供教育内容和资源的交互式数学平台。",isPaid:!1,isStudentFriendly:!1},{name:"Math Warehouse",url:"https://www.mathwarehouse.com/",description:"提供交互式数学活动、演示、定义和示例课程、工作表和其他资源。",isPaid:!1,isStudentFriendly:!1},{name:"Cut the Knot",url:"https://www.cut-the-knot.org/",description:"提供交互式数学谜题、问题和可视化的资源，通过解决问题来探索数学概念。",isPaid:!1,isStudentFriendly:!1},{name:"Trigonography",url:"https://trigonography.com/?page_id=230",description:"探索三角学及其应用的网站，具有视觉辅助工具、公式和解题技巧。",isPaid:!1,isStudentFriendly:!1},{name:"Lamar Math Tutorials",url:"https://tutorial.math.lamar.edu/",description:"综合的数学教程和练习题集合，涵盖从代数到微积分和微分方程的主题。",isPaid:!1,isStudentFriendly:!1},{name:"Art of Problem Solving",url:"https://artofproblemsolving.com/company",description:"专注于培养各学科问题解决技能，包括数学、物理、编程和语言艺术。提供严格的在线课程、实体学院和旨在鼓励学生批判性思维、实验和坚持的引人入胜的课程。",isPaid:!1,isStudentFriendly:!1},{name:"Mathematics Genealogy Project",url:"https://genealogy.math.ndsu.nodak.edu/index.php",description:"旨在汇编和分享全球数学家的综合信息，从学术机构和个人贡献者收集数据。",isPaid:!1,isStudentFriendly:!1},{name:"Enjeck Complicated Math Equation Generator",url:"https://enjeck.com/num2math/?input=4&submit=Generate",description:"根据用户输入生成复杂数学方程，提供练习或可视化数学问题的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Erdos Problems",url:"https://www.erdosproblems.com/",description:"专门探索与保罗·埃尔德什相关的开放数学问题的网站，包括数论和组合学中的持续挑战。",isPaid:!1,isStudentFriendly:!1},{name:"The Electronic Journal of Combinatorics",url:"https://www.combinatorics.org/ojs/index.php/eljc",description:"发表组合学研究论文和文章的开放获取期刊，组合学是处理计数、排列和结构的数学领域。",isPaid:!1,isStudentFriendly:!1},{name:"Zeta by Amir Hirsch",url:"https://amirhirsch.com/zeta/index.html",description:"致力于探索黎曼假设的网站，为对数论感兴趣的人提供交互式工具和资源。",isPaid:!1,isStudentFriendly:!1},{name:"Tungsteno",url:"https://www.tungsteno.io/",description:"提供教学工具的平台，让每个人都能接触数学，完全免费，基于开放协作。",isPaid:!1,isStudentFriendly:!1},{name:"PlanetMath",url:"https://planetmath.org/",description:"在线数学资源和协作平台，提供开放获取的数学内容，包括定义、定理和证明。",isPaid:!1,isStudentFriendly:!1},{name:"Geomstats Tutorials",url:"https://geomstats.github.io/tutorials/index.html",description:"几何统计学教程和资源，提供在流形上实现统计技术的实际示例和代码。",isPaid:!1,isStudentFriendly:!1},{name:"Ximera - MOOCulus",url:"https://ximera.osu.edu/mooculus",description:"为在线课程设计的交互式数学模块和学习资源集合，专注于微积分和相关学科。",isPaid:!1,isStudentFriendly:!1}]},{name:"工程",originalName:"Engineering",id:"engineering",subcategories:[{name:"土木工程",originalName:"Civil Engineering",id:"civil-engineering",websites:[{name:"Floor Plan Lab",url:"https://floorplanlab.com/",description:"创建和可视化平面图的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Engineers Edge",url:"https://www.engineersedge.com/",description:"提供结构和机械工程主题文章和资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"3D House Planner",url:"https://3dhouseplanner.com/",description:"网络上的免费3D平面图规划应用程序。",isPaid:!1,isStudentFriendly:!1}]},{name:"机械工程",originalName:"Mechanical Engineering",id:"mechanical-engineering",websites:[{name:"507 Movements",url:"https://507movements.com/toc.html",description:"以动画机械运动为特色的网站，提供对各种机械系统的视觉理解。",isPaid:!1,isStudentFriendly:!1},{name:"MadeHow",url:"https://www.madehow.com/",description:"解释和详述各种产品制造过程的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Comprehensive Structural Analysis Book",url:"https://temple.manifoldapp.org/projects/structural-analysis",description:"提供结构分析综合书籍的在线资源。",isPaid:!1,isStudentFriendly:!1},{name:"Awesome MechEng",url:"https://github.com/m2n037/awesome-mecheng#heat-transfer",description:"优秀的机械工程资源。",isPaid:!1,isStudentFriendly:!1},{name:"Animated Dynamics",url:"https://dynref.engr.illinois.edu/ref.html",description:"可视化动力学仿真的交互式参考资料，旨在帮助用户更好地理解复杂的机械系统。",isPaid:!1,isStudentFriendly:!1},{name:"Wikimedia Commons - Engine Animations",url:"https://commons.wikimedia.org/wiki/Category:Animations_of_engines",description:"演示各种类型发动机（从燃烧到电动）功能的动画图像和视频集合。",isPaid:!1,isStudentFriendly:!1},{name:"Mechanisms/menu-gear",url:"https://www.mekanizmalar.com/menu-gear.html",description:"齿轮机构动画。",isPaid:!1,isStudentFriendly:!1},{name:"Mechanism Animations",url:"https://people.ohio.edu/williams/html/MechanismAnimations.html",description:"提供演示机械系统和机构的交互式动画，帮助用户可视化和理解其运动和功能。",isPaid:!1,isStudentFriendly:!1},{name:"CalcResource",url:"https://calcresource.com/resources.html",description:"定期更新的专注于力学和静力学的资源列表，有助于学习和理解这些学科。",isPaid:!1,isStudentFriendly:!1},{name:"Engineering Toolbox",url:"https://www.engineeringtoolbox.com/",description:"涵盖广泛工程主题的综合资源。",isPaid:!1,isStudentFriendly:!1},{name:"StructX",url:"https://structx.com/",description:"为专业人士和学生提供结构工程资源的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Amesweb",url:"https://www.amesweb.info/",description:"包含计算器和涵盖各种工程主题文章的平台。",isPaid:!1,isStudentFriendly:!1},{name:"RoyMech",url:"https://www.roymech.co.uk/",description:"提供结构和机械工程主题深度文章的参考网站。",isPaid:!1,isStudentFriendly:!1},{name:"Arcelor-Mittal Design Software",url:"https://sections.arcelormittal.com/design_aid/design_software/EN",description:"阿赛洛-米塔尔提供的钢结构免费设计软件。",isPaid:!1,isStudentFriendly:!1},{name:"BU Moss",url:"https://www.bu.edu/moss/",description:"专注于理解细长结构的课程和资源。",isPaid:!1,isStudentFriendly:!1},{name:"Homestyler",url:"https://www.homestyler.com/",description:"面向专业人士和业余爱好者的简单省时的在线室内设计工具。",isPaid:!1,isStudentFriendly:!1},{name:"How a Car Works",url:"https://www.howacarworks.com/",description:"解释汽车工作原理的完整免费指南。",isPaid:!1,isStudentFriendly:!1},{name:"IIHS Ratings",url:"https://www.iihs.org/ratings",description:"来自公路安全保险协会的碰撞测试评级和安全信息。",isPaid:!1,isStudentFriendly:!1},{name:"Euro NCAP",url:"https://www.euroncap.com/en/",description:"为车辆提供安全评级的欧洲新车评估计划。",isPaid:!1,isStudentFriendly:!1},{name:"Toaster Museum",url:"http://toastermuseum.com/",description:"专门收藏古董烤面包机的展示，为烤面包机爱好者和收藏家展示复古型号，同时提供历史信息和保存细节。",isPaid:!1,isStudentFriendly:!1}]},{name:"材料纳米技术",originalName:"Materials / Nanotechnology",id:"materials---nanotechnology",websites:[{name:"DoITPoMS",url:"https://www.doitpoms.ac.uk/index.php",description:"来自剑桥大学材料科学与冶金系的DoITPoMS",isPaid:!1,isStudentFriendly:!1},{name:"Nanoscience Resources",url:"https://ssd.phys.strath.ac.uk/",description:"斯特拉斯克莱德大学物理系的纳米科学资源",isPaid:!1,isStudentFriendly:!1},{name:"StatNano",url:"https://product.statnano.com/",description:"关于纳米技术产品信息的可靠来源，目前广泛应用于各种工业领域",isPaid:!1,isStudentFriendly:!1},{name:"MyMiniFactory",url:"https://www.myminifactory.com/",description:"世界上最大的免费下载具有文化意义的3D可打印物体生态系统",isPaid:!1,isStudentFriendly:!1},{name:"Roy Mech",url:"https://roymech.org/",description:"提供与机械工程和工程材料相关的有用信息、表格、时间表和公式的网站。",isPaid:!1,isStudentFriendly:!1}]},{name:"电子工程",originalName:"Electronics Engineering",id:"electronics-engineering",websites:[{name:"Electrical4U",url:"https://www.electrical4u.com/",description:"学习电气工程的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Electronics Tutorials",url:"https://www.electronics-tutorials.ws/",description:"提供电子学各个方面教程的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Octopart",url:"https://octopart.com/",description:"电子元件平台，帮助用户查找和比较组件。",isPaid:!1,isStudentFriendly:!1},{name:"TechSpot - How CPUs Are Designed and Built",url:"https://www.techspot.com/article/1821-how-cpus-are-designed-and-built/",description:"涵盖计算机架构、处理器电路设计、超大规模集成电路、芯片制造和计算未来趋势的系列文章。",isPaid:!1,isStudentFriendly:!1},{name:"Open Circuits Book",url:"https://www.opencircuitsbook.com/",description:'"开放电路"是对日常电子产品内部精美设计的摄影探索。',isPaid:!1,isStudentFriendly:!1},{name:"Digi-Key Electronics",url:"https://www.digikey.com/",description:"电子元件在线市场，为工程师和制造商提供大量零件、工具和资源选择。",isPaid:!1,isStudentFriendly:!1},{name:"Telematic Connections Timeline",url:"http://telematic.walkerart.org/timeline/index.html",description:"探索远程信息处理和网络通信历史与影响的交互式时间轴，专注于艺术和技术。",isPaid:!1,isStudentFriendly:!1},{name:"Cybergraph",url:"https://cybergraph.dubberly.com/#",description:"控制论的视觉探索，提供网络的交互式表示。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Animagraffs",url:"https://animagraffs.com/",description:"以详细动画信息图表（animagraffs）为特色的教育网站，直观地解释各学科的复杂主题和过程。",isPaid:!1,isStudentFriendly:!1},{name:"Technology Student",url:"https://technologystudent.com/index.htm",description:"包含大量信息表、练习和动画的资源，涵盖各种技术相关主题。",isPaid:!1,isStudentFriendly:!1},{name:"Nuclear Power",url:"https://www.nuclear-power.com/",description:"旨在学习核能和反应堆物理基础的非营利项目，涵盖核电站、反应堆物理、热工程、材料和辐射防护等主题。",isPaid:!1,isStudentFriendly:!1},{name:"Knovel",url:"https://app.knovel.com/kn",description:"为各种工程和科学目的提供工具和计算器的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Engineering Library",url:"https://engineeringlibrary.org/reference/",description:"工程主题参考图书馆。",isPaid:!1,isStudentFriendly:!1},{name:"Engineering Toolbox",url:"https://www.engineeringtoolbox.com/index.html",description:"提供工程工具和信息的在线资源。",isPaid:!1,isStudentFriendly:!1},{name:"Text to CAD",url:"https://text-to-cad.zoo.dev/",description:"将文字描述转换为CAD模型的工具，帮助用户从书面输入生成技术图纸。",isPaid:!1,isStudentFriendly:!1},{name:"McMaster-Carr",url:"https://www.mcmaster.com/",description:"硬件、工具、原材料、工业材料和维护设备供应商，通过广泛的产品目录为各行业提供服务。",isPaid:!1,isStudentFriendly:!1},{name:"Engineer on a Disk: Modeling",url:"https://engineeronadisk.com/book_modeling/modelTOC.html",description:"为工程师和开发人员提供系统建模和仿真实践指南的书籍，包含实用示例和解释。",isPaid:!1,isStudentFriendly:!1},{name:"Bartosz Ciechanowski Archives",url:"https://ciechanow.ski/archives/",description:"巴托什·齐哈诺夫斯基关于设计和技术的文章集合。",isPaid:!1,isStudentFriendly:!1}]},{name:"计算机科学",originalName:"Computer Science",id:"computer-science",subcategories:[{name:"数据结构算法",originalName:"Data Structures and Algorithms (DS&A)",id:"data-structures-and-algorithms--ds-a-",websites:[{name:"Dictionary of Algorithms and Data Structures",url:"https://xlinux.nist.gov/dads/",description:"NIST算法和数据结构词典。",isPaid:!1,isStudentFriendly:!1},{name:"Visualgo",url:"https://visualgo.net/en",description:"通过动画可视化数据结构和算法的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Adventures In Coding & Algorithms",url:"https://entcheva.github.io/",description:"探索编程和算法的博客。",isPaid:!1,isStudentFriendly:!1},{name:"Damn Cool Algorithms",url:"https://blog.notdot.net/tag/damn-cool-algorithms",description:"专门介绍特别有趣算法的博客。",isPaid:!1,isStudentFriendly:!1},{name:"Skiena's Algorithms Lectures",url:"https://www3.cs.stonybrook.edu/~algorith/video-lectures/",description:"史蒂文·斯基纳的算法视频讲座。",isPaid:!1,isStudentFriendly:!1},{name:"Visual Algorithms",url:"https://thimbleby.gitlab.io/algorithm-wiki-site/",description:"呈现算法视觉表示的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Sinon - Algorithms",url:"https://sinon.org/algorithms/",description:"涵盖计算机科学重要成果的总结页面。",isPaid:!1,isStudentFriendly:!1}]},{name:"大O记号",originalName:"Big-O notation",id:"big-o-notation",websites:[{name:"Algorithms and Data Structures Big-O Notation",url:"https://cooervo.github.io/Algorithms-DataStructures-BigONotation/",description:"涵盖各种算法和数据结构大O表示法的综合资源。",isPaid:!1,isStudentFriendly:!1},{name:"Big-O Cheat Sheet",url:"https://www.bigocheatsheet.com/",description:"展示计算机科学中常用算法空间和时间复杂度（大O）的网页。",isPaid:!1,isStudentFriendly:!1},{name:"Comp160 Data Cheat",url:"https://www.clear.rice.edu/comp160/data_cheat.html",description:"提供计算机科学中数据结构和相关概念信息的资源。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Webopedia",url:"https://www.webopedia.com/",description:"计算机和IT术语的在线技术词典、学习指南和评论。",isPaid:!1,isStudentFriendly:!1},{name:"Teach Yourself Computer Science",url:"https://teachyourselfcs.com/",description:"计算机科学自学的综合指南。",isPaid:!1,isStudentFriendly:!1},{name:"Open Source Society University - Computer Science",url:"https://github.com/open-source-society/computer-science",description:"开源社会大学提供的计算机科学学习课程。",isPaid:!1,isStudentFriendly:!1},{name:"Functional Computer Science Curriculum",url:"https://functionalcs.github.io/curriculum/",description:"专注于计算机科学中函数式编程概念的课程。",isPaid:!1,isStudentFriendly:!1},{name:"Menimagerie",url:"https://www.menimagerie.com/",description:"探索理论计算机科学概念，从数系到康托无穷、哥德尔定理和自动机。",isPaid:!1,isStudentFriendly:!1},{name:"Computer Science Library",url:"https://www.compscilib.com/",description:"通过自动化分步解决方案和练习题帮助掌握计算机科学和数学课程概念的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Computer Jargon",url:"https://www.computerhope.com/jargon.htm",description:"计算机相关术语和技术术语词汇表。",isPaid:!1,isStudentFriendly:!1},{name:"Talks by Alan Kay",url:"https://tinlizzie.org/IA/index.php/Talks_by_Alan_Kay",description:"计算机科学家艾伦·凯演讲集。",isPaid:!1,isStudentFriendly:!1},{name:"Everything Computer Science",url:"https://everythingcomputerscience.com/",description:"涵盖计算机科学主题的广泛文章和教程，包括编程、算法、数据结构和软件开发实践。",isPaid:!1,isStudentFriendly:!1},{name:"Richard Sutton Video Lectures",url:"https://videolectures.net/search/?query=Richard%20sutton",description:"理查德·萨顿视频讲座和演讲集，他是强化学习和人工智能领域的著名研究者。",isPaid:!1,isStudentFriendly:!1}]},{name:"人工智能",originalName:"AI/ML",id:"ai-ml",subcategories:[{name:"机器人",originalName:"Robotics",id:"robotics",websites:[{name:"Robots Guide",url:"https://robotsguide.com/",description:"为机器人学初学者和爱好者提供指南、评论和见解的综合资源。",isPaid:!1,isStudentFriendly:!1},{name:"Control Challenges",url:"https://janismac.github.io/ControlChallenges/",description:"机器人学的LeetCode。",isPaid:!1,isStudentFriendly:!1}]},{name:"大语言模型",originalName:"LLMs",id:"llms",websites:[{name:"Hacker Llama",url:"https://osanseviero.github.io/hackerllama/blog/posts/hitchhiker_guide/",description:"关于加入本地大语言模型社区时需要了解的有用术语的博客文章。",isPaid:!1,isStudentFriendly:!1},{name:"Moebio Mind",url:"https://moebio.com/mind/",description:"探索语言模型内部工作原理，可视化GPT-3 API生成的语义空间和词汇补全轨迹。",isPaid:!1,isStudentFriendly:!1},{name:"LLM Visualization",url:"https://bbycroft.net/llm",description:"用于可视化大语言模型并探索其结构、行为和输出的交互式工具。",isPaid:!1,isStudentFriendly:!1},{name:"RR LM Game",url:"https://rr-lm-game.herokuapp.com/",description:"基于浏览器的语言建模游戏。",isPaid:!1,isStudentFriendly:!1}]},{name:"提示工程",originalName:"Prompt Engineering",id:"prompt-engineering",websites:[{name:"PromptPerfect",url:"https://promptperfect.jina.ai/",description:"为大语言模型、大型模型和LMOps设计的尖端提示优化器。",isPaid:!1,isStudentFriendly:!1},{name:"Jailbreak Chat",url:"https://www.jailbreakchat.com/",description:"大语言模型越狱提示集合。",isPaid:!1,isStudentFriendly:!1},{name:"MidJourney Styles and Keywords Reference",url:"https://github.com/willwulfken/MidJourney-Styles-and-Keywords-Reference",description:"AI图像生成的样式和关键词参考。",isPaid:!1,isStudentFriendly:!1},{name:"QuickRef ChatGPT",url:"https://quickref.me/chatgpt",description:"ChatGPT速查表。",isPaid:!1,isStudentFriendly:!1},{name:"OpenAI Cookbook",url:"https://cookbook.openai.com/",description:"OpenAI提供的AI工作实用指南手册。",isPaid:!1,isStudentFriendly:!1},{name:"Prompting Guide",url:"https://www.promptingguide.ai/",description:"为AI模型创建有效提示的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Instaprompt",url:"https://www.instaprompt.ai/?ref=producthunt",description:"提供即时写作提示的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Midjourney Prompt Helper",url:"https://promptfolder.com/midjourney-prompt-helper/",description:"帮助用户为Midjourney生成详细有效提示的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Free Midjourney Prompt",url:"https://www.freemidjourneyprompt.com/",description:"提供大量免费Midjourney提示，让用户轻松通过优化的自然语言提示生成图像。",isPaid:!1,isStudentFriendly:!1},{name:"Prompt Engineering Guide",url:"https://learnprompting.org/docs/introduction",description:"提示工程综合指南，提供为AI模型和创意任务制作有效提示的基本原则和技术。",isPaid:!1,isStudentFriendly:!1}]},{name:"AI工具",originalName:"AI tools",id:"ai-tools",websites:[{name:"Future Tools",url:"https://www.futuretools.io/",description:"寻找符合您需求的精确AI工具的平台。",isPaid:!1,isStudentFriendly:!1},{name:"WarpSound AI",url:"https://www.warpsound.ai/",description:"仅用提示在几秒钟内创建高质量生成式AI音乐。",isPaid:!1,isStudentFriendly:!1},{name:"AI Tools Arena",url:"https://aitoolsarena.com/",description:"展示各种AI工具和资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Klavier AI",url:"https://klavier.ai/",description:"允许您在选择的网页和文档上与ChatGPT进行问答。",isPaid:!1,isStudentFriendly:!1},{name:"Aiva Valley",url:"https://aivalley.ai/",description:"最新的AI工具和提示来源。",isPaid:!1,isStudentFriendly:!1},{name:"Bardeen AI",url:"https://www.bardeen.ai/",description:"几分钟内无需代码即可自动化手动工作和任务的AI工具。",isPaid:!1,isStudentFriendly:!1},{name:"Speechify",url:"https://speechify.com/",description:"通过领先的AI文本转语音阅读器聆听文档、文章、PDF、邮件等任何内容。",isPaid:!1,isStudentFriendly:!1},{name:"Humata AI",url:"https://www.humata.ai/",description:"上传任何PDF或文档，几秒钟内获得答案。",isPaid:!1,isStudentFriendly:!1},{name:"Syntelly",url:"https://app.syntelly.com/search",description:"用于有机化学和医学化学的人工智能。",isPaid:!1,isStudentFriendly:!1},{name:"Warp.dev - Warp AI",url:"https://www.warp.dev/warp-ai?source=producthunt&ref=producthunt",description:"无需编写代码即可构建API的AI驱动工具。",isPaid:!1,isStudentFriendly:!1},{name:"Feedback by AI",url:"https://feedbackbyai.com/?ref=producthunt",description:"利用AI为写作生成可操作反馈的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Seeing the World through Your Eyes",url:"https://world-from-eyes.github.io/",description:"重建通过人类眼睛反映的世界。",isPaid:!1,isStudentFriendly:!1},{name:"Auxiliary Tools",url:"https://www.auxiliary.tools/",description:"为人类提供AI实验和工具的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Hemingway Editor",url:"https://hemingwayapp.com/",description:"针对文体风格的拼写检查器。",isPaid:!1,isStudentFriendly:!1},{name:"FuturePedia",url:"https://www.futurepedia.io/",description:"领先的AI资源平台，致力于帮助各行业专业人士利用AI技术进行创新和发展。",isPaid:!1,isStudentFriendly:!1},{name:"ExplainPaper",url:"https://www.explainpaper.com/",description:"用户可以上传研究论文、高亮困惑文本并获得解释的平台，使研究论文更易理解。",isPaid:!1,isStudentFriendly:!1},{name:"Summarize Tech",url:"https://www.summarize.tech/",description:"AI驱动的工具，可获取任何长YouTube视频的摘要，如讲座、直播活动或政府会议。",isPaid:!1,isStudentFriendly:!1},{name:"YouTubeTranscript",url:"https://youtubetranscript.com/",description:"获取YouTube视频转录和摘要。",isPaid:!1,isStudentFriendly:!1}]},{name:"数据科学",originalName:"Data Science",id:"data-science",websites:[{name:"DataTau",url:"https://www.datatau.com/news",description:"专注于数据的Hackernews",isPaid:!1,isStudentFriendly:!1},{name:"DatasetList",url:"https://www.datasetlist.com/",description:"来自网络各处的机器学习数据集列表",isPaid:!1,isStudentFriendly:!1},{name:"Stat Learning",url:"https://www.statlearning.com/",description:"学习统计学和相关主题的平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"数据库",originalName:"Databases",id:"databases",websites:[{name:"ImageNet",url:"https://www.image-net.org/",description:"根据WordNet层次结构组织的图像数据库。",isPaid:!1,isStudentFriendly:!1},{name:"DataSheets.com",url:"https://www.datasheets.com/",description:"可搜索的电子元件数据表和采购信息数据库，专为设计工程师和电子采购代理设计。",isPaid:!1,isStudentFriendly:!1},{name:"Academic Torrents",url:"https://academictorrents.com/",description:"社区驱动的大型数据集共享平台，为学术研究提供种子下载，包括科学论文、数据集和多媒体。",isPaid:!1,isStudentFriendly:!1},{name:"DBLP",url:"https://dblp.org/",description:"提供主要计算机科学期刊、会议和论文集书目信息的综合开放式数据库。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Doublespeak Chat",url:"https://doublespeak.chat/#/handbook",description:"关于大语言模型黑客攻击的经验性、非学术性和实用指南。",isPaid:!1,isStudentFriendly:!1},{name:"Fast.ai Course",url:"https://course.fast.ai/",description:"深度学习和机器学习在线课程。",isPaid:!1,isStudentFriendly:!1},{name:"Papers with Code",url:"https://paperswithcode.com/sota",description:"机器学习任务最先进结果及相关代码的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Delta Academy Maps",url:"https://maps.joindeltaacademy.com/",description:"机器学习数学地图。",isPaid:!1,isStudentFriendly:!1},{name:"Inside the Matrix by PyTorch",url:"https://pytorch.org/blog/inside-the-matrix/",description:"PyTorch的博客文章，可视化矩阵乘法、注意力机制等内容。",isPaid:!1,isStudentFriendly:!1},{name:"Directory of AI Models",url:"https://docs.google.com/spreadsheets/d/1gc6yse74XCwBx028HV_cvdxwXkmXejVjkO-Mz2uwE0k/edit?pli=1#gid=0",description:"提供各种AI模型目录的Google表格文档，包含名称、日期、参数、组织等信息。",isPaid:!1,isStudentFriendly:!1},{name:"Papers with Code",url:"https://paperswithcode.com/",description:"提供最新机器学习研究论文及代码实现和基准测试的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Globe Engineer Explorer",url:"https://explorer.globe.engineer/",description:"应用AI公司，制作为人类理解优化信息表示的产品。",isPaid:!1,isStudentFriendly:!1},{name:"Colah's Blog",url:"https://colah.github.io/",description:"克里斯托弗·奥拉关于深度学习和人工智能的博客，包含详细解释、教程和研究见解。",isPaid:!1,isStudentFriendly:!1},{name:"TensorFlow Playground",url:"https://playground.tensorflow.org",description:"用于实验神经网络的交互式工具，让用户可视化不同配置对模型训练和分类任务的影响。",isPaid:!1,isStudentFriendly:!1},{name:"Ben's Bites",url:"https://bensbites.com/catalog",description:"提供各种一口大小AI资源的目录。",isPaid:!1,isStudentFriendly:!1}]},{name:"Web开发",originalName:"Web Development",id:"web-development",subcategories:[{name:"前端开发",originalName:"Front-end",id:"front-end",websites:[{name:"SafeRules",url:"https://anthonyhobday.com/sideprojects/saferules/",description:"每次都可以安全遵循的视觉设计规则。",isPaid:!1,isStudentFriendly:!1},{name:"Can I Email",url:"https://www.caniemail.com/",description:"检查HTML和CSS功能的电子邮件客户端兼容性。",isPaid:!1,isStudentFriendly:!1},{name:"Egg Gradients",url:"https://www.eggradients.com/",description:"渐变背景颜色集合。",isPaid:!1,isStudentFriendly:!1},{name:"Frontend Checklist",url:"https://frontendchecklist.io/",description:"在将网站/HTML页面发布到生产环境之前需要拥有/测试的所有元素的详尽列表。",isPaid:!1,isStudentFriendly:!1},{name:"Frontend Mentor",url:"https://www.frontendmentor.io/",description:"在处理专业设计时解决现实世界HTML、CSS和JavaScript挑战的平台。",isPaid:!1,isStudentFriendly:!1},{name:"CodePen",url:"https://codepen.io/",description:"构建、测试和发现前端代码的平台。探索[主题](https://codepen.io/topics/)。",isPaid:!1,isStudentFriendly:!1},{name:"Electron",url:"https://www.electronjs.org/",description:"使用JavaScript、HTML和CSS构建跨平台桌面应用程序的框架。",isPaid:!1,isStudentFriendly:!1},{name:"Homepage Gallery",url:"https://homepage.gallery/",description:"展示500多个网站以获得Web设计灵感的画廊。",isPaid:!1,isStudentFriendly:!1},{name:"HTML Dog",url:"https://htmldog.com/",description:"HTML、CSS和JavaScript的综合资源。",isPaid:!1,isStudentFriendly:!1},{name:"Learn.shayhowe",url:"https://learn.shayhowe.com/",description:"学习编写HTML和CSS代码。",isPaid:!1,isStudentFriendly:!1},{name:"This vs That",url:"https://thisthat.dev/",description:"探索前端开发概念之间的差异。",isPaid:!1,isStudentFriendly:!1},{name:"Know-It-All",url:"https://know-it-all.io/",description:"列出您在Web开发方面知道和不知道的内容。",isPaid:!1,isStudentFriendly:!1},{name:"You Might Not Need jQuery",url:"https://youmightnotneedjquery.com/",description:"jQuery替代方案。",isPaid:!1,isStudentFriendly:!1}]},{name:"HTML",originalName:"HTML",id:"html",websites:[{name:"Hyperscript",url:"https://hyperscript.org/",description:"使用普通标记编写的网站变得令人愉快。",isPaid:!1,isStudentFriendly:!1},{name:"htmx",url:"https://htmx.org/",description:"htmx通过属性直接在HTML中为您提供AJAX、CSS过渡、WebSockets和服务器发送事件的访问权限，因此您可以利用超文本的简洁性和强大功能构建现代用户界面。",isPaid:!1,isStudentFriendly:!1}]},{name:"CSS",originalName:"CSS",id:"css",websites:[{name:"CSS Solid",url:"https://www.csssolid.com/",description:"CSS参考、教程和文章。",isPaid:!1,isStudentFriendly:!1},{name:"CSS-Tricks",url:"https://css-tricks.com/",description:"由Digital Ocean支持的Web设计社区。",isPaid:!1,isStudentFriendly:!1},{name:"CSS Box Shadow Code Snippet",url:"https://onaircode.com/css-box-shadow-code-snippet/",description:"创建CSS盒子阴影的代码片段。",isPaid:!1,isStudentFriendly:!1}]},{name:"JavaScript",originalName:"JavaScript",id:"javascript",websites:[{name:"The Coding Cards",url:"https://thecodingcards.com/",description:"JavaScript和数据结构抽认卡。包含语法和示例的基本编程概念。",isPaid:!0,isStudentFriendly:!1},{name:"StandardJS",url:"https://standardjs.com/",description:"JavaScript风格指南、代码检查器和格式化程序。",isPaid:!1,isStudentFriendly:!1},{name:"Modern JavaScript Cheatsheet",url:"https://mbeaudru.github.io/modern-js-cheatsheet/",description:"现代项目中经常遇到的JavaScript知识速查表。",isPaid:!1,isStudentFriendly:!1},{name:"JSONPlaceholder",url:"https://jsonplaceholder.typicode.com/",description:"用于测试和原型设计的免费虚假API。",isPaid:!1,isStudentFriendly:!1},{name:"Vue.js Cheat Sheet",url:"https://marozed.com/vue-cheatsheet/",description:"Vue.js速查表。",isPaid:!1,isStudentFriendly:!1},{name:"JS Bin",url:"https://jsbin.com/",description:"开源协作Web开发调试工具。",isPaid:!1,isStudentFriendly:!1},{name:"JavaScript Event Keycode Info",url:"https://www.toptal.com/developers/keycode",description:"JavaScript事件键码信息。",isPaid:!1,isStudentFriendly:!1},{name:"jsDelivr",url:"https://www.jsdelivr.com/",description:"开源免费CDN。快速、可靠且自动化。",isPaid:!1,isStudentFriendly:!1}]},{name:"后端开发",originalName:"Back-End",id:"back-end",websites:[{name:"RunSidekick",url:"https://www.runsidekick.com/",description:"无需停止和重新部署应用程序即可按需收集跟踪和生成日志。",isPaid:!1,isStudentFriendly:!1},{name:"Vantage Instances",url:"https://instances.vantage.sh/",description:"比较亚马逊的实例类型、定价和其他页面。",isPaid:!1,isStudentFriendly:!1}]},{name:"API",originalName:"APIs",id:"apis",websites:[{name:"Public APIs Directory",url:"https://publicapis.dev/",description:"发现公共API",isPaid:!1,isStudentFriendly:!1},{name:"Public APIs on GitHub",url:"https://github.com/public-apis/public-apis",description:"供软件和Web开发使用的免费API集合列表",isPaid:!1,isStudentFriendly:!1},{name:"REST API Tutorial",url:"https://www.restapitutorial.com/",description:"学习REST",isPaid:!1,isStudentFriendly:!1},{name:"Spotify API Documentation",url:"https://developer.spotify.com/documentation/web-api",description:"Spotify的API文档",isPaid:!1,isStudentFriendly:!1},{name:"Stripe API Documentation",url:"https://stripe.com/docs/api",description:"Stripe的API文档",isPaid:!1,isStudentFriendly:!1}]},{name:"SQL",originalName:"SQL",id:"sql",websites:[{name:"SQLZoo",url:"https://sqlzoo.net/wiki/SQL_Tutorial",description:"分阶段学习SQL",isPaid:!1,isStudentFriendly:!1},{name:"Bipp SQL Tutorial",url:"https://bipp.io/sql-tutorial",description:"免费SQL教程",isPaid:!1,isStudentFriendly:!1},{name:"Select Star SQL",url:"https://selectstarsql.com/",description:"学习SQL的交互式书籍",isPaid:!1,isStudentFriendly:!1},{name:"Medium-Hard Data Analyst SQL Interview Questions",url:"https://quip.com/2gwZArKuWk7W",description:"SQL面试题集合",isPaid:!1,isStudentFriendly:!1},{name:"SQL Translate",url:"https://www.sqltranslate.app/",description:"SQL到自然语言和自然语言到SQL的翻译器。100%免费和开源",isPaid:!1,isStudentFriendly:!1},{name:"Servers for Hackers",url:"https://serversforhackers.com/",description:"程序员的服务器管理。学习开发和生产的服务器技术。",isPaid:!1,isStudentFriendly:!1}]},{name:"网站分析",originalName:"Web Analytics",id:"web-analytics",websites:[{name:"Pirsch",url:"https://pirsch.io/",description:"开源、无Cookie的网络分析平台。",isPaid:!1,isStudentFriendly:!1},{name:"Websites Milonic",url:"https://websites.milonic.com/",description:"为网站提供整洁报告的重要数据。",isPaid:!1,isStudentFriendly:!1},{name:"WooRank",url:"https://www.woorank.com/",description:"网站评估和SEO检查器。",isPaid:!0,isStudentFriendly:!1},{name:"Semji",url:"https://semji.com/",description:"通过创建高性能SEO内容来提高内容投资回报率的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Web.dev",url:"https://web.dev/",description:"允许您测量网站性能并提供可操作的见解。",isPaid:!1,isStudentFriendly:!1},{name:"Download Time Calculator",url:"https://downloadtimecalculator.com/",description:"根据您的传输速度估算下载任何文件所需的时间，而无需实际下载文件。",isPaid:!1,isStudentFriendly:!1},{name:"W3C Link Checker",url:"https://validator.w3.org/checklink",description:"检查网页或完整网站中的链接和锚点。",isPaid:!1,isStudentFriendly:!1},{name:"URLVoid",url:"https://www.urlvoid.com/",description:"网站声誉/安全检查器，帮助检测潜在恶意网站。",isPaid:!1,isStudentFriendly:!1},{name:"SimilarSites",url:"https://www.similarsites.com/",description:"帮助找到相似的网站并提供有关其统计信息的信息。",isPaid:!1,isStudentFriendly:!1},{name:"LocaBrowser",url:"https://www.locabrowser.com/",description:"实时测试您的网站在不同国家的外观。",isPaid:!1,isStudentFriendly:!1},{name:"StatusVista",url:"https://statusvista.com/",description:"您的产品所依赖系统的一体化状态页面。",isPaid:!1,isStudentFriendly:!1},{name:"BuiltWith",url:"https://builtwith.com/",description:"发现网站使用的技术，使用包含59,905+种网络技术和超过6.73亿个网站的数据库。",isPaid:!1,isStudentFriendly:!1},{name:"CamelCamelCamel",url:"https://camelcamelcamel.com/",description:"免费的亚马逊价格追踪器，监控数百万种产品并在价格下降时提醒您。",isPaid:!1,isStudentFriendly:!1},{name:"CloudPing",url:"https://cloudping.bastionhost.org/en/",description:"执行HTTP ping以测量从您的浏览器到世界各地各种云数据中心的网络延迟。",isPaid:!1,isStudentFriendly:!1},{name:"DownForEveryoneOrJustMe",url:"https://downforeveryoneorjustme.com/",description:"检查网站是否宕机。",isPaid:!1,isStudentFriendly:!1},{name:"DownDetector",url:"https://downdetector.in/",description:"实时问题和中断监控。",isPaid:!1,isStudentFriendly:!1},{name:"WebhookWizard",url:"https://webhookwizard.com/",description:"使用webhooks解锁数据。",isPaid:!1,isStudentFriendly:!1},{name:"Lighthouse",url:"https://chrome.google.com/webstore/detail/lighthouse/blipmdconlkpinefehnmjammfjpmpbjk?hl=en",description:"用于提高Web应用程序性能、质量和正确性的开源自动化工具。",isPaid:!1,isStudentFriendly:!1},{name:"Archive.md",url:"https://archive.md/",description:"网页的时间胶囊。",isPaid:!1,isStudentFriendly:!1},{name:"Hypothesis",url:"https://web.hypothes.is/",description:"覆盖整个网络的对话层，在任何地方都能工作，无需任何底层站点实施（开放社区项目）。",isPaid:!1,isStudentFriendly:!1}]},{name:"测试",originalName:"Testing",id:"testing",websites:[{name:"Fast.com",url:"https://fast.com/",description:"测量您的互联网速度、延迟和上传速度。",isPaid:!1,isStudentFriendly:!1},{name:"Pingdom Tools",url:"https://tools.pingdom.com/",description:"允许您测试页面加载时间、分析并找到瓶颈。",isPaid:!0,isStudentFriendly:!1},{name:"GTmetrix",url:"https://gtmetrix.com/",description:"分析您的网站性能，识别缓慢的原因，并发现优化机会。",isPaid:!1,isStudentFriendly:!1},{name:"Loader.io",url:"https://loader.io/",description:"免费负载测试服务，用数千个并发连接对Web应用程序和API进行压力测试。",isPaid:!1,isStudentFriendly:!1},{name:"WebPageTest",url:"https://www.webpagetest.org/",description:"测量您网站的碳足迹并运行无代码实验以找到改进方法。",isPaid:!1,isStudentFriendly:!1},{name:"Azure Speed Test",url:"https://www.azurespeed.com/Azure/Latency",description:"测试Azure服务速度和延迟的工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"Web3加密货币",originalName:"Web 3.0 Dev and Cryptocurrencies",id:"web-3-0-dev-and-cryptocurrencies",websites:[{name:"Web3 is Going Great",url:"https://web3isgoinggreat.com/",description:"追踪区块链/加密货币/web3技术领域挑战的例子。时间轴涵盖了自2021年初以来加密货币和基于区块链技术的事件。",isPaid:!1,isStudentFriendly:!1},{name:"Blockchain Demo",url:"https://andersbrownworth.com/blockchain/coinbase",description:"区块链工作原理的演示。",isPaid:!1,isStudentFriendly:!1},{name:"DappRadar",url:"https://dappradar.com/",description:"在DeFi、NFT和游戏世界中发现、追踪和交易。",isPaid:!1,isStudentFriendly:!1},{name:"DeFi Pulse",url:"https://www.defipulse.com/",description:"去中心化金融（DeFi）项目及其统计数据的列表。",isPaid:!1,isStudentFriendly:!1},{name:"GlassChain",url:"https://glasschain.org/home",description:"总部位于瑞士的非营利组织，检查地址、交易、钱包或提供商，以追回因犯罪行为而被盗或丢失的比特币（或其中一部分）。比特币、莱特币、比特币现金和狗狗币地址被聚类到基于UTXO的区块链钱包中。提供实时、可靠且100%正确的数据。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"Mozilla Developer Network",url:"https://developer.mozilla.org/en-US/",description:"Web技术文档，包括CSS、HTML和JavaScript。",isPaid:!1,isStudentFriendly:!1},{name:"W3C",url:"https://www.w3.org/",description:"万维网联盟官方网站，使Web技术能够与不同语言、脚本和文化一起使用。",isPaid:!1,isStudentFriendly:!1},{name:"UX Core",url:"https://keepsimple.io/uxcore",description:"UX Core允许您在创建软件时探索许多认知偏见。",isPaid:!1,isStudentFriendly:!1},{name:"Coggle",url:"https://coggle.it/diagram/Vz9LvW8byvN0I38x/t/web-development",description:"用于可视化和组织Web开发相关想法的协作思维导图工具。",isPaid:!1,isStudentFriendly:!1},{name:"Web.dev Learn",url:"https://web.dev/learn",description:"Google提供的Web开发资源和教程教育平台。",isPaid:!1,isStudentFriendly:!1},{name:"WebDevHome",url:"https://webdevhome.github.io/",description:"Web开发资源和教程集合。",isPaid:!1,isStudentFriendly:!1},{name:"Web Dev Resources",url:"https://web-dev-resources.com/#/",description:"为开发者精选的出色Web开发资源列表。",isPaid:!1,isStudentFriendly:!1},{name:"BBC Microbit Editor",url:"https://bbcmic.ro/",description:"BBC提供的Microbit编程基础编辑器。",isPaid:!1,isStudentFriendly:!1},{name:"DemoFox",url:"https://blog.demofox.org/",description:"包含与编程和开发相关的各种链接的博客。",isPaid:!1,isStudentFriendly:!1},{name:"Timeline of Web Browsers",url:"https://super-static-assets.s3.amazonaws.com/bc2689f0-a124-4777-93dc-416ee1aa4858/images/7db6532c-14f1-4c51-a337-b3021a7293bf.svg",description:"2019年前Web浏览器时间轴的可视化。",isPaid:!1,isStudentFriendly:!1},{name:"Is Houdini Ready Yet?",url:"https://ishoudinireadyyet.com/",description:"检查Web浏览器中各种Houdini规范就绪状态的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Artillery",url:"https://www.artillery.io/",description:"用于SRE（站点可靠性工程）和DevOps的现代负载测试和冒烟测试工具。",isPaid:!1,isStudentFriendly:!1},{name:"Rentry",url:"https://rentry.org/",description:"具有预览、自定义URL和编辑功能的Markdown粘贴板服务，提供快速、简单和免费使用，条目永久保存。",isPaid:!1,isStudentFriendly:!1},{name:"Intab Resources",url:"https://intab.io/resources/",description:"精选的2021年Web开发工具。",isPaid:!1,isStudentFriendly:!1},{name:"Chrome Extension Kit",url:"https://chromeextensionkit.com/",description:"包含17个经过实战检验的Chrome扩展构建入门模板的工具包，节省设置时间并专注于交付。",isPaid:!0,isStudentFriendly:!1},{name:"Esoteric Codes",url:"https://esoteric.codes/",description:"探索深奥编程语言、基于约束的编码、代码艺术、代码诗歌等的平台。",isPaid:!1,isStudentFriendly:!1},{name:"FreeFormatter",url:"https://freeformatter.com/",description:"为开发者提供的免费在线工具，包括格式化器、验证器、代码压缩器、字符串转义器、编码器/解码器、消息摘要器等。",isPaid:!1,isStudentFriendly:!1},{name:"dbdesigner.net",url:"https://www.dbdesigner.net/",description:"免费的在线数据库模式设计和建模工具。",isPaid:!1,isStudentFriendly:!1},{name:"Jvns blog",url:"https://jvns.ca/blog/2022/04/12/a-list-of-new-ish--command-line-tools/>",description:"新命令行工具列表",isPaid:!1,isStudentFriendly:!1},{name:"VisBug",url:"https://visbug.web.app/",description:"用于Web开发的开源浏览器设计工具。",isPaid:!1,isStudentFriendly:!1},{name:"Web Design Repo",url:"https://webdesignrepo.com/",description:"为设计师和开发者提供的免费Web设计资源库。",isPaid:!1,isStudentFriendly:!1},{name:"Devopedia",url:"https://devopedia.org/",description:"涵盖技术和软件开发各种主题的知识中心。",isPaid:!1,isStudentFriendly:!1},{name:"MadeWithBubble",url:"https://www.madewithbubble.xyz/",description:"发现用Bubble（可视化Web开发平台）创建的有趣应用程序和网站的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Interneting is Hard",url:"https://www.internetingishard.com/",description:"为完全初学者设计的友好Web开发教程。",isPaid:!1,isStudentFriendly:!1},{name:"Airtable Toolkit",url:"https://www.airtable.com/home/<USER>",description:"允许用户构建连接工作不同部分的应用程序平台，确保业务内的同步。",isPaid:!1,isStudentFriendly:!1},{name:"Grey Software Resources",url:"https://resources.grey.software/",description:"由全球专业人士和学者策划和众包的最新软件资源。",isPaid:!1,isStudentFriendly:!1},{name:"Free-for.dev",url:"https://free-for.dev/#/",description:"为开发者提供免费套餐的软件（SaaS、PaaS、IaaS等）和其他服务列表。",isPaid:!1,isStudentFriendly:!1},{name:"Free Privacy Policy Generator",url:"https://www.freeprivacypolicy.com/",description:"生成免费隐私政策的工具，确保符合CCPA、GDPR、CalOPPA、Google Analytics和AdSense等要求。",isPaid:!1,isStudentFriendly:!1},{name:"Mailinator",url:"https://www.mailinator.com/",description:"允许开发者和QA测试团队测试电子邮件和短信工作流程的平台，包括2FA验证、注册和密码重置。",isPaid:!0,isStudentFriendly:!1},{name:"Atlaq",url:"https://atlaq.com/",description:"域名生成器。",isPaid:!1,isStudentFriendly:!1},{name:"Addy's Toolkit",url:"https://toolkit.addy.codes/",description:"为Web设计师和开发者精选的806个工具和资源集合。",isPaid:!1,isStudentFriendly:!1},{name:"BrowserBench - Speedometer 3.0",url:"https://www.browserbench.org/Speedometer3.0/",description:"测量Web浏览器性能的基准测试工具，专门测试现代Web应用程序的响应性和速度。",isPaid:!1,isStudentFriendly:!1},{name:"UserAgents.me",url:"https://www.useragents.me/",description:"提供在Web上所有设备类型、操作系统和浏览器中最新和最常见用户代理的自更新列表。数据始终新鲜，每周更新。此用户代理列表非常适合希望融入的网络爬虫、开发者、网站管理员和研究人员。",isPaid:!1,isStudentFriendly:!1}]},{name:"软件工程",originalName:"Software Engineering",id:"software-engineering",subcategories:[{name:"Android开发",originalName:"Android Development",id:"android-development",websites:[{name:"Fossdroid",url:"https://fossdroid.com/",description:"发现和分享开源Android应用的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Android Weekly",url:"https://androidweekly.net/",description:"与Android开发相关的精选新闻、文章和资源的每周通讯。",isPaid:!1,isStudentFriendly:!1},{name:"F-Droid Repository Search",url:"https://apt.izzysoft.de/fdroid/index.php",description:"允许您搜索F-Droid存储库中可用的应用程序，这是一个免费开源Android应用程序的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Exodus Privacy",url:"https://reports.exodus-privacy.eu.org/en/",description:"帮助检查Android应用的权限和跟踪器，提供有关不同应用程序隐私方面的见解。",isPaid:!1,isStudentFriendly:!1},{name:"Don't Kill My App",url:"https://dontkillmyapp.com/",description:"倡导反对Android上激进的应用后台进程限制的网站，这可能会负面影响应用性能。",isPaid:!1,isStudentFriendly:!1},{name:"Mobile X Files",url:"https://mobilexfiles.com/",description:"智能手机的秘密代码和其他隐藏功能。",isPaid:!1,isStudentFriendly:!1},{name:"MobileRead Wiki",url:"https://wiki.mobileread.com/wiki/Main_Page",description:"电子阅读器、电子书和相关技术的综合资源，为各种移动阅读设备的用户提供指南、常见问题解答和教程。",isPaid:!1,isStudentFriendly:!1}]},{name:"游戏开发",originalName:"Game Development",id:"game-development",websites:[{name:"itch.io",url:"https://itch.io/",description:"提供免费在线查找和分享独立游戏的简单方式的平台。",isPaid:!1,isStudentFriendly:!1},{name:"System Requirements Lab",url:"https://www.systemrequirementslab.com/cyri",description:"在几秒钟内分析您的计算机规格，提供您的系统是否满足各种游戏要求的信息。",isPaid:!1,isStudentFriendly:!1},{name:"Kenney",url:"https://kenney.nl/",description:"提供免费游戏资产、图形和游戏开发资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Flash Games Archive",url:"https://flasharch.com/en",description:"Flash游戏档案，保存这些经典游戏供未来享受。",isPaid:!1,isStudentFriendly:!1},{name:"OpenGameArt",url:"https://opengameart.org/",description:"为开发者和游戏创作者提供免费使用游戏资产的平台，包括2D和3D艺术、音效和音乐。",isPaid:!1,isStudentFriendly:!1},{name:"Nexus Mods",url:"https://www.nexusmods.com/",description:"最大的游戏修改在线社区之一，为流行视频游戏提供大量mod集合，以增强游戏体验、添加内容或个性化体验。",isPaid:!1,isStudentFriendly:!1}]},{name:"游戏理论",originalName:"Game Theory",id:"game-theory",websites:[{name:"Ncase",url:"https://ncase.me/",description:"通过简单游戏解释博弈论。",isPaid:!1,isStudentFriendly:!1},{name:"Alberta Games Research",url:"https://webdocs.cs.ualberta.ca/~games/",description:"阿尔伯塔大学的网站，提供与博弈论、计算模型和作为计算研究的游戏相关的资源和研究。",isPaid:!1,isStudentFriendly:!1},{name:"Combinatorics.org",url:"https://www.combinatorics.org/",description:"A. S. Fraenkel的组合博弈论文献目录。",isPaid:!1,isStudentFriendly:!1},{name:"Geometry and Graph Theory",url:"https://ics.uci.edu/~eppstein/cgt/",description:"组合博弈论资源，包含解释、示例和组合问题算法研究。",isPaid:!1,isStudentFriendly:!1}]},{name:"宝可梦",originalName:"Pokemon",id:"pokemon",websites:[{name:"Pokemon Database",url:"https://pokemondb.net/",description:"包含新闻和更新的综合宝可梦数据库。",isPaid:!1,isStudentFriendly:!1},{name:"Serebii",url:"https://serebii.net/",description:"提供有关宝可梦的各种新闻、功能、档案和解释。",isPaid:!1,isStudentFriendly:!1}]},{name:"国际象棋",originalName:"Chess",id:"chess",websites:[{name:"The Chess Website",url:"https://www.thechesswebsite.com/",description:"学习、练习和下国际象棋。",isPaid:!1,isStudentFriendly:!1},{name:"Lichess",url:"https://en.lichess.org/",description:"由志愿者和捐赠支持的免费/自由开源国际象棋服务器。",isPaid:!1,isStudentFriendly:!1},{name:"Chess.com",url:"https://www.chess.com/",description:"在线国际象棋平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"嵌入",originalName:"Embeddings",id:"embeddings",websites:[{name:"FPGA4Fun",url:"https://www.fpga4fun.com/",description:"专注于FPGA（现场可编程门阵列）技术的教育资源和教程，为初学者和高级用户提供实用指南。",isPaid:!1,isStudentFriendly:!1},{name:"Wokwi",url:"https://wokwi.com/",description:"在线ESP32、STM32、Arduino模拟器。",isPaid:!1,isStudentFriendly:!1},{name:"x86 Instruction Set Reference",url:"https://c9x.me/x86/",description:'x86指令集"Into the Void"参考的镜像，为x86架构提供汇编语言和处理器指令的详细指南。',isPaid:!1,isStudentFriendly:!1},{name:"Analog Devices Wiki",url:"https://wiki.analog.com/",description:"包含模拟和混合信号设备技术文档、教程和资源的知识库，面向工程师和开发人员。",isPaid:!1,isStudentFriendly:!1},{name:"IEEE-754 Floating Point Converter",url:"https://www.h-schmidt.net/FloatConverter/IEEE754.html",description:"基于IEEE-754标准，在数字的十进制表示和现代CPU使用的二进制格式之间转换的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Guide to x86 Assembly",url:"https://www.cs.virginia.edu/~evans/cs216/guides/x86.html",description:"弗吉尼亚大学计算机科学的x86汇编指南",isPaid:!1,isStudentFriendly:!1}]},{name:"Linux",originalName:"Linux",id:"linux",websites:[{name:"Linux Journey",url:"https://linuxjourney.com/",description:"学习Linux的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Run Linux in Your Browser",url:"https://bellard.org/jslinux/",description:"在浏览器中运行Linux或其他操作系统。",isPaid:!1,isStudentFriendly:!1},{name:"OS Directory",url:"https://os.directory/",description:"在Web浏览器中模拟Linux发行版的平台。",isPaid:!1,isStudentFriendly:!1},{name:"DistroWatch",url:"https://distrowatch.com/",description:"Linux和BSD发行版资源，提供新闻、评论和比较，帮助用户找到并安装合适的操作系统。",isPaid:!1,isStudentFriendly:!1},{name:"SS64",url:"https://ss64.com/",description:"包含最常见计算命令语法和示例的参考指南，涵盖数据库和操作系统。",isPaid:!1,isStudentFriendly:!1},{name:"SS64 Bash Keyboard Shortcuts",url:"https://ss64.com/bash/syntax-keyboard.html",description:"bash键盘快捷键参考指南。",isPaid:!1,isStudentFriendly:!1},{name:"Command Line for Beginners",url:"https://ubuntu.com/tutorials/command-line-for-beginners#1-overview",description:"面向初学者的Linux命令行概述。",isPaid:!1,isStudentFriendly:!1},{name:"Linux Journey",url:"https://linuxjourney.com/",description:"提供免费、交互式Linux课程的教育网站，涵盖从基本命令到高级系统管理的所有内容。",isPaid:!1,isStudentFriendly:!1},{name:"ExplainShell",url:"https://explainshell.com/",description:"解释Linux shell命令的工具，提供命令语法和功能的详细分解。",isPaid:!1,isStudentFriendly:!1},{name:"LibreHunt",url:"https://librehunt.org/",description:"在您的Linux发行版（以及潜在的自由软件）搜索中为您提供帮助。回答简单问题，根据这些回答获得满足您需求的Linux发行版推荐。",isPaid:!1,isStudentFriendly:!1}]},{name:"Vim",originalName:"Vim",id:"vim",websites:[{name:"Vim Cheat Sheet",url:"https://vim.rtorr.com/",description:"Vim速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Learn Vim",url:"https://learnvim.irian.to/",description:"智能学习Vim的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Vim Awesome",url:"https://vimawesome.com/",description:"搜索列出的Vim插件",isPaid:!1,isStudentFriendly:!1},{name:"Vim Adventures",url:"https://vim-adventures.com/",description:"一个交互式游戏，旨在通过引人入胜的谜题和挑战教用户Vim文本编辑器的基础知识。",isPaid:!1,isStudentFriendly:!1},{name:"Vimified",url:"https://www.vimified.com/",description:"学习和练习Vim的平台，一个免费开源的基于屏幕的文本编辑器程序。",isPaid:!1,isStudentFriendly:!1}]},{name:"Git",originalName:"Git",id:"git",websites:[{name:"Git - The Simple Guide",url:"https://rogerdudler.github.io/git-guide/",description:"Git入门的直接指南。",isPaid:!1,isStudentFriendly:!1},{name:"Git Sheet",url:"https://gitsheet.wtf",description:"常见Git命令的快速参考指南。",isPaid:!1,isStudentFriendly:!1},{name:"MiXLab on Google Colab",url:"https://colab.research.google.com/github/shirooo39/MiXLab/blob/master/MiXLab.ipynb#scrollTo=e-0yDs4C0HkB",description:"从GitHub编译的Google Colab笔记本的混合。",isPaid:!1,isStudentFriendly:!1},{name:"Learn Git Branching",url:"https://learngitbranching.js.org/",description:"通过模拟git存储库学习Git分支的交互式工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"GitHub",originalName:"GitHub",id:"github",websites:[{name:"GitStalk",url:"https://gitstalk.netlify.app/",description:"发现GitHub社区中个人正在做什么的平台。",isPaid:!1,isStudentFriendly:!1},{name:"GitExplorer",url:"https://gitexplorer.com/",description:"无需在网络中搜索即可找到正确Git命令的工具。",isPaid:!1,isStudentFriendly:!1},{name:"GitStats",url:"https://gitstats.me/",description:"开源GitHub贡献分析器，提供对您的GitHub活动的见解。",isPaid:!1,isStudentFriendly:!1},{name:"Gitcoin",url:"https://gitcoin.co/",description:"个人可以通过为各种编程语言和领域的开源软件做贡献而获得报酬的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Oh Shit, Git!",url:"https://ohshitgit.com/",description:"为常见Git错误提供解决方案以及如何从中恢复的资源。",isPaid:!1,isStudentFriendly:!1},{name:"GitHub Trending Archive",url:"https://github.motakasoft.com/trending/",description:"GitHub上趋势存储库的档案，允许用户随时间探索流行项目和编程趋势。",isPaid:!1,isStudentFriendly:!1},{name:"Map of GitHub",url:"https://anvaka.github.io/map-of-github/",description:"GitHub存储库网络的可视化表示。",isPaid:!1,isStudentFriendly:!1}]},{name:"集成开发环境",originalName:"IDEs",id:"ides",websites:[{name:"OneLang IDE",url:"https://ide.onelang.io/",description:"将代码从一种编程语言转换为另一种的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Theia IDE",url:"https://theia-ide.org/",description:"灵活可扩展的云和桌面IDE平台，使用现代Web技术实现IDE和工具的高效开发和交付。",isPaid:!1,isStudentFriendly:!1},{name:"VSCode Themes",url:"https://vscodethemes.com/",description:"为Visual Studio Code提供各种主题的平台，允许用户通过不同的配色方案和样式个性化其编码环境的外观和感觉。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"NoviceDock",url:"https://novicedock.com/",description:"提供各种软件工程领域资源和解释的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Morioh",url:"https://morioh.com/",description:"开发者社交网络，讨论bug和问题，分享知识并连接全球数百万程序员和开发者的才能。",isPaid:!1,isStudentFriendly:!1},{name:"Algorithmica - High-Performance Computing",url:"https://en.algorithmica.org/hpc/",description:"Sergey Slotin的《现代硬件算法》。",isPaid:!1,isStudentFriendly:!1},{name:"ExBook",url:"https://markm208.github.io/exbook/",description:"Elixir的动画介绍。",isPaid:!1,isStudentFriendly:!1},{name:"Ben Grosser Projects",url:"https://bengrosser.com/projects/",description:"Ben Grosser的项目作品集。",isPaid:!1,isStudentFriendly:!1},{name:"Cybercademy Project Ideas",url:"https://cybercademy.org/project-ideas/",description:"探索网络安全项目想法的资源，为教育或实际网络安全倡议提供灵感。",isPaid:!1,isStudentFriendly:!1}]},{name:"隐私安全",originalName:"Privacy",id:"privacy",subcategories:[{name:"密码学",originalName:"Cryptography",id:"cryptography",websites:[{name:"Cryptologie",url:"https://www.cryptologie.net/",description:"涵盖密码学各种主题的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Invisible",url:"https://mikebradley.me/invisible/index.html",description:"在图像中隐藏文本并允许您查找隐藏文本的工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"GAFA替代品",originalName:"GAFA Alternatives",id:"gafa-alternatives",websites:[{name:"DeGoogle",url:"https://degoogle.jmoore.dev/#mobile-applications-mobile-apps-installable-from-stores",description:"Google产品替代方案的庞大列表。隐私提示、技巧和链接。",isPaid:!1,isStudentFriendly:!1},{name:"Degooglisons Internet",url:"https://degooglisons-internet.org/en/",description:"FAANG的替代方案。",isPaid:!1,isStudentFriendly:!1},{name:"AccountKiller",url:"https://www.accountkiller.com/en/home",description:"AccountKiller收集直接链接和删除说明，使账户终止变得容易。",isPaid:!1,isStudentFriendly:!1},{name:"JustDeleteMe",url:"https://backgroundchecks.org/justdeleteme/",description:"从Web服务删除您的账户的直接链接目录。",isPaid:!1,isStudentFriendly:!1},{name:"SmartTubeNext",url:"https://github.com/yuliskov/SmartTubeNext",description:"在Android TV盒子上观看YouTube视频的无广告应用。",isPaid:!1,isStudentFriendly:!1},{name:"Libredirect",url:"https://libredirect.github.io/",description:"将YouTube、Twitter、Instagram请求重定向到隐私友好的替代前端和后端的Web扩展。",isPaid:!1,isStudentFriendly:!1}]},{name:"广告拦截",originalName:"Ad Blocker",id:"ad-blocker",websites:[{name:"Adblock Tester",url:"https://adblock-tester.com/",description:"检查AdBlock扩展有效性的工具。",isPaid:!1,isStudentFriendly:!1},{name:"12ft",url:"https://12ft.io/",description:"移除付费墙并获得文章访问权限。",isPaid:!1,isStudentFriendly:!1},{name:"Incoggo",url:"https://incoggo.com/",description:"针对付费墙的广告拦截器。",isPaid:!1,isStudentFriendly:!1}]},{name:"邮箱",originalName:"Emails",id:"emails",websites:[{name:"BurnerMail",url:"https://burnermail.io/",description:"保护您的个人电子邮件地址，控制谁可以向您发送电子邮件，一键生成新的临时地址。",isPaid:!1,isStudentFriendly:!1},{name:"Kill the Newsletter",url:"https://kill-the-newsletter.com/",description:"将电子邮件通讯转换为Atom提要以便于消费。",isPaid:!1,isStudentFriendly:!1},{name:"Dead Man's Switch",url:"https://www.deadmansswitch.net/",description:"如果您在指定时间内不与其交互，则将您的电子邮件发送给指定收件人的服务。",isPaid:!1,isStudentFriendly:!1},{name:"YAMM",url:"https://yamm.com/",description:"Gmail的邮件合并，允许您使用Gmail发送大量邮件，确保投递到主收件箱。直接从Google Sheets实时跟踪结果。",isPaid:!0,isStudentFriendly:!1}]},{name:"临时邮箱",originalName:"Disposable Email",id:"disposable-email",websites:[{name:"Erine Email",url:"https://erine.email/",description:"为您现有电子邮件地址提供的反垃圾邮件服务。",isPaid:!1,isStudentFriendly:!1},{name:"Maildrop",url:"https://maildrop.cc/",description:"当您不想透露真实电子邮件地址时使用Maildrop。",isPaid:!1,isStudentFriendly:!1},{name:"Mailsac",url:"https://mailsac.com/",description:"为测试和开发目的提供临时电子邮件服务。",isPaid:!1,isStudentFriendly:!1},{name:"InboxKitten",url:"https://inboxkitten.com/",description:"开源的临时电子邮件服务。",isPaid:!1,isStudentFriendly:!1},{name:"Guerrilla Mail",url:"https://www.guerrillamail.com/inbox",description:"允许您撰写电子邮件并确定临时电子邮件的域名、主密码短语和密码。",isPaid:!1,isStudentFriendly:!1},{name:"EmailDrop",url:"https://www.emaildrop.io/",description:"以高度简约的设计创建具有自定义或随机域名的电子邮件。",isPaid:!1,isStudentFriendly:!1},{name:"GetNada",url:"https://getnada.com/",description:"提供临时电子邮件地址。",isPaid:!1,isStudentFriendly:!1},{name:"GetNotify",url:"https://www.getnotify.com/",description:"免费的电子邮件跟踪服务，当您发送的电子邮件被阅读时通知您。",isPaid:!1,isStudentFriendly:!1},{name:"MXToolbox",url:"https://mxtoolbox.com/",description:"在一个地方提供免费的DNS和电子邮件工具，简化故障排除。",isPaid:!1,isStudentFriendly:!1},{name:"Postale",url:"https://postale.io/",description:"允许您在几分钟内轻松创建域名电子邮件地址。",isPaid:!0,isStudentFriendly:!1}]},{name:"数据泄露",originalName:"Data Breach",id:"data-breach",websites:[{name:"Have I Been Pwned",url:"https://haveibeenpwned.com/",description:"检查您的电子邮件或手机是否在数据泄露中。",isPaid:!1,isStudentFriendly:!1},{name:"TinEye",url:"https://tineye.com/",description:"查找图像在线出现的位置。",isPaid:!1,isStudentFriendly:!1},{name:"IPLeak",url:"https://ipleak.net/",description:"查看您访问的所有网站可以看到和收集的关于您的信息类型。",isPaid:!1,isStudentFriendly:!1},{name:"Cover Your Tracks",url:"https://coveryourtracks.eff.org/",description:"测试您的浏览器，查看您在跟踪和指纹识别方面的保护程度。",isPaid:!1,isStudentFriendly:!1},{name:"Am I FLoCed",url:"https://amifloced.org/",description:"检查Google是否在您的Chrome浏览器上测试FLoC。",isPaid:!1,isStudentFriendly:!1}]},{name:"搜索",originalName:"Search",id:"search",websites:[{name:"Startpage",url:"https://www.startpage.com/",description:"在整个网络上获得隐私保护。",isPaid:!1,isStudentFriendly:!1},{name:"Neeva",url:"https://neeva.com/",description:"无广告的私人搜索引擎，采用订阅模式。",isPaid:!1,isStudentFriendly:!1},{name:"AndiSearch",url:"https://andisearch.com/",description:"无广告和匿名搜索引擎。",isPaid:!1,isStudentFriendly:!1},{name:"Plex Page",url:"https://plex.page/",description:"搜索摘要工具。",isPaid:!1,isStudentFriendly:!1},{name:"Marginalia Search",url:"https://search.marginalia.nu/",description:"专注于非商业内容的独立DIY搜索引擎。",isPaid:!1,isStudentFriendly:!1},{name:"Unlisted Videos",url:"https://unlistedvideos.com/",description:"用于提交、搜索和观看未列出的YouTube视频。无需注册。",isPaid:!1,isStudentFriendly:!1},{name:"Filmot",url:"https://filmot.com/",description:"在YouTube字幕中搜索。",isPaid:!1,isStudentFriendly:!1},{name:"XN--1-ZFA",url:"https://xn--1-zfa.com/",description:"Google、DuckDuckGo、Twitter、YouTube、Reddit的高级搜索。",isPaid:!1,isStudentFriendly:!1},{name:"Seekr",url:"https://www.seekr.com/",description:"利用AI分析和评分内容质量的搜索引擎，从新闻文章开始。",isPaid:!1,isStudentFriendly:!1},{name:"Million Short",url:"https://millionshort.com/",description:"发现前百万搜索结果之外的内容。",isPaid:!1,isStudentFriendly:!1}]},{name:"互联网",originalName:"Internet",id:"internet",websites:[{name:"Internet Live Stats",url:"https://www.internetlivestats.com/",description:"提供有关互联网的实时统计信息，包括网站数量、电子邮件等。",isPaid:!1,isStudentFriendly:!1},{name:"Internet Map",url:"https://internet-map.net/",description:"可视化表示互联网的交互式地图，显示各种网站之间的关系。",isPaid:!1,isStudentFriendly:!1},{name:"Test IPv6",url:"https://test-ipv6.com/",description:"允许您测试IPv6连接性并提供有关网络设置的信息。",isPaid:!1,isStudentFriendly:!1},{name:"TLS 1.2 Explained",url:"https://tls12.xargs.org/",description:"提供TLS连接中每个字节的详细解释。",isPaid:!1,isStudentFriendly:!1},{name:"CIDR.xyz",url:"https://cidr.xyz/",description:"交互式IP地址和CIDR范围可视化工具。",isPaid:!1,isStudentFriendly:!1},{name:"I Can Haz IP",url:"https://icanhazip.com/",description:"在页面顶部仅显示您的IP地址。",isPaid:!1,isStudentFriendly:!1},{name:"IP Location",url:"https://iplocation.io/",description:"提供输入IP地址的免费位置跟踪，包括城市、国家、纬度和经度。",isPaid:!1,isStudentFriendly:!1},{name:"Ifconfig.co",url:"https://ifconfig.co/",description:"帮助您找到自己的IP地址并提供相关信息。",isPaid:!1,isStudentFriendly:!1},{name:"IPinfo.io",url:"https://ipinfo.io/",description:"为各种用例提供准确的IP地址数据。",isPaid:!1,isStudentFriendly:!1},{name:"Visual Subnet Calculator",url:"https://www.davidc.net/sites/default/subnets/subnets.html",description:"帮助进行IP地址和子网计算的可视化子网计算器。",isPaid:!1,isStudentFriendly:!1},{name:"Ping Test",url:"https://www.meter.net/ping-test/",description:"通过向指定服务器发送测试数据包（ping）来测量网络延迟的在线工具，提供连接稳定性和速度的见解。",isPaid:!1,isStudentFriendly:!1},{name:"LibreSpeed",url:"https://librespeed.org/",description:"开源互联网速度测试工具，提供下载、上传和延迟的准确测量，无需安装额外软件。",isPaid:!1,isStudentFriendly:!1}]},{name:"DNS",originalName:"DNS",id:"dns",websites:[{name:"How DNS Works",url:"https://howdns.works/",description:"通过漫画的帮助提供DNS工作原理的生动解释。",isPaid:!1,isStudentFriendly:!1},{name:"DNS Checker",url:"https://dnschecker.org/",description:"提供免费的DNS查找服务，检查域名系统记录对全球选定DNS服务器列表。",isPaid:!1,isStudentFriendly:!1},{name:"AdGuard DNS Providers",url:"https://kb.adguard.com/en/general/dns-providers",description:"DNS提供商列表，您可以配置使用以替代路由器或ISP提供的默认设置。",isPaid:!1,isStudentFriendly:!1},{name:"DNS Speed Test",url:"https://dnsspeedtest.online/",description:"快速DNS服务器速度测试工具，允许用户找到最佳DNS服务器以实现更快的互联网浏览，无需安装。",isPaid:!1,isStudentFriendly:!1}]},{name:"URL工具",originalName:"URL",id:"url",websites:[{name:"URL Tools",url:"https://shrtco.de/tools/",description:"各种URL工具。",isPaid:!1,isStudentFriendly:!1},{name:"OneLink",url:"https://www.onelink.to/",description:"为您的应用创建链接和二维码的平台。",isPaid:!1,isStudentFriendly:!1},{name:"QR Code Generator",url:"https://freecodetools.org/qr-code-generator/",description:"生成二维码的工具。",isPaid:!1,isStudentFriendly:!1},{name:"AppOpener",url:"https://appopener.com/",description:"创建智能链接以从URL打开所需应用，无需登录。",isPaid:!1,isStudentFriendly:!1},{name:"Who Is Hosting Any Domain",url:"https://digital.com/best-web-hosting/who-is/",description:"查找任何域名的托管商，包括网络主机、IP地址、名称服务器等。",isPaid:!1,isStudentFriendly:!1},{name:"Webhook.Site",url:"https://webhook.site/",description:"检查、测试和自动化任何传入HTTP请求或电子邮件的平台。",isPaid:!1,isStudentFriendly:!1},{name:"GoQR.Me",url:"https://goqr.me/",description:"生成适合打印的高分辨率二维码的生成器。",isPaid:!1,isStudentFriendly:!1},{name:"Open Graph Generator",url:"https://freecodetools.org/ogp/",description:"生成Open Graph图像的工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"短链接",originalName:"URL Shortener",id:"url-shortener",websites:[{name:"Rebrandly",url:"https://www.rebrandly.com/",description:"使用自定义域名品牌化、跟踪和分享短URL的链接管理平台。",isPaid:!1,isStudentFriendly:!1},{name:"Bit.do",url:"https://bit.do/",description:"免费为您的链接提供实时流量统计。",isPaid:!1,isStudentFriendly:!1},{name:"s.id",url:"https://home.s.id/",description:"链接缩短器和微网站构建器。",isPaid:!1,isStudentFriendly:!1},{name:"TinyURL",url:"https://tinyurl.com/app",description:"提供可选的短链接结尾。",isPaid:!1,isStudentFriendly:!1},{name:"Tiny.cc",url:"https://tiny.cc/",description:"提供可选的短链接结尾。",isPaid:!1,isStudentFriendly:!1},{name:"Bitly",url:"https://bitly.com/",description:"Web/移动链接管理和活动管理分析以及品牌链接。",isPaid:!1,isStudentFriendly:!1},{name:"iplogger.org",url:"https://iplogger.org/",description:"具有高级分析功能的URL缩短器，分析通过您的链接的流量、在线商店、博客或网站的访客。",isPaid:!1,isStudentFriendly:!1},{name:"cutr.ml",url:"https://cutr.ml/",description:"URL缩短器。",isPaid:!1,isStudentFriendly:!1},{name:"is.gd",url:"https://is.gd/",description:"URL缩短器。",isPaid:!1,isStudentFriendly:!1},{name:"gg.gg",url:"https://gg.gg/",description:"URL缩短器。",isPaid:!1,isStudentFriendly:!1},{name:"shrunken.com",url:"https://www.shrunken.com/",description:"URL缩短器。",isPaid:!1,isStudentFriendly:!1}]},{name:"VPN",originalName:"VPN",id:"vpn",websites:[{name:"Top10VPN",url:"https://www.top10vpn.com/",description:"提供各种VPN服务信息和评论的平台。",isPaid:!1,isStudentFriendly:!1},{name:"What's My Browser",url:"https://www.whatsmybrowser.org/",description:"检查您的浏览器详细信息，包括版本和插件。",isPaid:!1,isStudentFriendly:!1},{name:"VPN Comparison Spreadsheet",url:"https://docs.google.com/spreadsheets/d/1ijfqfLrJWLUVBfJZ_YalVpstWsjw-JGzkvMd6u2jqEk/edit#gid=231869418",description:"包含分析和比较的所有VPN详细列表。",isPaid:!1,isStudentFriendly:!1},{name:"Njalla",url:"https://njal.la/",description:"提供从您的计算机到互联网的加密隧道，将您的真实IP地址隐藏在他们的IP地址后面。",isPaid:!0,isStudentFriendly:!1}]},{name:"虚假信息生成",originalName:"Fake Information Generation",id:"fake-information-generation",websites:[{name:"Fake Name Generator",url:"https://www.fakenamegenerator.com/",description:"生成虚假个人资料信息，包括37种语言和31个国家的姓名、地址和电话号码。",isPaid:!1,isStudentFriendly:!1},{name:"Burner",url:"https://www.burnerapp.com/",description:"创建临时第二个电话号码用于通话和短信的应用，对隐私保护和避免垃圾信息有用。",isPaid:!1,isStudentFriendly:!1},{name:"Random.org",url:"https://www.random.org/",description:"生成真随机数、列表、字符串和映射的服务。",isPaid:!1,isStudentFriendly:!1}]},{name:"密码生成",originalName:"Password Generation",id:"password-generation",websites:[{name:"Everything About Passwords and Internet Security",url:"https://www.healthypasswords.com/index-2.html",description:"关于密码和互联网安全的综合信息。",isPaid:!1,isStudentFriendly:!1},{name:"Random Password Generator",url:"https://random-password-gen.web.app/",description:"生成随机和安全的密码。",isPaid:!1,isStudentFriendly:!1},{name:"How Secure Is My Password?",url:"https://www.security.org/how-secure-is-my-password/",description:"检查密码的安全强度。输入是安全的，不会被存储或分享。",isPaid:!1,isStudentFriendly:!1},{name:"Password Generator",url:"https://freecodetools.org/password-generator/",description:"轻松生成安全密码。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"ToS;DR",url:"https://tosdr.org/",description:"服务条款；没有阅读（简称：ToS;DR）。",isPaid:!1,isStudentFriendly:!1},{name:"Nothing Private",url:"https://www.nothingprivate.ml/",description:"检查为什么使用隐私浏览模式或无痕模式时您并不匿名。您也可以在[这里](https://github.com/gautamkrishnar/nothing-private)阅读。",isPaid:!1,isStudentFriendly:!1},{name:"TrustPage",url:"https://trustpage.com/directory",description:"查找和比较数千家公司的安全政策，根据从网络上获取的安全政策选择合适的软件和服务。",isPaid:!1,isStudentFriendly:!1},{name:"Arkenfox User.js",url:"https://github.com/arkenfox/user.js/wiki/4.1-Extensions",description:"隐私和安全相关的浏览器扩展。",isPaid:!1,isStudentFriendly:!1},{name:"Cookiepedia",url:"https://cookiepedia.co.uk/",description:"最大的预分类Cookie和在线跟踪技术数据库。",isPaid:!1,isStudentFriendly:!1},{name:"Security Planner",url:"https://securityplanner.consumerreports.org/",description:"获得定制建议以减少数据收集并防止黑客攻击。",isPaid:!1,isStudentFriendly:!1},{name:"MyWhisper",url:"https://mywhisper.net/",description:"基于AES密码学的文本加密工具。",isPaid:!1,isStudentFriendly:!1},{name:"BeEncrypted",url:"https://beencrypted.com/",description:"变得更加加密的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Prism Break",url:"https://prism-break.org/en/all/",description:"最常用应用程序和服务的私密安全替代方案。",isPaid:!1,isStudentFriendly:!1},{name:"Reddit Piracy Megathread",url:"https://www.reddit.com/r/piracy/wiki/megathread/tools",description:"应用程序、工具和Web服务列表。",isPaid:!1,isStudentFriendly:!1},{name:"Ressources",url:"https://gitlab.com/tzkuat/Ressources",description:"不同领域网站列表，如安全、OSINT等。",isPaid:!1,isStudentFriendly:!1},{name:"Privacy Tools List by CHEF-KOCH",url:"https://chef-koch.bearblog.dev/privacy-tools-list-by-chef-koch/",description:"CHEF-KOCH的综合隐私工具列表。",isPaid:!1,isStudentFriendly:!1},{name:"Privacy Analyzer",url:"https://privacy.net/analyzer/#pre-load",description:"分析网站的隐私设置。",isPaid:!1,isStudentFriendly:!1},{name:"Shut Up Trackers",url:"https://shutuptrackers.com/",description:"提供保护数据安全和隐私的信息。",isPaid:!1,isStudentFriendly:!1},{name:"EFF Surveillance Self-Defense",url:"https://ssd.eff.org/",description:"更安全在线通信的提示、工具和操作指南。",isPaid:!1,isStudentFriendly:!1},{name:"Router Passwords",url:"https://www.routerpasswords.com/",description:"互联网上最新的默认路由器密码库。",isPaid:!1,isStudentFriendly:!1},{name:"BugMeNot",url:"https://bugmenot.com/",description:"查找和分享各种网站的登录信息。",isPaid:!1,isStudentFriendly:!1},{name:"Security.org",url:"https://www.security.org/security-score/",description:"了解您的安全评分。",isPaid:!1,isStudentFriendly:!1},{name:"LibreProjects",url:"https://libreprojects.net/#favs=wikipedia,joindiaspora-com,nextcloud,openstreetmap,jamendo,plos",description:"118个开源托管Web服务列表。",isPaid:!1,isStudentFriendly:!1},{name:"Privnote",url:"https://privnote.com/",description:"发送阅读后会自毁的笔记。",isPaid:!1,isStudentFriendly:!1},{name:"Dangerzone",url:"https://dangerzone.rocks/",description:"将潜在不安全的PDF、Office文档或图像转换为安全可查看格式的平台。",isPaid:!1,isStudentFriendly:!1},{name:"ExifTool",url:"https://exiftool.org/",description:"读取、写入和编辑图像、音频和视频文件元数据的软件。",isPaid:!1,isStudentFriendly:!1},{name:"HideMyWeb",url:"https://hidemyweb.wordpress.com/",description:"隐藏、模糊和突出显示网页内容的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Browser.lol",url:"https://browser.lol/",description:"在浏览器内使用免费虚拟环境匿名浏览。",isPaid:!1,isStudentFriendly:!1},{name:"OneTimeSecret",url:"https://onetimesecret.com/",description:"使用一次性链接安全分享密码、秘密消息或私人链接的平台。",isPaid:!1,isStudentFriendly:!1},{name:"MetaDefender",url:"https://metadefender.opswat.com/",description:"扫描和分析文件安全威胁的在线工具，提供潜在风险的详细报告。",isPaid:!1,isStudentFriendly:!1},{name:"FotoForensics",url:"https://fotoforensics.com/",description:"分析和验证数字图像的在线工具，提供检测照片修改和编辑的取证工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"软件工具",originalName:"Softwares",id:"softwares",subcategories:[{name:"代码片段",originalName:"Snippets",id:"snippets",websites:[{name:"CodeClippet",url:"https://codeclippet.com/",description:"在以社区为中心的环境中分享代码片段的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Kod.so",url:"https://kod.so/",description:"创建可视化代码片段的平台，具有下载和分享功能。",isPaid:!1,isStudentFriendly:!1},{name:"CodePNG",url:"https://www.codepng.app/",description:"通过将代码转换为图像来从源代码创建图片的工具。",isPaid:!1,isStudentFriendly:!1},{name:"CodeMyUI",url:"https://codemyui.com/",description:"提供网页设计和UI灵感以及精选代码片段的网站。",isPaid:!1,isStudentFriendly:!1},{name:"30 Seconds of Code",url:"https://www.30secondsofcode.org/list/p/1",description:"为各种开发需求精选的短代码片段集合。",isPaid:!1,isStudentFriendly:!1},{name:"W3Schools HowTo",url:"https://www.w3schools.com/howto/default.asp",description:"W3Schools提供HTML、CSS和JavaScript代码片段的部分。",isPaid:!1,isStudentFriendly:!1},{name:"Snipplr",url:"https://snipplr.com/",description:"将常用代码片段保存在一个可访问位置的平台，允许用户分享和发现代码片段。",isPaid:!1,isStudentFriendly:!1},{name:"LittleSnippets",url:"https://littlesnippets.net/",description:"分享和发现小代码片段的平台。",isPaid:!1,isStudentFriendly:!1},{name:"CodeToGo",url:"https://codetogo.io/",description:"查找JavaScript和React用例的最新代码片段的资源。",isPaid:!1,isStudentFriendly:!1},{name:"TweetSnippet",url:"https://tweetsnippet.com/",description:"来自Twitter的技巧和窍门精选列表，以代码片段形式呈现。",isPaid:!1,isStudentFriendly:!1},{name:"CSS-Tricks Snippets",url:"https://css-tricks.com/snippets/",description:"涵盖各种设计和布局技术的CSS代码片段集合。",isPaid:!1,isStudentFriendly:!1},{name:"Crontab Guru",url:"https://crontab.guru/",description:"用于cron调度表达式的快速简单编辑器，提供人类可读的解释。",isPaid:!1,isStudentFriendly:!1},{name:"Crontab Generator",url:"https://crontab-generator.org/",description:"生成cron调度表达式的在线工具，具有用户友好的界面。",isPaid:!1,isStudentFriendly:!1},{name:"Carbon",url:"https://carbon.now.sh/",description:"创建和分享具有语法高亮的源代码片段美丽图像的基于Web的工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"代码检查",originalName:"Linters",id:"linters",websites:[{name:"FromLatest.io",url:"https://www.fromlatest.io/#/",description:"用于检查Dockerfile语法和最佳实践的Dockerfile代码检查器。",isPaid:!1,isStudentFriendly:!1},{name:"YAMLlint",url:"https://www.yamllint.com/",description:"检查YAML代码有效性并提供为Ruby优化的干净UTF-8版本的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"K8sYaml",url:"https://k8syaml.com/",description:"用于创建Kubernetes配置文件的Kubernetes YAML生成器。",isPaid:!1,isStudentFriendly:!1},{name:"Puppet Validator",url:"https://validate.puppet.com/",description:"检查Puppet代码语法有效性的工具，无需编译或强制执行目录。",isPaid:!1,isStudentFriendly:!1},{name:"ShellCheck",url:"https://www.shellcheck.net/",description:"通过分析并提供改进建议来查找shell脚本中bug的在线平台。",isPaid:!1,isStudentFriendly:!1},{name:"Beautifier.io",url:"https://beautifier.io/",description:"美化、解压或反混淆JavaScript和HTML，并使JSON/JSONP可读的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"JSONLint",url:"https://jsonlint.com/",description:"JSON验证器和重新格式化工具，为混乱的JSON代码提供整理和验证。",isPaid:!1,isStudentFriendly:!1},{name:"Algorithm Visualizer",url:"https://algorithm-visualizer.org/",description:"从代码可视化算法以帮助理解其执行的交互式平台。",isPaid:!1,isStudentFriendly:!1},{name:"CodeBeautify",url:"https://codebeautify.org/",description:"提供代码格式化工具的在线平台，包括JSON美化器、XML查看器、十六进制转换器等。",isPaid:!1,isStudentFriendly:!1},{name:"ExplainShell",url:"https://explainshell.com/",description:"通过显示每个参数的帮助文本来解释命令行命令的工具。",isPaid:!1,isStudentFriendly:!1},{name:"ShowTheDocs",url:"https://showthedocs.com/",description:"为您的代码查找相关文档的文档浏览器，使探索和理解变得更容易。",isPaid:!1,isStudentFriendly:!1},{name:"Mango - Specification Interpretation",url:"https://mango-slra1-ckwssph7iq-ue.a.run.app/readme",description:"解释规格说明并自动识别逻辑矛盾、过度复杂性和实体名称不一致等缺陷的技术。",isPaid:!1,isStudentFriendly:!1},{name:"Code Minifier",url:"https://freecodetools.org/minifier/",description:"用于压缩代码以减少文件大小和提高加载时间的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Markdown Preview",url:"https://freecodetools.org/markdown-preview/",description:"预览和格式化Markdown文本的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Code Beautifier",url:"https://freecodetools.org/beautifier/",description:"美化和格式化代码以提高可读性的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"CodePad",url:"https://codepad.org/",description:"在线编译器/解释器和协作工具，允许用户使用短URL运行和分享代码片段。",isPaid:!1,isStudentFriendly:!1},{name:"JSON Formatter",url:"https://jsonformatter.org/json-parser",description:"在线JSON解析器和格式化工具。",isPaid:!1,isStudentFriendly:!1}]},{name:"测试",originalName:"Testing",id:"testing",websites:[{name:"OWASP Fuzzing Project",url:"https://owasp.org/www-community/Fuzzing",description:"OWASP模糊测试项目的官方页面，旨在改善模糊测试工具、技术和流程的整体状态。",isPaid:!1,isStudentFriendly:!1},{name:"Awesome Fuzzing",url:"https://github.com/secfigo/Awesome-Fuzzing",description:"模糊测试资源的精选列表，包括工具、教程、研究论文等。",isPaid:!1,isStudentFriendly:!1}]},{name:"正则表达式",originalName:"Regex",id:"regex",websites:[{name:"Regex Cheat Sheet",url:"https://dev.to/emmabostian/regex-cheat-sheet-2j2a",description:"正则表达式的有用指南。",isPaid:!1,isStudentFriendly:!1},{name:"RegExr",url:"https://regexr.com/",description:"学习、构建和测试正则表达式（RegEx/RegExp）的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Regulex",url:"https://jex.im/regulex/",description:"用于理解和可视化正则表达式的JavaScript正则表达式可视化工具。",isPaid:!1,isStudentFriendly:!1},{name:"Regex101",url:"https://regex101.com/",description:"在线正则表达式测试器和调试器。",isPaid:!1,isStudentFriendly:!1},{name:"Debuggex",url:"https://www.debuggex.com/",description:"可视化正则表达式测试器，让您了解正则表达式的工作原理。",isPaid:!1,isStudentFriendly:!1},{name:"UI Bakery Regex Library",url:"https://uibakery.io/regex-library/",description:"用于UI设计的正则表达式集合。",isPaid:!1,isStudentFriendly:!1}]},{name:"无代码",originalName:"No-Code",id:"no-code",websites:[{name:"No Code List",url:"https://nocodelist.co/",description:"浏览类别以发现超过300个无代码工具。",isPaid:!1,isStudentFriendly:!1},{name:"No-Code Things",url:"https://www.spacebarcounter.net/no-code-tools",description:"无代码工具集合。",isPaid:!1,isStudentFriendly:!1}]},{name:"许可证",originalName:"Licensing",id:"licensing",websites:[{name:"ChooseALicense - Appendix",url:"https://choosealicense.com/appendix/",description:"与开源许可证相关的附加信息和资源。",isPaid:!1,isStudentFriendly:!1},{name:"GitHub Docs - Licensing a Repository",url:"https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/licensing-a-repository",description:"GitHub关于存储库许可的文档。",isPaid:!1,isStudentFriendly:!1},{name:"Public License Selector",url:"https://ufal.github.io/public-license-selector/",description:"帮助您根据偏好选择开源许可证。",isPaid:!1,isStudentFriendly:!1},{name:"Creative Commons License Chooser",url:"https://creativecommons.org/choose/",description:"选择适合您内容分享偏好的知识共享许可证。",isPaid:!1,isStudentFriendly:!1},{name:"License Buttons",url:"https://licensebuttons.net/",description:"获取指示您内容许可条款的网站按钮。",isPaid:!1,isStudentFriendly:!1},{name:"Shields.io",url:"https://shields.io/",description:"为您的GitHub存储库创建自定义徽章（盾牌），提供许可证、版本等信息。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"OlderGeeks",url:"https://oldergeeks.com/",description:"由捐赠支持的无广告软件下载网站，提供无忧体验。",isPaid:!1,isStudentFriendly:!1},{name:"AlternativeTo",url:"https://alternativeto.net/",description:"基于用户推荐发现流行软件的最佳替代方案。",isPaid:!1,isStudentFriendly:!1},{name:"Open Source Alternative",url:"https://www.opensourcealternative.to/",description:"提供专有软件开源替代方案的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Dark Mode List",url:"https://darkmodelist.com/",description:"支持暗黑模式的300个应用列表。",isPaid:!1,isStudentFriendly:!1},{name:"LocalStack",url:"https://localstack.cloud/",description:"离线开发和测试云和无服务器应用。",isPaid:!1,isStudentFriendly:!1},{name:"Markwhen",url:"https://markwhen.com/",description:"将类markdown文本转换为级联时间线的文本到时间线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Asciinema",url:"https://asciinema.org/",description:"以纯文本方式记录和分享您的终端会话。",isPaid:!1,isStudentFriendly:!1},{name:"Apache Guacamole",url:"https://guacamole.apache.org/",description:"支持VNC、RDP和SSH等协议的无客户端远程桌面网关。",isPaid:!1,isStudentFriendly:!1},{name:"DockerSwarm.rocks",url:"https://dockerswarm.rocks/",description:"使用Docker Compose文件将应用程序堆栈部署到分布式集群的生产环境。",isPaid:!1,isStudentFriendly:!1},{name:"EasyCron",url:"https://www.easycron.com/",description:"在线定时任务服务（付费）。",isPaid:!1,isStudentFriendly:!1},{name:"CDecl",url:"https://cdecl.org/",description:"将C语言难懂的声明翻译为英语，帮助您理解复杂的C声明。",isPaid:!1,isStudentFriendly:!1},{name:"NirSoft",url:"https://www.nirsoft.net/",description:"Windows小工具集合，包括系统工具、密码恢复工具等。",isPaid:!1,isStudentFriendly:!1},{name:"Ninite",url:"https://ninite.com/",description:"一次安装和更新多个程序，无工具栏或不必要的点击。",isPaid:!1,isStudentFriendly:!1},{name:"ReadWok",url:"https://app.readwok.com/lib",description:"具有渐进式查看模式的在线文本阅读器，允许逐段阅读和编辑。",isPaid:!1,isStudentFriendly:!1},{name:"BitwiseCMD",url:"https://bitwisecmd.com/",description:"在线位运算和转换。",isPaid:!1,isStudentFriendly:!1},{name:"Commands.dev",url:"https://www.commands.dev/?ref=producthunt",description:"可搜索的模板化流行终端命令目录，从互联网各处精选。",isPaid:!1,isStudentFriendly:!1},{name:"SourceForge",url:"https://sourceforge.net/",description:"基于Web的服务，允许您比较、下载和开发开源和商业软件。",isPaid:!1,isStudentFriendly:!1}]},{name:"编程语言",originalName:"Programming Languages",id:"programming-languages",subcategories:[{name:"Haskell",originalName:"Haskell",id:"haskell",websites:[{name:"Learn You a Haskell for Great Good!",url:"https://learnyouahaskell.github.io/chapters.html",description:'Miran Lipovača著的"Learn You a Haskell"在线图书版本。',isPaid:!1,isStudentFriendly:!1},{name:"Real World Haskell",url:"https://book.realworldhaskell.org/read/",description:`Bryan O'Sullivan、Don Stewart和John Goerzen著的"Real World Haskell"在线图书版本。`,isPaid:!1,isStudentFriendly:!1},{name:"CIS 194 - Introduction to Haskell (Spring 2013)",url:"https://www.seas.upenn.edu/~cis1940/spring13/lectures.html",description:"Haskell编程的讲座材料和资源。",isPaid:!1,isStudentFriendly:!1},{name:"GitHub - Programming in Haskell",url:"https://github.com/topics/programming-in-haskell",description:"GitHub上Haskell相关存储库和项目的集合。",isPaid:!1,isStudentFriendly:!1}]},{name:"Python",originalName:"Python",id:"python",websites:[{name:"Python Documentation",url:"https://docs.python.org/3/",description:"Python编程语言的官方文档。",isPaid:!1,isStudentFriendly:!1},{name:"Python Wiki",url:"https://wiki.python.org/moin/FrontPage",description:"提供Python相关信息和资源的协作平台。",isPaid:!1,isStudentFriendly:!1},{name:"Awesome Python",url:"https://awesome-python.com/",description:"精选的优秀Python框架、库、软件和资源列表。",isPaid:!1,isStudentFriendly:!1},{name:"Project Python",url:"https://projectpython.net/",description:"提供Python教程和学习开发资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Learn Python",url:"https://www.learnpython.org/",description:"提供教程和练习来学习Python编程的交互式平台。",isPaid:!1,isStudentFriendly:!1},{name:"CSCircles",url:"https://cscircles.cemc.uwaterloo.ca/",description:"滑铁卢大学开发的提供交互式Python练习和资源的在线平台。",isPaid:!1,isStudentFriendly:!1},{name:"PythonBasics",url:"https://pythonbasics.org/",description:"提供学习Python基础资源和教程的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Majyori",url:"https://www.majyori.com/",description:"为所有级别学习者提供Python教程和资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"PyFormat",url:"https://pyformat.info/",description:"比较不同版本Python格式化样式并提供示例的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Learn by Example - Python Resources",url:"https://learnbyexample.github.io/py_resources/",description:"各种主题的Python资源和教程集合。",isPaid:!1,isStudentFriendly:!1},{name:"Data to Fish",url:"https://datatofish.com/python-tutorials/",description:"提供清晰Python教程的平台，涵盖机器学习、数据库、pandas、GUI等主题。",isPaid:!1,isStudentFriendly:!1},{name:"Notion Programming Course",url:"https://www.notion.so/Programming-Course-4d4331de1a0c4ae894133cb1ca1e9315",description:"在Notion上托管的学习用Django构建基本Web应用程序的自学课程。",isPaid:!1,isStudentFriendly:!1}]},{name:"C++",originalName:"C++",id:"c--",websites:[{name:"LearnCpp.com",url:"https://www.learncpp.com/",description:"学习C++编程的综合资源。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"EbookFoundation-Free-Programming-Books",url:"https://github.com/EbookFoundation/free-programming-books/blob/main/books/free-programming-books-langs.md",description:"免费和开源编程书籍",isPaid:!1,isStudentFriendly:!1},{name:"Codecademy Catalog",url:"https://www.codecademy.com/catalog",description:"提供学习各种编程语言和技术课程目录的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Learn X in Y Minutes",url:"https://learnxinyminutes.com/",description:"提供各种编程语言快速概览的资源。",isPaid:!1,isStudentFriendly:!1},{name:"eComputerNotes",url:"https://ecomputernotes.com/",description:"在线教育学习资源，涵盖广泛的计算机科学和编程主题。",isPaid:!1,isStudentFriendly:!1},{name:"Libraries.io",url:"https://libraries.io/",description:"发现新的开源包、模块和框架，并跟踪依赖关系的平台。",isPaid:!1,isStudentFriendly:!1},{name:"LearnByExample",url:"https://www.learnbyexample.org/",description:"通过示例和实际解释学习Python、SQL和R语言的平台。",isPaid:!1,isStudentFriendly:!1},{name:"PythonTutor",url:"https://pythontutor.com/",description:"通过逐步可视化代码执行帮助用户学习Python、JavaScript、C、C++和Java编程的工具。",isPaid:!1,isStudentFriendly:!1},{name:"Classic Papers in Programming Languages and Logic",url:"https://www.cs.cmu.edu/~crary/819-f09/",description:"卡内基梅隆大学精选的编程语言和逻辑开创性学术论文集合。",isPaid:!1,isStudentFriendly:!1},{name:"RubyGems Guides",url:"https://guides.rubygems.org/",description:"学习RubyGems的工作原理以及如何制作自己的gem",isPaid:!1,isStudentFriendly:!1}]},{name:"编程练习",originalName:"Coding Practice / Competitive Programming",id:"coding-practice---competitive-programming",subcategories:[{name:"CTF",originalName:"Capture the Flag",id:"capture-the-flag",websites:[{name:"CTF101",url:"https://ctf101.org/",description:"为Capture The Flag竞赛新手提供教育资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"CTFtime",url:"https://ctftime.org/",description:"Capture The Flag事件、团队和时间线的档案。",isPaid:!1,isStudentFriendly:!1},{name:"Google CTF on GitHub",url:"https://github.com/google/google-ctf",description:"GitHub上可用的Google Capture The Flag竞赛资源。",isPaid:!1,isStudentFriendly:!1},{name:"Meusec CTF Resources",url:"https://www.meusec.com/ctf/capture-the-flags-in-cybersecurity/",description:"Meusec在网络安全领域的Capture The Flag (CTF)资源集合。",isPaid:!1,isStudentFriendly:!1},{name:"Trail of Bits - CTF Guide",url:"https://trailofbits.github.io/ctf/",description:"Trail of Bits提供参与Capture The Flag竞赛的见解和技巧的指南。",isPaid:!1,isStudentFriendly:!1}]},{name:"项目",originalName:"Projects",id:"projects",websites:[{name:"Projects-Solutions on GitHub",url:"https://github.com/karan/Projects-Solutions",description:"提供基于项目的编程挑战以通过实际应用学习编程的GitHub存储库。",isPaid:!1,isStudentFriendly:!1},{name:"Build Your Own X",url:"https://github.com/codecrafters-io/build-your-own-x#build-your-own-neural-network",description:"提供从零开始构建您喜爱技术指南的GitHub存储库。",isPaid:!1,isStudentFriendly:!1},{name:"Arduino Project Hub",url:"https://projecthub.arduino.cc/",description:"分享和发现Arduino项目的中心。",isPaid:!1,isStudentFriendly:!1},{name:"Projects in Networking",url:"https://projectsinnetworking.com/",description:"为计算机网络和安全领域的学生、毕业生和专业人士提供网络项目、网络安全项目、网络安全案例研究和源代码的资源。",isPaid:!1,isStudentFriendly:!1}]},{name:"开源",originalName:"Open Source",id:"open-source",websites:[{name:"LibHunt",url:"https://www.libhunt.com/",description:"发现趋势开源项目及其替代方案的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Awesome Open Source",url:"https://awesomeopensource.com/",description:"通过在各种类别和项目中搜索、浏览和组合主题来查找开源项目的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Open Source Libs",url:"https://opensourcelibs.com/",description:"世界最佳开源软件的大量集合。",isPaid:!1,isStudentFriendly:!1},{name:"CodeTriage",url:"https://www.codetriage.com/",description:"您可以贡献的开源存储库问题列表，如果注册可选择通过电子邮件接收问题。",isPaid:!1,isStudentFriendly:!1},{name:"GitLab Explore",url:"https://gitlab.com/explore/projects/starred/",description:"在GitLab上探索项目。",isPaid:!1,isStudentFriendly:!1},{name:"Open Source Guide",url:"https://opensource.guide/",description:"提供启动和发展开源项目资源的指南。",isPaid:!1,isStudentFriendly:!1},{name:"The Architecture of Open Source Applications",url:"https://aosabook.org/en/index.html",description:"关于各种开源软件项目设计和架构的书籍系列。",isPaid:!1,isStudentFriendly:!1},{name:"OSS Gallery",url:"https://oss.gallery/",description:"互联网上最佳开源项目的众包集合，提供发现和探索顶级软件存储库和工具的简便方法。",isPaid:!1,isStudentFriendly:!1}]},{name:"黑客马拉松",originalName:"Hackathons",id:"hackathons",websites:[{name:"DEVPOST Hackathons",url:"https://devpost.com/hackathons",description:"查找在线和线下黑客马拉松。",isPaid:!1,isStudentFriendly:!1},{name:"GitHub Education Events",url:"https://education.github.com/events",description:"在您附近的黑客马拉松、会议和活动中找到社区成员。",isPaid:!1,isStudentFriendly:!1},{name:"Hackathons by Hack Club",url:"https://hackathons.hackclub.com/",description:"精选的高中黑客马拉松列表，包含27个州和18个国家的699个活动。",isPaid:!1,isStudentFriendly:!1},{name:"Cerebral Valley AI Events",url:"https://events.cerebralvalley.ai/",description:"提供即将到来的AI相关活动、黑客马拉松和共同工作机会的信息。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"LeetCode",url:"https://leetcode.com/",description:"专注于算法挑战的编程问题练习平台。",isPaid:!1,isStudentFriendly:!1},{name:"NeetCode",url:"https://neetcode.io/",description:"通过现实项目和协作编程提升技能的编程练习网站。",isPaid:!1,isStudentFriendly:!1},{name:"HackerRank",url:"https://www.hackerrank.com/dashboard",description:"提供从算法到人工智能各领域挑战的编程平台。",isPaid:!1,isStudentFriendly:!1},{name:"CodeWars",url:"https://www.codewars.com/",description:"社区驱动的平台，通过同行评审解决方案提供编程挑战以改进技能。",isPaid:!1,isStudentFriendly:!1},{name:"Project Euler",url:"https://projecteuler.net/about",description:"以数学为导向的编程平台，通过具有挑战性的问题鼓励通过编程解决问题。",isPaid:!1,isStudentFriendly:!1},{name:"Kattis Guide",url:"https://unh-cpc.github.io/kattisguide.html",description:"使用Kattis平台解决竞技编程问题的指南，为参与者提供技巧、解题策略和资源。",isPaid:!1,isStudentFriendly:!1},{name:"Kaggle",url:"https://www.kaggle.com/",description:"举办竞赛、数据集和笔记本的数据科学平台，促进该领域的合作和创新。",isPaid:!1,isStudentFriendly:!1},{name:"Replit",url:"https://replit.com/",description:"促进协作编程的在线编程环境，具有实时分享和广泛语言支持等功能。",isPaid:!1,isStudentFriendly:!1},{name:"AlgoLeague",url:"https://www.algoleague.com/",description:"练习算法问题解决的竞技编程平台。",isPaid:!1,isStudentFriendly:!1},{name:"Codeforces",url:"https://codeforces.com/",description:"提供竞赛和大量题目集的在线竞技编程平台，吸引多样化的国际用户群体。",isPaid:!1,isStudentFriendly:!1},{name:"AtCoder",url:"https://atcoder.jp/",description:"举办竞赛和练习以增强算法技能的日本竞技编程平台。",isPaid:!1,isStudentFriendly:!1},{name:"InterviewBit",url:"https://www.interviewbit.com/coding-interview-questions/",description:"提供编程面试问题和挑战以帮助准备技术面试的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Advent of Code",url:"https://adventofcode.com/",description:"适合各种技能水平的小型编程谜题降临日历，可用任何编程语言解决。",isPaid:!1,isStudentFriendly:!1},{name:"CList",url:"https://clist.by/",description:"来自各种编程网站的竞赛列表，为即将到来的竞赛提供集中资源。",isPaid:!1,isStudentFriendly:!1},{name:"Codeply",url:"https://www.codeply.com/",description:"具有数十个框架、启动模板和超过50,000个代码片段的免费在线编辑器。",isPaid:!1,isStudentFriendly:!1},{name:"URI Online Judge",url:"https://www.urionlinejudge.com.br/judge/en/login",description:"用于训练算法和编程挑战以增强编程技能的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Rosalind",url:"https://rosalind.info/problems/locations/",description:"通过解决问题学习生物信息学和编程的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Kattis",url:"https://open.kattis.com/",description:"拥有数百个编程问题的平台，帮助用户练习和提高编程技能。",isPaid:!1,isStudentFriendly:!1},{name:"Exercism",url:"https://exercism.org/tracks",description:"通过学习、练习和指导的独特结合免费开发67种编程语言流利度的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Codility",url:"https://codility.com/programmers/challenges",description:"提供编程挑战以评估和改进算法和问题解决技能的平台。",isPaid:!1,isStudentFriendly:!1},{name:"r/dailyprogrammer",url:"https://www.reddit.com/r/dailyprogrammer/",description:"Reddit上为编程爱好者提供每日编程挑战和讨论的子版块。",isPaid:!1,isStudentFriendly:!1},{name:"Daily Coding Problem",url:"https://www.dailycodingproblem.com/",description:"向订阅者发送每日编程挑战以改进编程和问题解决技能的服务。",isPaid:!1,isStudentFriendly:!1},{name:"Coderbyte",url:"https://coderbyte.com/",description:"具有编程挑战和面试准备资源以增强编程技能的平台。",isPaid:!1,isStudentFriendly:!1},{name:"CodinGame",url:"https://www.codingame.com/start",description:"游戏化编程挑战的在线平台，使学习和练习编程更具吸引力。",isPaid:!1,isStudentFriendly:!1},{name:"A2OJ",url:"https://a2oj.netlify.app/",description:"提供结构化阶梯系统以改进问题解决技能的竞技编程资源，按难度级别和主题分类。",isPaid:!1,isStudentFriendly:!1},{name:"CodeChef",url:"https://www.codechef.com/",description:"定期举办竞赛并拥有大型题库的竞技编程网站，面向全球编程社区。",isPaid:!1,isStudentFriendly:!1},{name:"USACO",url:"http://usaco.org/index.php?page=contests",description:"美国计算奥林匹克平台，提供编程竞赛以鼓励和识别美国有才华的年轻程序员。",isPaid:!1,isStudentFriendly:!1},{name:"USACO Guide",url:"https://usaco.guide/",description:"在竞技编程中指导用户从青铜级到白金级及以上的综合免费资源集合。",isPaid:!1,isStudentFriendly:!1},{name:"JoinCPI",url:"https://joincpi.org/",description:"致力于通过资源、课程、推广和竞赛在学生中推广竞技编程的平台。",isPaid:!1,isStudentFriendly:!1},{name:"CP-Algorithms",url:"https://cp-algorithms.com/",description:"提供专门为竞技编程量身定制的算法详细指南的网站，提供深入解释和示例。",isPaid:!1,isStudentFriendly:!1},{name:"CSS Battle",url:"https://cssbattle.dev/",description:"挑战用户使用CSS技能以最小代码复制目标的平台。",isPaid:!1,isStudentFriendly:!1},{name:"JavaScript Quiz",url:"https://javascriptquiz.com/",description:"提供专注于测试和增强JavaScript编程语言知识的测验的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Elevator Saga",url:"https://play.elevatorsaga.com/",description:"学习和练习JavaScript的游戏。",isPaid:!1,isStudentFriendly:!1},{name:"Deep ML",url:"https://www.deep-ml.com/",description:"提供机器学习代码挑战的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Hack The Box",url:"https://www.hackthebox.com/",description:"提供虚拟环境和挑战以帮助个人和组织发展网络安全技能的黑客训练在线平台。",isPaid:!1,isStudentFriendly:!1}]},{name:"速查表",originalName:"Cheat Sheets",id:"cheat-sheets",subcategories:[{name:"Python速查表",originalName:"Python Cheat Sheet",id:"python-cheat-sheet",websites:[{name:"Speedsheet",url:"https://speedsheet.io/",description:"用于更快更好编程的交互式Python速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Python Cheatsheet",url:"https://www.pythoncheatsheet.org/",description:"基于《用Python自动化繁琐工作》一书和其他各种来源的速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Zero to Mastery Python Cheat Sheet",url:"https://zerotomastery.io/cheatsheets/python-cheat-sheet/",description:"Zero to Mastery提供的Python速查表。",isPaid:!1,isStudentFriendly:!1}]},{name:"前端速查表",originalName:"Front-end Cheat Sheet",id:"front-end-cheat-sheet",websites:[{name:"Can I use",url:"https://caniuse.com/",description:"为桌面和移动浏览器提供前端Web技术的最新浏览器支持表。",isPaid:!1,isStudentFriendly:!1},{name:"Easings",url:"https://easings.net/en",description:"帮助您为动画选择正确的缓动函数。",isPaid:!1,isStudentFriendly:!1},{name:"WebCode.tools",url:"https://webcode.tools/",description:"协助前端Web项目的代码生成器。",isPaid:!1,isStudentFriendly:!1},{name:"MarkSheet",url:"https://marksheet.io/",description:"提供免费的HTML和CSS教程。",isPaid:!1,isStudentFriendly:!1},{name:"Xul.fr",url:"https://www.xul.fr/en/",description:"CSS、HTML和JavaScript的教程和索引。",isPaid:!1,isStudentFriendly:!1},{name:"Emmet Cheat Sheet",url:"https://docs.emmet.io/cheat-sheet/",description:"Emmet速查表，Emmet是Web开发人员更快编写HTML和CSS代码的工具包。",isPaid:!1,isStudentFriendly:!1}]},{name:"HTML速查表",originalName:"HTML Cheat Sheet",id:"html-cheat-sheet",websites:[{name:"HTML Cheat Sheet",url:"https://htmlcheatsheet.com",description:"涵盖各种元素和属性的综合HTML速查表。",isPaid:!1,isStudentFriendly:!1},{name:"HTML5 Doctor Element Index",url:"https://html5doctor.com/element-index/",description:"HTML5中新增或重新定义元素的快速参考。",isPaid:!1,isStudentFriendly:!1},{name:"HTML5 Canvas Cheat Sheet",url:"https://simon.html5.org/dump/html5-canvas-cheat-sheet.html",description:"HTML5 Canvas速查表，提供其属性和方法的快速参考。",isPaid:!1,isStudentFriendly:!1},{name:"HTML Vocabulary",url:"https://apps.workflower.fi/vocabs/html/en#children",description:"提供HTML词汇表的资源，对元素及其关系进行分类。",isPaid:!1,isStudentFriendly:!1},{name:"HTML Reference",url:"https://htmlreference.io/",description:"免费HTML指南，包含所有HTML元素和属性的详细解释。",isPaid:!1,isStudentFriendly:!1}]},{name:"CSS速查表",originalName:"CSS Cheat Sheet",id:"css-cheat-sheet",websites:[{name:"CSS Reference",url:"https://cssreference.io",description:"CSS属性和选择器的综合参考指南。",isPaid:!1,isStudentFriendly:!1},{name:"Grid Malven",url:"https://grid.malven.co",description:"CSS Grid布局的交互式指南。",isPaid:!1,isStudentFriendly:!1},{name:"Flexbox Malven",url:"https://flexbox.malven.co/",description:"CSS Flexbox布局的交互式指南。",isPaid:!1,isStudentFriendly:!1},{name:"Justin Aguilar Animations",url:"https://www.justinaguilar.com/animations/",description:"带实时预览的CSS动画集合。",isPaid:!1,isStudentFriendly:!1},{name:"CSS Grid Cheat Sheet",url:"https://alialaa.github.io/css-grid-cheat-sheet/",description:"CSS Grid布局的可视化速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Adam Marsden CSS Cheat Sheet",url:"https://adam-marsden.co.uk/css-cheat-sheet",description:"带简洁解释和示例的CSS速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Responsive Web Design Cheat Sheet",url:"https://uxpin.s3.amazonaws.com/responsive_web_design_cheatsheet.pdf",description:"响应式Web设计原则的PDF速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Media Queries Cheat Sheet",url:"https://mac-blog.org.ua/css-3-media-queries-cheat-sheet",description:"CSS3媒体查询的速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Bootsnipp",url:"https://bootsnipp.com/",description:"为Bootstrap HTML/CSS/JS框架提供设计元素、游乐场和代码片段的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Bootstrap Cheatsheet",url:"https://hackerthemes.com/bootstrap-cheatsheet/",description:"Bootstrap框架的可视化速查表。",isPaid:!1,isStudentFriendly:!1},{name:"HackerThemes",url:"https://hackerthemes.com/",description:"基于Bootstrap框架的UI/UX资源。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"DevHints",url:"https://devhints.io",description:"各种编程语言和工具的速查表集合。",isPaid:!1,isStudentFriendly:!1},{name:"Cheatography",url:"https://cheatography.com/",description:"提供用户生成的各种主题速查表的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Sound of Sorting Algorithm Cheat Sheet",url:"https://panthema.net/2013/sound-of-sorting/SoS-CheatSheet.pdf",description:'与"排序算法之声"可视化项目相关的速查表。',isPaid:!1,isStudentFriendly:!1},{name:"SANS Cheat Sheets",url:"https://www.sans.org/blog/the-ultimate-list-of-sans-cheat-sheets/",description:"涵盖一般IT安全主题的SANS速查表终极列表。",isPaid:!1,isStudentFriendly:!1},{name:"Codecademy Cheatsheets",url:"https://www.codecademy.com/resources/cheatsheets/all",description:"各种编程语言和概念的速查表集合。",isPaid:!1,isStudentFriendly:!1},{name:"Awesome Cheatsheets",url:"https://lecoupa.github.io/awesome-cheatsheets/",description:"涵盖广泛编程语言、工具和主题的精选优秀速查表列表。",isPaid:!1,isStudentFriendly:!1},{name:"OverAPI",url:"https://overapi.com/",description:"为各种编程语言和框架提供集中访问速查表和快速参考的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Nota Language",url:"https://nota-lang.org/",description:"为浏览器设计的文档语言，提供创建基于Web文档的简化语法。",isPaid:!1,isStudentFriendly:!1},{name:"Cheat-Sheets.org",url:"https://www.cheat-sheets.org/",description:"涵盖各种主题的速查表、汇总、快速参考卡、指南和表格的编译。",isPaid:!1,isStudentFriendly:!1}]},{name:"电脑组装",originalName:"Building Computer / PC Build",id:"building-computer---pc-build",subcategories:[{name:"键盘",originalName:"Keyboard",id:"keyboard",websites:[{name:"MechanicalKeyboards",url:"https://mechanicalkeyboards.com/index.php",description:"全球最大的专用机械键盘目录，提供快速配送和售后支持。",isPaid:!1,isStudentFriendly:!1},{name:"Keycaps.info",url:"https://www.keycaps.info/",description:"键帽爱好者的资源，提供各种键帽轮廓、设计和机械键盘兼容性信息。",isPaid:!1,isStudentFriendly:!1},{name:"Keybr",url:"https://www.keybr.com/",description:"提供可定制课程来改善触摸打字技能的平台网站。",isPaid:!1,isStudentFriendly:!1},{name:"PairType",url:"https://www.pairtype.com/",description:"与伙伴实时练习触摸打字的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"KeyCombiner",url:"https://keycombiner.com/",description:"学习和练习键盘快捷键和按键组合的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Yip-Yip",url:"https://www.yip-yip.com/",description:"提供各种应用程序和程序键盘快捷键的在线工具。",isPaid:!1,isStudentFriendly:!1},{name:"Keebmaker",url:"https://keebmaker.com/",description:"创建定制机械键盘的资源。",isPaid:!1,isStudentFriendly:!1},{name:"Colemak - Learn",url:"https://colemak.com/Learn",description:"Colemak键盘布局的学习资源。",isPaid:!1,isStudentFriendly:!1}]},{name:"打字练习",originalName:"Typing Practice",id:"typing-practice",websites:[{name:"TypeFast",url:"https://typefast.io/",description:"旨在通过引人入胜的挑战和练习帮助用户提高打字速度和准确性的打字练习平台。",isPaid:!1,isStudentFriendly:!1},{name:"10FastFingers",url:"https://10fastfingers.com/typing-test/english",description:"测量英语打字速度和准确性的打字测试平台。",isPaid:!1,isStudentFriendly:!1},{name:"TypingClub",url:"https://www.typingclub.com/",description:"提供交互式打字课程和游戏以提高打字技能的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Typing.com",url:"https://www.typing.com/",description:"为所有级别学习者提供打字课程、游戏和测试的在线资源。",isPaid:!1,isStudentFriendly:!1}]},{name:"键盘快捷键",originalName:"Keyboard Shortcuts",id:"keyboard-shortcuts",websites:[{name:"UseTheKeyboard",url:"https://usethekeyboard.com/",description:"Mac应用程序、Windows程序和网站的键盘快捷键集合，涵盖广泛的常用应用程序。",isPaid:!1,isStudentFriendly:!1},{name:"ASCII Tables",url:"https://ascii-tables.com/",description:"提供ASCII表、ALT代码、Z分数表、希腊字母表、T分布表和二进制翻译器的在线资源。",isPaid:!1,isStudentFriendly:!1},{name:"ShortcutFoo",url:"https://www.shortcutfoo.com/",description:"免费练习键盘快捷键的平台，支持Mac、Windows、Linux，并为各种应用程序、语言和终端提供速查表。",isPaid:!1,isStudentFriendly:!1},{name:"Microsoft Word Keyboard Shortcuts",url:"https://support.microsoft.com/en-us/office/keyboard-shortcuts-in-word-************************************",description:"提供Microsoft Word键盘快捷键综合列表的官方Microsoft Office支持页面。",isPaid:!1,isStudentFriendly:!1}]}],websites:[{name:"PCPartPicker",url:"https://pcpartpicker.com/list/",description:"用于规划和构建定制PC的工具，让用户创建配件列表、比较价格并确保组件之间的兼容性。",isPaid:!1,isStudentFriendly:!1},{name:"PCBuilder",url:"https://pcbuilder.net/list/",description:"设计定制PC构建的平台，为用户提供组件兼容性检查、价格比较和配置选项。",isPaid:!1,isStudentFriendly:!1},{name:"PC Builds",url:"https://pc-builds.com/",description:"定制PC构建资源，提供兼容组件的精选列表、指南和各种性能级别及预算的建议。",isPaid:!1,isStudentFriendly:!1},{name:"LinearMouse",url:"https://linearmouse.app/",description:"为Mac提供高级鼠标和触控板自定义选项的实用程序，允许用户微调手势、按钮映射和滚动行为。",isPaid:!1,isStudentFriendly:!1}]},{name:"网站导航",originalName:"Other Websites of Websites",id:"other-websites-of-websites",subcategories:[],websites:[{name:"Stumbled.to",url:"https://stumbled.to/",description:"发现和分享有趣网站和内容的平台。",isPaid:!1,isStudentFriendly:!1},{name:"5000Best",url:"https://5000best.com/websites/",description:"各种类别中5000个最佳网站的精选集合。",isPaid:!1,isStudentFriendly:!1},{name:"Neal.fun",url:"https://neal.fun/",description:"具有娱乐性和交互性项目的网站。",isPaid:!1,isStudentFriendly:!1},{name:"Tennessine",url:"https://tennessine.co.uk/",description:"网络政治、数学和有趣项目的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Blakeir",url:"https://blakeir.com/blog/smart-youtube",description:"列出一些具有智慧和洞察力内容的YouTube页面的博客文章。",isPaid:!1,isStudentFriendly:!1},{name:"AwesomeOSINT on GitHub",url:"https://awesomeopensource.com/project/jivoi/awesome-osint#-pastebins",description:"包含开源情报工具和资源精选列表的GitHub存储库。",isPaid:!1,isStudentFriendly:!1},{name:"MadeWithBubble",url:"https://www.madewithbubble.xyz/",description:"发现使用Bubble开发平台创建的应用程序和网站的平台。",isPaid:!1,isStudentFriendly:!1},{name:"NoviceDock",url:"https://novicedock.com/",description:"提供学习编程和相关主题精选资源的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Hackr.io",url:"https://hackr.io/",description:"查找编程社区推荐的编程课程和教程的平台。",isPaid:!1,isStudentFriendly:!1},{name:"Limnology",url:"https://limnology.co/en",description:"20种不同语言的教育YouTube频道策划，按主题分类，可选择查找类似频道。",isPaid:!1,isStudentFriendly:!1},{name:"Tech Blogs",url:"https://tech-blogs.dev/",description:"优秀技术博客列表。",isPaid:!1,isStudentFriendly:!1},{name:"Track Awesome List",url:"https://www.trackawesomelist.com/",description:"跟踪超过500个优秀列表更新，您还可以通过RSS或新闻通讯订阅每日或每周更新。",isPaid:!1,isStudentFriendly:!1},{name:"Internet Is Fun",url:"https://projects.kwon.nyc/internet-is-fun/",description:"互联网上有趣和有意思网站的集合。",isPaid:!1,isStudentFriendly:!1},{name:"Wiby",url:"https://wiby.me/",description:"经典网络搜索引擎，旨在重现互联网早期的浏览体验，特别适合老式计算机。",isPaid:!1,isStudentFriendly:!1}]}]};function uf({title:T,description:G}){return S.jsx("header",{className:"header",children:S.jsxs("div",{className:"container",children:[S.jsx("h1",{className:"header-title",children:T}),S.jsx("p",{className:"header-description",children:G}),S.jsxs("div",{className:"header-stats",children:[S.jsx("span",{className:"stat",children:"🌟 1420+ 精选网站"}),S.jsx("span",{className:"stat",children:"📂 30+ 主要分类"}),S.jsx("span",{className:"stat",children:"🔍 智能搜索"}),S.jsx("span",{className:"stat",children:"📱 响应式设计"})]})]})})}function Bp({searchTerm:T,onSearchChange:G}){return S.jsx("div",{className:"search-bar",children:S.jsxs("div",{className:"search-input-container",children:[S.jsxs("svg",{className:"search-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[S.jsx("circle",{cx:"11",cy:"11",r:"8"}),S.jsx("path",{d:"m21 21-4.35-4.35"})]}),S.jsx("input",{type:"text",placeholder:"搜索网站名称或描述...",value:T,onChange:R=>G(R.target.value),className:"search-input"}),T&&S.jsx("button",{onClick:()=>G(""),className:"clear-button","aria-label":"清除搜索",children:S.jsxs("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[S.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),S.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})})}function Lp({data:T,searchTerm:G,selectedCategory:R}){const p=()=>T.categories.reduce((v,A)=>{const L=A.websites.length,J=A.subcategories.reduce((Ae,Me)=>Ae+Me.websites.length,0);return v+L+J},0),_=()=>T.categories.length,Y=()=>T.categories.reduce((v,A)=>v+A.subcategories.length,0),V=p(),B=_(),C=Y();return S.jsx("div",{className:"stats",children:S.jsxs("div",{className:"stats-container",children:[S.jsxs("div",{className:"stat-item",children:[S.jsx("div",{className:"stat-number",children:V}),S.jsx("div",{className:"stat-label",children:"精选网站"})]}),S.jsxs("div",{className:"stat-item",children:[S.jsx("div",{className:"stat-number",children:B}),S.jsx("div",{className:"stat-label",children:"主要分类"})]}),S.jsxs("div",{className:"stat-item",children:[S.jsx("div",{className:"stat-number",children:C}),S.jsx("div",{className:"stat-label",children:"子分类"})]}),G&&S.jsxs("div",{className:"stat-item search-result",children:[S.jsx("div",{className:"stat-number",children:V}),S.jsx("div",{className:"stat-label",children:"搜索结果"})]})]})})}function qp({categories:T,selectedCategory:G,onCategorySelect:R}){return S.jsxs("aside",{className:"category-list",children:[S.jsx("h3",{className:"category-list-title",children:"分类"}),S.jsxs("div",{className:"category-buttons",children:[S.jsx("button",{className:`category-button ${G?"":"active"}`,onClick:()=>R(null),children:"全部分类"}),T.map(p=>S.jsx("button",{className:`category-button ${G===p.id?"active":""}`,onClick:()=>R(p.id),children:p.name},p.id))]})]})}function rf({website:T,searchTerm:G,animationDelay:R=0}){const p=(V,B)=>{if(!B)return V;const C=new RegExp(`(${B})`,"gi");return V.split(C).map((A,L)=>C.test(A)?S.jsx("mark",{className:"highlight",children:A},L):A)},_=V=>{try{return`https://www.google.com/s2/favicons?domain=${new URL(V).hostname}&sz=32`}catch{return null}},Y=V=>{try{return new URL(V).hostname.replace("www.","")}catch{return V}};return S.jsxs("div",{className:"website-card",style:{animationDelay:`${R}s`},children:[S.jsxs("div",{className:"website-header",children:[S.jsxs("div",{className:"website-info",children:[S.jsx("img",{src:_(T.url),alt:"",className:"website-favicon",onError:V=>V.target.style.display="none"}),S.jsx("h4",{className:"website-name",children:p(T.name,G)})]}),S.jsxs("div",{className:"website-badges",children:[T.isPaid&&S.jsx("span",{className:"badge paid",title:"付费服务",children:"$"}),T.isStudentFriendly&&S.jsx("span",{className:"badge student",title:"学生友好",children:"@"})]})]}),S.jsx("p",{className:"website-description",children:p(T.description,G)}),S.jsxs("div",{className:"website-footer",children:[S.jsx("div",{className:"website-domain",children:Y(T.url)}),S.jsxs("a",{href:T.url,target:"_blank",rel:"noopener noreferrer",className:"website-link",children:["访问网站",S.jsxs("svg",{className:"external-link-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[S.jsx("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),S.jsx("polyline",{points:"15,3 21,3 21,9"}),S.jsx("line",{x1:"10",y1:"14",x2:"21",y2:"3"})]})]})]})]})}function Yp({data:T,searchTerm:G}){const[R,p]=Ut.useState(new Set),_=()=>{R.size===T.categories.length?p(new Set):p(new Set(T.categories.map(B=>B.id)))},Y=B=>{const C=new Set(R);C.has(B)?C.delete(B):C.add(B),p(C)},V=B=>{const C=B.websites.length,v=B.subcategories.reduce((A,L)=>A+L.websites.length,0);return C+v};return T.categories.length?S.jsxs("main",{className:"website-grid",children:[!G&&S.jsx("div",{className:"grid-controls",children:S.jsx("button",{className:"toggle-all-button",onClick:_,children:R.size===T.categories.length?"全部折叠":"全部展开"})}),T.categories.map(B=>{const C=V(B),v=R.has(B.id)||G;return S.jsxs("section",{className:"category-section",children:[S.jsxs("div",{className:"category-header",onClick:()=>!G&&Y(B.id),children:[S.jsxs("h2",{className:"category-title",children:[B.name,S.jsxs("span",{className:"website-count",children:["(",C,")"]})]}),!G&&S.jsx("button",{className:`expand-button ${v?"expanded":""}`,children:S.jsx("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:S.jsx("polyline",{points:"6,9 12,15 18,9"})})})]}),v&&S.jsxs("div",{className:"category-content",children:[B.websites.length>0&&S.jsx("div",{className:"websites-container",children:B.websites.map((A,L)=>S.jsx(rf,{website:A,searchTerm:G,animationDelay:L*.1},`${B.id}-${L}`))}),B.subcategories.map(A=>S.jsxs("div",{className:"subcategory-section",children:[S.jsx("h3",{className:"subcategory-title",children:A.name}),S.jsx("div",{className:"websites-container",children:A.websites.map((L,J)=>S.jsx(rf,{website:L,searchTerm:G,animationDelay:J*.1},`${A.id}-${J}`))})]},A.id))]})]},B.id)})]}):S.jsxs("div",{className:"no-results",children:[S.jsx("h3",{children:"未找到匹配的网站"}),S.jsx("p",{children:"尝试使用不同的搜索词或浏览其他分类"})]})}function Vp(){const T=new Date().getFullYear();return S.jsx("footer",{className:"footer",children:S.jsxs("div",{className:"container",children:[S.jsxs("div",{className:"footer-content",children:[S.jsxs("div",{className:"footer-section",children:[S.jsx("h3",{children:"Awesome Useful Websites"}),S.jsx("p",{children:"发现互联网上的隐藏宝石，探索各种有用的在线工具和资源。"})]}),S.jsxs("div",{className:"footer-section",children:[S.jsx("h4",{children:"数据来源"}),S.jsxs("ul",{children:[S.jsx("li",{children:S.jsx("a",{href:"https://www.producthunt.com/",target:"_blank",rel:"noopener noreferrer",children:"Product Hunt"})}),S.jsx("li",{children:S.jsx("a",{href:"https://www.reddit.com/r/InternetIsBeautiful/",target:"_blank",rel:"noopener noreferrer",children:"Reddit - Internet Is Beautiful"})}),S.jsx("li",{children:S.jsx("a",{href:"https://news.ycombinator.com/",target:"_blank",rel:"noopener noreferrer",children:"Hacker News"})}),S.jsx("li",{children:S.jsx("a",{href:"https://x.com/IndieRandWeb",target:"_blank",rel:"noopener noreferrer",children:"IndieRandWeb"})})]})]}),S.jsxs("div",{className:"footer-section",children:[S.jsx("h4",{children:"技术栈"}),S.jsxs("ul",{children:[S.jsx("li",{children:"React 18"}),S.jsx("li",{children:"Vite"}),S.jsx("li",{children:"CSS3"}),S.jsx("li",{children:"响应式设计"})]})]}),S.jsxs("div",{className:"footer-section",children:[S.jsx("h4",{children:"特性"}),S.jsxs("ul",{children:[S.jsx("li",{children:"🔍 智能搜索"}),S.jsx("li",{children:"📱 移动友好"}),S.jsx("li",{children:"🎨 现代设计"}),S.jsx("li",{children:"⚡ 快速加载"})]})]})]}),S.jsxs("div",{className:"footer-bottom",children:[S.jsxs("p",{children:["© ",T," Awesome Useful Websites. 基于开源数据构建。"]}),S.jsx("div",{className:"footer-links",children:S.jsx("span",{children:"Made with ❤️ for the community"})})]})]})})}function Xp(){const[T,G]=Ut.useState(!1);Ut.useEffect(()=>{const p=()=>{window.pageYOffset>300?G(!0):G(!1)};return window.addEventListener("scroll",p),()=>window.removeEventListener("scroll",p)},[]);const R=()=>{window.scrollTo({top:0,behavior:"smooth"})};return S.jsx("button",{className:`back-to-top ${T?"visible":""}`,onClick:R,"aria-label":"回到顶部",children:S.jsx("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:S.jsx("polyline",{points:"18,15 12,9 6,15"})})})}function Qp(){return S.jsxs("div",{className:"loading-container",children:[S.jsxs("div",{className:"loading-spinner",children:[S.jsx("div",{className:"spinner-ring"}),S.jsx("div",{className:"spinner-ring"}),S.jsx("div",{className:"spinner-ring"})]}),S.jsx("p",{className:"loading-text",children:"正在加载精选网站..."})]})}function Zp(){const[T,G]=Ut.useState(""),[R,p]=Ut.useState(null),[_,Y]=Ut.useState(!0);Ut.useEffect(()=>{const B=setTimeout(()=>{Y(!1)},1e3);return()=>clearTimeout(B)},[]);const V=Ut.useMemo(()=>!T&&!R?fi:{...fi,categories:fi.categories.map(C=>{if(R&&C.id!==R)return null;const v=L=>L.filter(J=>J.name.toLowerCase().includes(T.toLowerCase())||J.description.toLowerCase().includes(T.toLowerCase())),A={...C,websites:v(C.websites),subcategories:C.subcategories.map(L=>({...L,websites:v(L.websites)})).filter(L=>L.websites.length>0)};return A.websites.length>0||A.subcategories.length>0?A:null}).filter(Boolean)},[T,R]);return _?S.jsxs("div",{className:"app",children:[S.jsx(uf,{title:fi.title,description:fi.description}),S.jsx("div",{className:"container",children:S.jsx(Qp,{})})]}):S.jsxs("div",{className:"app",children:[S.jsx(uf,{title:fi.title,description:fi.description}),S.jsxs("div",{className:"container",children:[S.jsx(Bp,{searchTerm:T,onSearchChange:G}),S.jsx(Lp,{data:V,searchTerm:T,selectedCategory:R}),S.jsxs("div",{className:"main-content",children:[S.jsx(qp,{categories:fi.categories,selectedCategory:R,onCategorySelect:p}),S.jsx(Yp,{data:V,searchTerm:T})]})]}),S.jsx(Vp,{}),S.jsx(Xp,{})]})}Up.createRoot(document.getElementById("root")).render(S.jsx(Ut.StrictMode,{children:S.jsx(Zp,{})}));
