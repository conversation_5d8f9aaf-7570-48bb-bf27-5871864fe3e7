# 使用指南

## 🎯 网站功能介绍

### 主要特性
- **1420+ 精选网站**: 来自AD.md的完整网站集合
- **30个主要分类**: 涵盖工具、开发、学习、娱乐等各个领域
- **134个子分类**: 精细化分类，快速定位所需资源
- **智能搜索**: 支持网站名称和描述的实时搜索
- **折叠展开**: 大数据集优化，按需加载内容
- **响应式设计**: 完美适配桌面、平板、手机

## 🔍 如何使用

### 1. 浏览分类
- **查看统计**: 页面顶部显示总网站数、分类数等统计信息
- **分类导航**: 左侧边栏提供所有主要分类的快速导航
- **折叠展开**: 点击分类标题可以折叠/展开该分类的内容
- **全部控制**: 使用右上角的"全部展开/折叠"按钮一键操作

### 2. 搜索网站
- **实时搜索**: 在搜索框中输入关键词，结果会实时更新
- **搜索范围**: 搜索会匹配网站名称和描述内容
- **高亮显示**: 搜索结果中的关键词会被高亮标记
- **清除搜索**: 点击搜索框右侧的×按钮清除搜索

### 3. 访问网站
- **网站信息**: 每个网站卡片显示名称、描述、域名
- **特殊标记**: 
  - `$` 表示付费服务
  - `@` 表示学生友好
- **直接访问**: 点击"访问网站"按钮在新标签页打开
- **网站图标**: 自动获取网站favicon图标

### 4. 移动端使用
- **响应式布局**: 自动适配手机屏幕
- **触摸友好**: 优化的触摸交互体验
- **侧边栏**: 移动端侧边栏变为顶部分类选择

## 📂 分类说明

### 主要分类概览
1. **工具** - 各种在线工具和实用程序
2. **动手制作** - DIY项目和制作指南
3. **文化** - 文化相关资源和信息
4. **语言学习** - 语言学习和语法资源
5. **旅行** - 旅行规划和相关工具
6. **健康** - 健康、健身和生活方式
7. **音乐音频** - 音乐发现、制作和工具
8. **影视娱乐** - 电影、电视剧和娱乐内容
9. **媒体** - 社交媒体和内容平台
10. **经济** - 经济和金融信息
11. **商业** - 商业工具和资源
12. **工作求职** - 求职和职业发展
13. **创业** - 创业资源和工具
14. **艺术** - 设计、艺术和创意工具
15. **学术** - 学术研究和教育资源
16. **科学** - 科学研究和信息
17. **物理** - 物理学相关资源
18. **数学** - 数学工具和资源
19. **工程** - 各种工程领域资源
20. **计算机科学** - 计算机科学基础
21. **人工智能** - AI和机器学习工具
22. **Web开发** - 网站开发相关工具
23. **软件工程** - 软件开发和工程
24. **隐私安全** - 隐私保护和安全工具
25. **软件工具** - 各种软件和工具
26. **编程语言** - 编程语言资源
27. **编程练习** - 编程练习和竞赛
28. **速查表** - 各种技术速查表
29. **电脑组装** - 硬件和组装相关
30. **网站导航** - 其他网站导航资源

## 🔧 技术特性

### 性能优化
- **智能加载**: 分类折叠减少初始渲染负担
- **搜索优化**: 客户端实时搜索，无需服务器请求
- **图标缓存**: 网站图标通过Google服务获取并缓存
- **响应式图片**: 根据设备自动调整图片大小

### 用户体验
- **加载动画**: 优雅的加载状态提示
- **平滑动画**: 卡片淡入、按钮悬停等动画效果
- **返回顶部**: 长页面滚动时的便捷返回功能
- **键盘导航**: 支持键盘操作和无障碍访问

## 🛠️ 开发者信息

### 数据更新
- **自动解析**: 使用 `npm run generate-data` 重新解析AD.md
- **数据格式**: JSON格式，包含完整的分类和网站信息
- **中文映射**: 自动将英文分类名转换为中文

### 技术栈
- **前端**: React 18 + Vite
- **样式**: 原生CSS3，无框架依赖
- **构建**: Vite构建系统，支持热重载
- **部署**: 静态文件，可部署到任何静态托管服务

## 📱 浏览器支持

- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **移动浏览器**: iOS Safari 14+, Chrome Mobile 90+
- **功能支持**: CSS Grid, Flexbox, ES6+, Fetch API

## 🎨 自定义

### 主题颜色
- 主要渐变: `#667eea` 到 `#764ba2`
- 可在 `src/index.css` 中修改全局颜色变量

### 布局调整
- 网格布局: `src/components/WebsiteGrid.css`
- 卡片样式: `src/components/WebsiteCard.css`
- 响应式断点: 768px (平板), 480px (手机)

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 完整解析1420个网站
- ✅ 30个主要分类，134个子分类
- ✅ 中文分类名称
- ✅ 智能折叠/展开功能
- ✅ 性能优化和加载状态
- ✅ 全部展开/折叠控制

### v1.0.0 (初始版本)
- ✅ 基础网站展示
- ✅ 搜索功能
- ✅ 响应式设计
- ✅ 基础分类导航
