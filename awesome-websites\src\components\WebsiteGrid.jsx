import { useState, useMemo } from 'react'
import WebsiteCard from './WebsiteCard'
import './WebsiteGrid.css'

function WebsiteGrid({ data, searchTerm }) {
  const [expandedCategories, setExpandedCategories] = useState(new Set())

  // 全部展开/折叠
  const toggleAllCategories = () => {
    if (expandedCategories.size === data.categories.length) {
      setExpandedCategories(new Set())
    } else {
      setExpandedCategories(new Set(data.categories.map(cat => cat.id)))
    }
  }

  // 切换分类展开状态
  const toggleCategory = (categoryId) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  // 计算每个分类的网站总数
  const getCategoryWebsiteCount = (category) => {
    const directWebsites = category.websites.length
    const subcategoryWebsites = category.subcategories.reduce((total, sub) => {
      return total + sub.websites.length
    }, 0)
    return directWebsites + subcategoryWebsites
  }
  if (!data.categories.length) {
    return (
      <div className="no-results">
        <h3>未找到匹配的网站</h3>
        <p>尝试使用不同的搜索词或浏览其他分类</p>
      </div>
    )
  }

  return (
    <main className="website-grid">
      {!searchTerm && (
        <div className="grid-controls">
          <button
            className="toggle-all-button"
            onClick={toggleAllCategories}
          >
            {expandedCategories.size === data.categories.length ? '全部折叠' : '全部展开'}
          </button>
        </div>
      )}
      {data.categories.map(category => {
        const websiteCount = getCategoryWebsiteCount(category)
        const isExpanded = expandedCategories.has(category.id) || searchTerm

        return (
          <section key={category.id} className="category-section">
            <div
              className="category-header"
              onClick={() => !searchTerm && toggleCategory(category.id)}
            >
              <h2 className="category-title">
                {category.name}
                <span className="website-count">({websiteCount})</span>
              </h2>
              {!searchTerm && (
                <button className={`expand-button ${isExpanded ? 'expanded' : ''}`}>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </button>
              )}
            </div>

          {isExpanded && (
            <div className="category-content">
              {/* 分类下的直接网站 */}
              {category.websites.length > 0 && (
            <div className="websites-container">
              {category.websites.map((website, index) => (
                <WebsiteCard
                  key={`${category.id}-${index}`}
                  website={website}
                  searchTerm={searchTerm}
                  animationDelay={index * 0.1}
                />
              ))}
            </div>
          )}
          
          {/* 子分类 */}
          {category.subcategories.map(subcategory => (
            <div key={subcategory.id} className="subcategory-section">
              <h3 className="subcategory-title">{subcategory.name}</h3>
              <div className="websites-container">
                {subcategory.websites.map((website, index) => (
                  <WebsiteCard
                    key={`${subcategory.id}-${index}`}
                    website={website}
                    searchTerm={searchTerm}
                    animationDelay={index * 0.1}
                  />
                ))}
              </div>
            </div>
          ))}
            </div>
          )}
        </section>
        )
      })}
    </main>
  )
}

export default WebsiteGrid
