import WebsiteCard from './WebsiteCard'
import './WebsiteGrid.css'

function WebsiteGrid({ data, searchTerm }) {
  if (!data.categories.length) {
    return (
      <div className="no-results">
        <h3>未找到匹配的网站</h3>
        <p>尝试使用不同的搜索词或浏览其他分类</p>
      </div>
    )
  }

  return (
    <main className="website-grid">
      {data.categories.map(category => (
        <section key={category.id} className="category-section">
          <h2 className="category-title">{category.name}</h2>
          
          {/* 分类下的直接网站 */}
          {category.websites.length > 0 && (
            <div className="websites-container">
              {category.websites.map((website, index) => (
                <WebsiteCard
                  key={`${category.id}-${index}`}
                  website={website}
                  searchTerm={searchTerm}
                  animationDelay={index * 0.1}
                />
              ))}
            </div>
          )}
          
          {/* 子分类 */}
          {category.subcategories.map(subcategory => (
            <div key={subcategory.id} className="subcategory-section">
              <h3 className="subcategory-title">{subcategory.name}</h3>
              <div className="websites-container">
                {subcategory.websites.map((website, index) => (
                  <WebsiteCard
                    key={`${subcategory.id}-${index}`}
                    website={website}
                    searchTerm={searchTerm}
                    animationDelay={index * 0.1}
                  />
                ))}
              </div>
            </div>
          ))}
        </section>
      ))}
    </main>
  )
}

export default WebsiteGrid
