import './CategoryList.css'

function CategoryList({ categories, selectedCategory, onCategorySelect }) {
  return (
    <aside className="category-list">
      <h3 className="category-list-title">分类</h3>
      <div className="category-buttons">
        <button
          className={`category-button ${!selectedCategory ? 'active' : ''}`}
          onClick={() => onCategorySelect(null)}
        >
          全部分类
        </button>
        {categories.map(category => (
          <button
            key={category.id}
            className={`category-button ${selectedCategory === category.id ? 'active' : ''}`}
            onClick={() => onCategorySelect(category.id)}
          >
            {category.name}
          </button>
        ))}
      </div>
    </aside>
  )
}

export default CategoryList
