// 解析AD.md文件的脚本
import fs from 'fs';
import path from 'path';

function parseMarkdownToJson(markdownContent) {
  const lines = markdownContent.split('\n');
  const result = {
    title: "Awesome Useful Websites",
    description: "Explore the internet's hidden gems with this list of awesome useful websites!",
    categories: []
  };

  let currentCategory = null;
  let currentSubCategory = null;
  let inContentSection = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 跳过前面的介绍部分，直到找到第一个主要分类
    if (line.startsWith('## Tools')) {
      inContentSection = true;
    }
    
    if (!inContentSection) continue;

    // 主要分类 (## 开头)
    if (line.startsWith('## ') && !line.includes('Contents')) {
      const categoryName = line.replace('## ', '').trim();
      currentCategory = {
        name: categoryName,
        id: categoryName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
        subcategories: [],
        websites: []
      };
      result.categories.push(currentCategory);
      currentSubCategory = null;
    }
    
    // 子分类 (### 开头)
    else if (line.startsWith('### ')) {
      const subCategoryName = line.replace('### ', '').trim();
      currentSubCategory = {
        name: subCategoryName,
        id: subCategoryName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
        websites: []
      };
      if (currentCategory) {
        currentCategory.subcategories.push(currentSubCategory);
      }
    }
    
    // 网站条目 (- 开头，包含链接)
    else if (line.startsWith('- [') && line.includes('](')) {
      const website = parseWebsiteEntry(line);
      if (website) {
        if (currentSubCategory) {
          currentSubCategory.websites.push(website);
        } else if (currentCategory) {
          currentCategory.websites.push(website);
        }
      }
    }
  }

  return result;
}

function parseWebsiteEntry(line) {
  // 匹配格式: - [网站名](链接) - 描述
  const match = line.match(/^- \[([^\]]+)\]\(([^)]+)\)(?:\s*\(\$\)|\s*\(@\))?\s*-?\s*(.*)$/);
  
  if (!match) return null;

  const [, name, url, description] = match;
  
  // 检查特殊标记
  const isPaid = line.includes('($)');
  const isStudentFriendly = line.includes('(@)');
  
  return {
    name: name.trim(),
    url: url.trim(),
    description: description.trim(),
    isPaid,
    isStudentFriendly
  };
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  try {
    const markdownPath = path.join(process.cwd(), '..', '..', 'ad.md');
    const markdownContent = fs.readFileSync(markdownPath, 'utf-8');
    const jsonData = parseMarkdownToJson(markdownContent);
    
    const outputPath = path.join(process.cwd(), 'src', 'data', 'websites.json');
    fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2));
    
    console.log('Successfully parsed markdown and created websites.json');
    console.log(`Found ${jsonData.categories.length} categories`);
  } catch (error) {
    console.error('Error parsing markdown:', error);
  }
}

export { parseMarkdownToJson };
