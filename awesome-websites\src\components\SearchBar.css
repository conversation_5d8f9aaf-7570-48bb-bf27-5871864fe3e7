.search-bar {
  margin-top: 30px;
}

.search-input-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 50px;
  font-size: 1.1rem;
  border: none;
  border-radius: 25px;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #666;
  pointer-events: none;
}

.clear-button {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.clear-button:hover {
  background-color: #f0f0f0;
}

.clear-button svg {
  width: 16px;
  height: 16px;
  color: #666;
}

@media (max-width: 768px) {
  .search-input {
    padding: 12px 45px 12px 45px;
    font-size: 1rem;
  }
  
  .search-icon {
    left: 15px;
    width: 18px;
    height: 18px;
  }
  
  .clear-button {
    right: 12px;
  }
}
