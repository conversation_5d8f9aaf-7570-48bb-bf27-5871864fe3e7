# 部署指南

本文档介绍如何部署 Awesome Useful Websites 项目。

## 构建项目

1. 确保已安装依赖：
```bash
npm install
```

2. 构建生产版本：
```bash
npm run build
```

构建完成后，所有文件将生成在 `dist/` 目录中。

## 部署选项

### 1. Netlify 部署

1. 将项目推送到 GitHub 仓库
2. 在 [Netlify](https://netlify.com) 创建账户
3. 连接 GitHub 仓库
4. 设置构建命令：`npm run build`
5. 设置发布目录：`dist`
6. 点击部署

### 2. Vercel 部署

1. 将项目推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 创建账户
3. 导入 GitHub 仓库
4. Vercel 会自动检测 Vite 项目并配置构建设置
5. 点击部署

### 3. GitHub Pages 部署

1. 安装 gh-pages：
```bash
npm install --save-dev gh-pages
```

2. 在 `package.json` 中添加部署脚本：
```json
{
  "scripts": {
    "deploy": "gh-pages -d dist"
  },
  "homepage": "https://yourusername.github.io/awesome-websites"
}
```

3. 构建并部署：
```bash
npm run build
npm run deploy
```

### 4. 静态文件服务器

构建完成后，可以将 `dist/` 目录中的文件上传到任何静态文件服务器：

- Apache
- Nginx
- AWS S3
- Firebase Hosting
- Surge.sh

## 环境变量

项目目前不需要环境变量，所有数据都是静态的。

## 性能优化

构建版本已经包含以下优化：

- 代码分割
- 资源压缩
- CSS 优化
- 图片优化（favicon 通过 Google 服务获取）

## 域名配置

如果使用自定义域名，请确保：

1. 配置 DNS 记录指向部署服务器
2. 如果使用 HTTPS，配置 SSL 证书
3. 设置适当的缓存策略

## 监控和分析

建议添加以下服务：

- Google Analytics（网站分析）
- Google Search Console（SEO）
- 性能监控工具

## 更新部署

当有新的网站数据或功能更新时：

1. 更新 `src/data/websitesData.js`
2. 测试本地开发版本
3. 构建新版本
4. 部署到生产环境

## 故障排除

### 构建失败
- 检查 Node.js 版本（推荐 16+）
- 清除 node_modules 并重新安装
- 检查代码语法错误

### 部署后页面空白
- 检查控制台错误
- 确认资源路径正确
- 检查构建输出是否完整

### 搜索功能不工作
- 确认 JavaScript 已正确加载
- 检查浏览器兼容性
- 验证数据格式正确
